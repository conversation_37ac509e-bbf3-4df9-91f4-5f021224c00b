"use strict";Object.defineProperty(exports,"__esModule",{value:true});var Amaz$2=effect.Amaz;var AmazMgr$1=Amaz$2.AmazingManager;class LumiUtils{static isLumiTargetType(targetType){return targetType===0||targetType===1||targetType===2||targetType===3}static loadFeatureScene(featurePath){const sceneConfigPath=featurePath+"/config.json";const swingTemplateUtils=new Amaz$2.SwingTemplateUtils;const configJsonStr=swingTemplateUtils.readJsonStringFromFile(sceneConfigPath);if(configJsonStr==null||configJsonStr==""){return undefined}const jsonObj=JSON.parse(configJsonStr);if("effect"in jsonObj&&"Link"in jsonObj.effect){const Link=jsonObj.effect.Link;for(let i=0;i<Link.length;i++){const link=Link[i];if("type"in link&&link.type=="AmazingFeature"&&"path"in link){const resoutrcePath=featurePath+"/"+link.path;return AmazMgr$1.loadScene(resoutrcePath)}}}return undefined}static loadLumiHubToScene(targetScene,lumiPath){const contentPath=lumiPath+"/content.json";const swingTemplateUtils=new Amaz$2.SwingTemplateUtils;const contentJsonStr=swingTemplateUtils.readJsonStringFromFile(contentPath);if(contentJsonStr==null||contentJsonStr==""){return-204}const jsonObj=JSON.parse(contentJsonStr);if("filemap"in jsonObj&&"prefab"in jsonObj.filemap){const prefabName=jsonObj.filemap.prefab;const pm=AmazMgr$1.getSingleton("PrefabManager");const prefab=pm.loadPrefab(lumiPath,prefabName);if(prefab!==null){const lumiRoot=targetScene.addInstantiatedPrefab(prefab);if(lumiRoot){lumiRoot.name="LumiRoot";const scriptComp=lumiRoot.getComponent("ScriptComponent");if(scriptComp){swingTemplateUtils.reloadScript(scriptComp);return 0}return-201}}else{return-208}}return-201}static convertObjectForNumber(obj){const result=[];for(const effectID in obj){const effectData=obj[effectID];for(const key in effectData){result.push({effectID:effectID,key:key,value:effectData[key]})}}return result}static getTargetType(segmentType,lumiType){console.log("getTargetType: segmentType: "+segmentType+", lumiType: "+lumiType);if(segmentType=="Feature"&&lumiType=="Mask"){return 2}if(segmentType=="Video"&&lumiType=="Mask"){return 2}return 1}static getTargetName(segmentType,lumiType){if(segmentType=="Feature"&&lumiType=="Mask"){return"mask"}if(segmentType=="Video"&&lumiType=="Mask"){return"mask"}return""}}var Amaz$1=effect.Amaz;var AmazMgr=Amaz$1.AmazingManager;var SwingSingletonMgr=Amaz$1.SwingSingletonManager;class Lumi{constructor(){this.m_lumiHubScript=undefined;this.m_host=undefined;this.m_lumiRoot=undefined;this.m_enableLumiEffectCache=false;this.m_customMaskHelperIDs=undefined;this.m_effectIDToPrefabRootMap=new Map;this.m_lumiEnableFlagMap=new Map}addLumiHubToScene(targetScene,lumiHubPath){if(0==LumiUtils.loadLumiHubToScene(targetScene,lumiHubPath)){const lumiRoot=targetScene.findEntityBy("LumiRoot",null);if(lumiRoot){this.m_lumiHubScript=lumiRoot.getComponent("ScriptComponent");this.m_lumiRoot=lumiRoot;this.m_host=targetScene;this.m_host.addSystem("TextSystem");this.m_host.addSystem("TextLayerRendererSystem")}if(!this.m_lumiHubScript){console.error("addLumiHubToScene: lumiHubScript is null!");return-201}}return-201}removeLumiHub(targetScene){const lumiRoot=targetScene.findEntityBy("LumiRoot",null);if(lumiRoot){targetScene.removeEntity(lumiRoot);this.m_lumiHubScript=undefined;return 0}return-201}enableLumiEffectCache(enable){this.m_enableLumiEffectCache=enable}addLumiEffect(jsonString){console.log("addLumiEffect: lumi begin to add LumiEffect! jsonString: ",jsonString);if(!this.m_lumiHubScript){return-210}const lumiParams=JSON.parse(jsonString);if(!("type"in lumiParams)||!("children"in lumiParams)){return-204}const swingTemplateUtils=new Amaz$1.SwingTemplateUtils;const segmentType=lumiParams.type;const children=lumiParams.children;for(let i=0;i<children.length;i++){const element=children[i];if(!("lumiPath"in element)||!("lumiType"in element)||!("effectID"in element)){continue}const lumiType=element.lumiType;const configPath=element.lumiPath+"/config.json";let contentPath=element.lumiPath+"/content.json";let rootPath=element.lumiPath+"/";if(!swingTemplateUtils.isFileExist(configPath)){continue}const configStr=swingTemplateUtils.readJsonStringFromFile(configPath);const config=JSON.parse(configStr);if(!config||!("effect"in config)){continue}const effect=config.effect;if(!("Link"in effect)){continue}const links=effect.Link;if(links.length<=0||!("path"in links[0])){continue}const tempStr=links[0].path;rootPath+=tempStr;contentPath=rootPath+"/content.json";const contentStr=swingTemplateUtils.readJsonStringFromFile(contentPath);const content=JSON.parse(contentStr);if(content&&"filemap"in content&&"prefab"in content.filemap){const prefabName=content.filemap.prefab;const targetType=LumiUtils.getTargetType(segmentType,lumiType);const targetName=LumiUtils.getTargetName(segmentType,lumiType);const effectID=element.effectID;if(!LumiUtils.isLumiTargetType(targetType)){return-206}let ret=-201;if(this.m_enableLumiEffectCache){ret=this.m_lumiHubScript.call("reuseLumiEffect",[targetType,targetName,effectID]);if(ret==0){return ret}}if(this.m_host&&this.m_lumiRoot){const pm=AmazMgr.getSingleton("PrefabManager");const prefab=pm.loadPrefab(rootPath,prefabName);const prefabRoot=prefab.instantiateToEntity(this.m_host,this.m_lumiRoot,true);prefabRoot.name=effectID;const scriptComp=prefabRoot.getComponent("ScriptComponent");let ret=-201;if(scriptComp){swingTemplateUtils.reloadScript(scriptComp);ret=this.m_lumiHubScript.call("addLumiEffect",[prefabRoot,targetType,targetName,effectID]);console.log("addLumiEffect: lumi add LumiEffect ret, effectID : ",ret,effectID);if(ret==effectID){this.m_effectIDToPrefabRootMap.set(effectID,prefabRoot);console.log("addLumiEffect: lumi add LumiEffect to m_effectIDToPrefabRootMap success! effectID: ",effectID)}return ret}}}}return-201}removeLumiEffect(jsonString){var _a,_b,_c,_d,_e,_f;console.log("removeLumiEffect: lumi begin to remove LumiEffect!");if(!this.m_lumiHubScript){return-210}const lumiParams=JSON.parse(jsonString);if(!("type"in lumiParams)||!("children"in lumiParams)){return-204}const children=lumiParams.children;if(children.length===0){if(this.m_enableLumiEffectCache){return this.m_lumiHubScript.call("cacheLumiEffects",[])}else{(_a=this.m_effectIDToPrefabRootMap)===null||_a===void 0?void 0:_a.clear();(_b=this.m_lumiEnableFlagMap)===null||_b===void 0?void 0:_b.clear();return this.m_lumiHubScript.call("clearLumiEffects",[])}}for(let i=0;i<children.length;i++){const element=children[i];if(!("effectIDs"in element)){continue}const effectIDs=element.effectIDs;if(effectIDs.length===0){if(!("type"in element&&"lumiType"in element)){return-201}const segmentType=lumiParams.type;const lumiType=lumiParams.lumiType;const targetType=LumiUtils.getTargetType(segmentType,lumiType);const targetName=LumiUtils.getTargetName(segmentType,lumiType);if(this.m_enableLumiEffectCache){return this.m_lumiHubScript.call("cacheLumiEffectForTarget",[targetType,targetName])}else{(_c=this.m_effectIDToPrefabRootMap)===null||_c===void 0?void 0:_c.clear();(_d=this.m_lumiEnableFlagMap)===null||_d===void 0?void 0:_d.clear();return this.m_lumiHubScript.call("clearLumiEffectForTarget",[targetType,targetName])}}let ret=0;for(let j=0;j<effectIDs.length;j++){if(this.m_enableLumiEffectCache){ret=this.m_lumiHubScript.call("cacheLumiEffect",[effectIDs[j]])}else{console.log("lumi removeLumiEffect: remove "+effectIDs[j]+" from m_effectIDToPrefabRootMap");ret=this.m_lumiHubScript.call("removeLumiEffect",[effectIDs[j]]);if(ret==0){(_e=this.m_effectIDToPrefabRootMap)===null||_e===void 0?void 0:_e.delete(effectIDs[j]);(_f=this.m_lumiEnableFlagMap)===null||_f===void 0?void 0:_f.delete(effectIDs[j])}if(this.m_customMaskHelperIDs){const indexToRemove=this.m_customMaskHelperIDs.findIndex((item=>item===effectIDs[j]));if(indexToRemove!==-1){this.m_customMaskHelperIDs.splice(indexToRemove,1)}console.log("lumi removeLumiEffect: remove "+effectIDs[j]+" from m_customMaskHelperIDs")}}}return ret}return-201}clearAllLumiEffects(){if(this.m_lumiHubScript){return this.m_lumiHubScript.call("clearLumiEffect",[])}return-210}setLumiParams(jsonString){var _a,_b,_c,_d,_e;console.log("wjj test setLumiParams: jsonString: ",jsonString);if(!this.m_lumiHubScript){return-210}const lumiParams=JSON.parse(jsonString);if(!("effectID"in lumiParams)||!("params"in lumiParams)){return-204}const effectID=lumiParams.effectID;if("enable"in lumiParams){this.m_lumiEnableFlagMap.set(effectID,lumiParams.enable);this.m_lumiHubScript.call("enableLumiEffect",[effectID,lumiParams.enable])}const params=lumiParams.params;const lumiHelper=SwingSingletonMgr.getAmazerSingleton("SwingLumiUtils");if("ContourPoints"in params||"isClosedCurves"in params){if(this.m_customMaskHelperIDs==undefined){this.m_customMaskHelperIDs=new Array}if(!((_a=this.m_customMaskHelperIDs)===null||_a===void 0?void 0:_a.includes(effectID))){const ret=lumiHelper.createCustomMaskHelper(effectID,0);if(this.m_host&&ret==0){const rootEntity=(_b=this.m_effectIDToPrefabRootMap)===null||_b===void 0?void 0:_b.get(effectID);if(rootEntity){const renderComp=rootEntity.searchEntity("customMaskPen").getComponent("MeshRenderer");lumiHelper.bindMeshRendererToCustomMaskHelper(effectID,renderComp);this.m_customMaskHelperIDs.push(effectID)}else{return-201}}}}else if("text_params"in params){if(this.m_customMaskHelperIDs==undefined){this.m_customMaskHelperIDs=new Array}if(!((_c=this.m_customMaskHelperIDs)===null||_c===void 0?void 0:_c.includes(effectID))){const ret=lumiHelper.createCustomMaskHelper(effectID,3);if(this.m_host&&ret==0){console.log("lumi js createCustomMaskHelper for TEXT, effectID: ",effectID);const rootEntity=(_d=this.m_effectIDToPrefabRootMap)===null||_d===void 0?void 0:_d.get(effectID);if(rootEntity){const renderComp=rootEntity.searchEntity("customMaskText").getComponent("MeshRenderer");lumiHelper.bindMeshRendererToCustomMaskHelper(effectID,renderComp);this.m_customMaskHelperIDs.push(effectID)}else{return-201}}}}let ret=-201;Object.keys(params).forEach((key=>{ret=this._setLumiParams(effectID,key,params[key])}));if((_e=this.m_customMaskHelperIDs)===null||_e===void 0?void 0:_e.includes(effectID)){lumiHelper.update(0)}return ret}_setLumiParams(effectID,key,params){var _a;if(this.m_lumiHubScript==undefined){return-210}if((_a=this.m_customMaskHelperIDs)===null||_a===void 0?void 0:_a.includes(effectID)){const lumiHelper=SwingSingletonMgr.getAmazerSingleton("SwingLumiUtils");if(key==="ContourPoints"){console.log("lumi js setCustomMaskDatas: ",JSON.stringify(params));lumiHelper.setCustomMaskDatas(effectID,params);return 0}else if(key==="text_params"){Object.keys(params).forEach((_key=>{lumiHelper.setCustomMaskPoperty(effectID,_key,params[_key])}))}else{lumiHelper.setCustomMaskPoperty(effectID,key,params)}}return this.m_lumiHubScript.call("updateLumiPara",[effectID,key,params])}getLumiParams(jsonString){var _a,_b;const paramMap=new Amaz$1.Map;if(!this.m_lumiHubScript){return paramMap}const lumiParams=JSON.parse(jsonString);if(!("effectID"in lumiParams)){return paramMap}const effectID=lumiParams.effectID;paramMap.insert("effectID",effectID);if(((_a=this.m_customMaskHelperIDs)===null||_a===void 0?void 0:_a.includes(effectID))&&"key"in lumiParams&&lumiParams.key=="bbox"){const lumiHelper=SwingSingletonMgr.getAmazerSingleton("SwingLumiUtils");if(lumiHelper.getCustomMaskHelperType(effectID)===3){const bbox=lumiHelper.getCustomMaskPoperty(effectID,lumiParams.key);if(bbox){const valueVec=new Amaz$1.Vector;valueVec.pushBack(bbox.x);valueVec.pushBack(bbox.y);valueVec.pushBack(bbox.width);valueVec.pushBack(bbox.height);paramMap.insert("key",lumiParams.key);paramMap.insert("value",valueVec);return paramMap}}}if("key"in lumiParams){const key=lumiParams.key;const value=this.m_lumiHubScript.call("getLumiPara",[effectID,key]);paramMap.insert("key",key);paramMap.insert("value",value)}if("keys"in lumiParams){const keys=lumiParams.keys;const valueVec=new Amaz$1.Vector;const lumiHelper=SwingSingletonMgr.getAmazerSingleton("SwingLumiUtils");for(let i=0;i<keys.length;i++){const key=keys[i];if(((_b=this.m_customMaskHelperIDs)===null||_b===void 0?void 0:_b.includes(effectID))&&lumiHelper.getCustomMaskHelperType(effectID)===3){const value=lumiHelper.getCustomMaskPoperty(effectID,key);valueVec.pushBack(value)}else{const value=this.m_lumiHubScript.call("getLumiPara",[effectID,key]);valueVec.pushBack(value)}}paramMap.insert("keys",keys);paramMap.insert("values",valueVec)}return paramMap}setLumiParamsBatch(jsonString){if(!this.m_lumiHubScript){return-210}const lumiParams=JSON.parse(jsonString);const convertObj=LumiUtils.convertObjectForNumber(lumiParams);return this.m_lumiHubScript.call("updateLumiParaBatch",[convertObj])}setPoperty(key,value){console.log("lumi js setPoperty: ",key,value);return 0}setLumiRTType(lumiRTType){if(!this.m_lumiHubScript){return-210}return this.m_lumiHubScript.call("setLumiRTType",[lumiRTType])}isAllLumiEffectsDisable(){let isAllDisable=true;for(const[,value]of this.m_lumiEnableFlagMap){if(value==true){isAllDisable=false;break}}return isAllDisable}}var Amaz=effect.Amaz;class LumiBase{constructor(){this.m_lumi=null;this.m_resourcePath="";this.m_templateUtils=null;this.m_alphaState=1}createLumiHub(resourcePath){console.log("lumi js createLumiHub, resourcePath: ",resourcePath);if(!this.m_lumi){this.m_lumi=new Lumi}this.m_resourcePath=resourcePath;if(!this.m_templateUtils){this.m_templateUtils=new Amaz.SwingTemplateUtils}}removeLumiHub(){if(this.m_lumi){this.m_lumi=null;this.m_resourcePath="";this.m_templateUtils=null;return 0}return-201}addLumiEffect(jsonString){if(this.m_lumi){const effectID=this.m_lumi.addLumiEffect(jsonString);const params={effectID:effectID,params:{effectID:effectID}};const paramsStr=JSON.stringify(params);this.m_lumi.setLumiParams(paramsStr);return effectID}return-201}clearAllLumiEffects(){if(this.m_lumi){return this.m_lumi.clearAllLumiEffects()}return-201}removeLumiEffect(jsonString){if(this.m_lumi){return this.m_lumi.removeLumiEffect(jsonString)}return-201}setLumiParams(jsonString){if(this.m_lumi){return this.m_lumi.setLumiParams(jsonString)}return-201}getLumiPara(jsonString){if(this.m_lumi){return this.m_lumi.getLumiParams(jsonString)}return-201}setLumiParamsBatch(jsonString){if(this.m_lumi){return this.m_lumi.setLumiParamsBatch(jsonString)}return-201}setPoperty(key,value){if(this.m_lumi){return this.m_lumi.setPoperty(key,value)}return-201}setAlphaState(alphaState){this.m_alphaState=alphaState}createSceneByResources(resourceObj){if(!("relPath"in resourceObj&&"type"in resourceObj)){return undefined}if(resourceObj.type!=="AmazingFeature"||resourceObj.relPath===""){return undefined}const featurePath=this.m_resourcePath+"/"+resourceObj.relPath;const lastSubstring=featurePath.split("/").pop()||"";return this.createFeatureScene(featurePath,lastSubstring)}createFeatureScene(featurePath,name){var _a;console.log("lumi js _createFeatureScene featurePath: ",featurePath);console.log("lumi js _createFeatureScene name: ",name);const sceneConfigPath=featurePath+"/config.json";if((_a=this.m_templateUtils)===null||_a===void 0?void 0:_a.isFileExist(sceneConfigPath)){const scene=LumiUtils.loadFeatureScene(featurePath);if(scene){scene.name=name;console.log("feature.js _createFeatureScene: create feature scene success!",scene.name);return scene}else{console.error("feature.js _createFeatureScene: create feature scene fail!");return undefined}}else{console.error("feature.js _createFeatureScene: config.json not exist!");return undefined}}addLumiHubToScene(targetScene,lumi_hub){if(!("relPath"in lumi_hub&&"type"in lumi_hub)){return-204}if(lumi_hub.type!=="InfoSticker"||lumi_hub.relPath===""){return-204}const lumiHubPath=this.m_resourcePath+"/"+lumi_hub.relPath;if(this.m_lumi==null){return-201}return this.m_lumi.addLumiHubToScene(targetScene,lumiHubPath)}}class LumiVideo extends LumiBase{constructor(){super();this.m_maskScene=undefined}createLumiHub(resourcePath){var _a,_b,_c;super.createLumiHub(resourcePath);if(this.m_maskScene&&this.m_maskScene.findEntityBy("LumiRoot",null)){return 0}const jsonPath=resourcePath+"/config.json";const jsonStr=(_a=this.m_templateUtils)===null||_a===void 0?void 0:_a.readJsonStringFromFile(jsonPath);if(jsonStr==undefined){return-201}const jsonObj=JSON.parse(jsonStr);if("resources"in jsonObj){const resources=jsonObj.resources;if("featureMask"in resources){const featureMask=resources.featureMask;this.m_maskScene=this.createSceneByResources(featureMask)}this._dispatchFeatureRT();if(this.m_maskScene&&"lumi_hub"in resources){const lumi_hub=resources.lumi_hub;let ret=this.addLumiHubToScene(this.m_maskScene,lumi_hub);ret=(_b=this.m_lumi)===null||_b===void 0?void 0:_b.setLumiRTType(1);(_c=this.m_lumi)===null||_c===void 0?void 0:_c.enableLumiEffectCache(false);console.log("video.js createLumiHub: add lumi hub to scene success! ret: ",ret);return ret}}return-201}removeLumiHub(){var _a;if(this.m_maskScene){(_a=this.m_lumi)===null||_a===void 0?void 0:_a.removeLumiHub(this.m_maskScene);this.m_maskScene=undefined}return super.removeLumiHub()}setLumiHubEnable(isEnable){if(this.m_maskScene){this.m_maskScene.visible=isEnable}return 0}getLumiScenes(){if(this.m_maskScene){this.m_maskScene.config.set("lumiIndex",1);this.m_maskScene.config.set("useFaceEffect",false);this.m_maskScene.config.set("alphaState",this.m_alphaState);return[this.m_maskScene]}return[]}setLumiParams(jsonString){var _a;const ret=super.setLumiParams(jsonString);if((_a=this.m_lumi)===null||_a===void 0?void 0:_a.isAllLumiEffectsDisable()){this.setLumiHubEnable(false)}else{this.setLumiHubEnable(true)}return ret}_dispatchFeatureRT(){}}const video=LumiVideo;exports.LumiVideo=LumiVideo;exports.video=video;
//# sourceMappingURL=video.cjs.js.map
