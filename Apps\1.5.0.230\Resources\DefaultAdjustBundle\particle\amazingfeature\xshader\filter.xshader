%YAML 1.1
--- !XShader &1
name: filter_xshader
guid: {a: 17603145512193419232, b: 15600233197133209490}
renderQueue: 0
passes:
  - __class: Pass
    name: ""
    guid: {a: 8452304208693961008, b: 4357627959522429068}
    shaders:
      __class: Map
      gles2:
        - {localId: 2}
        - {localId: 3}
    semantics:
      __class: Map
      color:
        __class: VertexAttribType
        value: COLOR
      position:
        __class: VertexAttribType
        value: POSITION
      texcoord0:
        __class: VertexAttribType
        value: TEXCOORD0
    clearColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    clearDepth: 1
    clearType:
      __class: CameraClearType
      value: DONT
    renderState:
      __class: RenderState
      name: ""
      guid: {a: 3044567642376404197, b: 4212617322958395019}
      depthstencil:
        __class: DepthStencilState
        name: ""
        guid: {a: 1461137799432321397, b: 14288832661997804723}
        depthTestEnable: false
        depthTestEnableName: ""
        depthCompareOp:
          __class: CompareOp
          value: LESS
        depthCompareOpName: ""
        depthWriteEnable: true
        stencilTestEnable: false
        stencilTestEnableName: ""
    useFBOTexture: false
    useCameraRT: false
    useFBOFetch: false
    isFullScreenShading: false
    macrosMap:
      __class: Map
    preprocess: false
    passType:
      __class: PassType
      value: NORMAL
--- !Shader &2
name: ""
guid: {a: 16016791498492238213, b: 10254683055216122784}
type:
  __class: ShaderType
  value: VERTEX
sourcePath: xshader/gles2_filter.vert
--- !Shader &3
name: ""
guid: {a: 10757829758247566148, b: 10334965239674535554}
type:
  __class: ShaderType
  value: FRAGMENT
sourcePath: xshader/gles2_filter.frag
