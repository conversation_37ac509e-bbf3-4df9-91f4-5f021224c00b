[{"ClassName": "graphBuild", "Super": "ScriptComponent", "FilePath": "lua/graphBuild.lua", "FileAbsPath": "/Users/<USER>/Downloads/27a0813364ec2f63570a1ee09d59f3c8/AmazingFeature/lua/graphBuild.lua", "Properties": [{"VarName": "inputTexture", "VarType": "Texture", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Type", "RawValue": "", "Values": ["Texture"]}]}]}, {"VarName": "curTime", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 3.0]}, {"AttrType": "Slide<PERSON>", "RawValue": "", "Values": []}]}]}, {"VarName": "saturation", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 1.5]}, {"AttrType": "Slide<PERSON>", "RawValue": "", "Values": []}]}]}, {"VarName": "center", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 1.0]}, {"AttrType": "Slide<PERSON>", "RawValue": "", "Values": []}]}]}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "init", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [], "Comment": ""}, {"FuncName": "onEvent", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": ["prefabs"]}]