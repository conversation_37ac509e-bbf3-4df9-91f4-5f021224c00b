%YAML 1.1
--- !Material &1
name: filter_material
guid: {a: 14503549389630434813, b: 16355779124598519955}
xshader: {localId: 1, path: xshader/filter.xshader}
properties:
  __class: PropertySheet
  name: filter_propertySheet
  guid: {a: 8668178894555349169, b: 11412721429120913826}
  floatmap:
    __class: Map
    intensity: -1
  vec4map:
    __class: Map
  vec3map:
    __class: Map
  vec2map:
    __class: Map
  mat4map:
    __class: Map
  texmap:
    __class: Map
    inputImageTexture: {localId: 1, path: share://input.texture}
    inputImageTexture2: {localId: 1, path: image/hue_min.png}
    inputImageTexture3: {localId: 1, path: image/hue_max.png}
  intmap:
    __class: Map
renderQueue: 1
enabledMacros:
  __class: Map
mshaderPath: ""
