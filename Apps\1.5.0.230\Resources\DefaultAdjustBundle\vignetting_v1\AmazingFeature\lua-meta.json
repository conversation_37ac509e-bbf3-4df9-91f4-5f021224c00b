[{"ClassName": "SeekModeScript", "Super": "ScriptComponent", "FilePath": "lua/SeekModeScript.lua", "FileAbsPath": "/Users/<USER>/Downloads/vignetting_v1_text/AmazingFeature/lua/SeekModeScript.lua", "Properties": [{"VarName": "radius", "VarType": "Vector2f", "Comment": ""}, {"VarName": "bezier_tmp", "VarType": "Vector4f", "Comment": ""}, {"VarName": "huagan", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-1.0, 1.0]}, {"AttrType": "Slide<PERSON>", "RawValue": "", "Values": []}]}]}, {"VarName": "blendMode", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 26.0]}, {"AttrType": "Slide<PERSON>", "RawValue": "", "Values": []}, {"AttrType": "Drag", "RawValue": "", "Values": [1.0]}]}]}, {"VarName": "alphaFactor", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 1.0]}, {"AttrType": "Slide<PERSON>", "RawValue": "", "Values": []}]}]}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "constructor", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [], "Comment": ""}, {"FuncName": "seekToTime", "Params": [], "Comment": ""}, {"FuncName": "onEvent", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}]