import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qaccessible.h"
        name: "QAccessible"
        accessSemantics: "value"
        Enum {
            name: "Event"
            values: [
                "SoundPlayed",
                "Alert",
                "ForegroundChanged",
                "MenuStart",
                "MenuEnd",
                "PopupMenuStart",
                "PopupMenuEnd",
                "ContextHelpStart",
                "ContextHelpEnd",
                "DragDropStart",
                "DragDropEnd",
                "DialogStart",
                "DialogEnd",
                "ScrollingStart",
                "ScrollingEnd",
                "MenuCommand",
                "ActionChanged",
                "ActiveDescendantChanged",
                "AttributeChanged",
                "DocumentContentChanged",
                "DocumentLoadComplete",
                "DocumentLoadStopped",
                "DocumentReload",
                "HyperlinkEndIndexChanged",
                "HyperlinkNumberOfAnchorsChanged",
                "HyperlinkSelectedLinkChanged",
                "HypertextLinkActivated",
                "HypertextLinkSelected",
                "HyperlinkStartIndexChanged",
                "HypertextChanged",
                "HypertextNLinksChanged",
                "ObjectAttributeChanged",
                "PageChanged",
                "SectionChanged",
                "TableCaptionChanged",
                "TableColumnDescriptionChanged",
                "TableColumnHeaderChanged",
                "TableModelChanged",
                "TableRowDescriptionChanged",
                "TableRowHeaderChanged",
                "TableSummaryChanged",
                "TextAttributeChanged",
                "TextCaretMoved",
                "TextColumnChanged",
                "TextInserted",
                "TextRemoved",
                "TextUpdated",
                "TextSelectionChanged",
                "VisibleDataChanged",
                "ObjectCreated",
                "ObjectDestroyed",
                "ObjectShow",
                "ObjectHide",
                "ObjectReorder",
                "Focus",
                "Selection",
                "SelectionAdd",
                "SelectionRemove",
                "SelectionWithin",
                "StateChanged",
                "LocationChanged",
                "NameChanged",
                "DescriptionChanged",
                "ValueChanged",
                "ParentChanged",
                "HelpChanged",
                "DefaultActionChanged",
                "AcceleratorChanged",
                "InvalidEvent"
            ]
        }
        Enum {
            name: "Role"
            values: [
                "NoRole",
                "TitleBar",
                "MenuBar",
                "ScrollBar",
                "Grip",
                "Sound",
                "Cursor",
                "Caret",
                "AlertMessage",
                "Window",
                "Client",
                "PopupMenu",
                "MenuItem",
                "ToolTip",
                "Application",
                "Document",
                "Pane",
                "Chart",
                "Dialog",
                "Border",
                "Grouping",
                "Separator",
                "ToolBar",
                "StatusBar",
                "Table",
                "ColumnHeader",
                "RowHeader",
                "Column",
                "Row",
                "Cell",
                "Link",
                "HelpBalloon",
                "Assistant",
                "List",
                "ListItem",
                "Tree",
                "TreeItem",
                "PageTab",
                "PropertyPage",
                "Indicator",
                "Graphic",
                "StaticText",
                "EditableText",
                "Button",
                "PushButton",
                "CheckBox",
                "RadioButton",
                "ComboBox",
                "ProgressBar",
                "Dial",
                "HotkeyField",
                "Slider",
                "SpinBox",
                "Canvas",
                "Animation",
                "Equation",
                "ButtonDropDown",
                "ButtonMenu",
                "ButtonDropGrid",
                "Whitespace",
                "PageTabList",
                "Clock",
                "Splitter",
                "LayeredPane",
                "Terminal",
                "Desktop",
                "Paragraph",
                "WebDocument",
                "Section",
                "Notification",
                "ColorChooser",
                "Footer",
                "Form",
                "Heading",
                "Note",
                "ComplementaryContent",
                "UserRole"
            ]
        }
    }
    Component {
        file: "qvalidator.h"
        name: "QDoubleValidator"
        accessSemantics: "reference"
        prototype: "QValidator"
        Enum {
            name: "Notation"
            values: ["StandardNotation", "ScientificNotation"]
        }
        Property {
            name: "bottom"
            type: "double"
            read: "bottom"
            write: "setBottom"
            notify: "bottomChanged"
            index: 0
        }
        Property { name: "top"; type: "double"; read: "top"; write: "setTop"; notify: "topChanged"; index: 1 }
        Property {
            name: "decimals"
            type: "int"
            read: "decimals"
            write: "setDecimals"
            notify: "decimalsChanged"
            index: 2
        }
        Property {
            name: "notation"
            type: "Notation"
            read: "notation"
            write: "setNotation"
            notify: "notationChanged"
            index: 3
        }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "double" }
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "double" }
        }
        Signal {
            name: "decimalsChanged"
            Parameter { name: "decimals"; type: "int" }
        }
        Signal {
            name: "notationChanged"
            Parameter { name: "notation"; type: "QDoubleValidator::Notation" }
        }
    }
    Component {
        file: "qbrush.h"
        name: "QGradient"
        accessSemantics: "value"
        Enum {
            name: "Type"
            values: [
                "LinearGradient",
                "RadialGradient",
                "ConicalGradient",
                "NoGradient"
            ]
        }
        Enum {
            name: "Spread"
            values: ["PadSpread", "ReflectSpread", "RepeatSpread"]
        }
        Enum {
            name: "CoordinateMode"
            values: [
                "LogicalMode",
                "StretchToDeviceMode",
                "ObjectBoundingMode",
                "ObjectMode"
            ]
        }
        Enum {
            name: "Preset"
            values: [
                "WarmFlame",
                "NightFade",
                "SpringWarmth",
                "JuicyPeach",
                "YoungPassion",
                "LadyLips",
                "SunnyMorning",
                "RainyAshville",
                "FrozenDreams",
                "WinterNeva",
                "DustyGrass",
                "TemptingAzure",
                "HeavyRain",
                "AmyCrisp",
                "MeanFruit",
                "DeepBlue",
                "RipeMalinka",
                "CloudyKnoxville",
                "MalibuBeach",
                "NewLife",
                "TrueSunset",
                "MorpheusDen",
                "RareWind",
                "NearMoon",
                "WildApple",
                "SaintPetersburg",
                "PlumPlate",
                "EverlastingSky",
                "HappyFisher",
                "Blessing",
                "SharpeyeEagle",
                "LadogaBottom",
                "LemonGate",
                "ItmeoBranding",
                "ZeusMiracle",
                "OldHat",
                "StarWine",
                "HappyAcid",
                "AwesomePine",
                "NewYork",
                "ShyRainbow",
                "MixedHopes",
                "FlyHigh",
                "StrongBliss",
                "FreshMilk",
                "SnowAgain",
                "FebruaryInk",
                "KindSteel",
                "SoftGrass",
                "GrownEarly",
                "SharpBlues",
                "ShadyWater",
                "DirtyBeauty",
                "GreatWhale",
                "TeenNotebook",
                "PoliteRumors",
                "SweetPeriod",
                "WideMatrix",
                "SoftCherish",
                "RedSalvation",
                "BurningSpring",
                "NightParty",
                "SkyGlider",
                "HeavenPeach",
                "PurpleDivision",
                "AquaSplash",
                "SpikyNaga",
                "LoveKiss",
                "CleanMirror",
                "PremiumDark",
                "ColdEvening",
                "CochitiLake",
                "SummerGames",
                "PassionateBed",
                "MountainRock",
                "DesertHump",
                "JungleDay",
                "PhoenixStart",
                "OctoberSilence",
                "FarawayRiver",
                "AlchemistLab",
                "OverSun",
                "PremiumWhite",
                "MarsParty",
                "EternalConstance",
                "JapanBlush",
                "SmilingRain",
                "CloudyApple",
                "BigMango",
                "HealthyWater",
                "AmourAmour",
                "RiskyConcrete",
                "StrongStick",
                "ViciousStance",
                "PaloAlto",
                "HappyMemories",
                "MidnightBloom",
                "Crystalline",
                "PartyBliss",
                "ConfidentCloud",
                "LeCocktail",
                "RiverCity",
                "FrozenBerry",
                "ChildCare",
                "FlyingLemon",
                "NewRetrowave",
                "HiddenJaguar",
                "AboveTheSky",
                "Nega",
                "DenseWater",
                "Seashore",
                "MarbleWall",
                "CheerfulCaramel",
                "NightSky",
                "MagicLake",
                "YoungGrass",
                "ColorfulPeach",
                "GentleCare",
                "PlumBath",
                "HappyUnicorn",
                "AfricanField",
                "SolidStone",
                "OrangeJuice",
                "GlassWater",
                "NorthMiracle",
                "FruitBlend",
                "MillenniumPine",
                "HighFlight",
                "MoleHall",
                "SpaceShift",
                "ForestInei",
                "RoyalGarden",
                "RichMetal",
                "JuicyCake",
                "SmartIndigo",
                "SandStrike",
                "NorseBeauty",
                "AquaGuidance",
                "SunVeggie",
                "SeaLord",
                "BlackSea",
                "GrassShampoo",
                "LandingAircraft",
                "WitchDance",
                "SleeplessNight",
                "AngelCare",
                "CrystalRiver",
                "SoftLipstick",
                "SaltMountain",
                "PerfectWhite",
                "FreshOasis",
                "StrictNovember",
                "MorningSalad",
                "DeepRelief",
                "SeaStrike",
                "NightCall",
                "SupremeSky",
                "LightBlue",
                "MindCrawl",
                "LilyMeadow",
                "SugarLollipop",
                "SweetDessert",
                "MagicRay",
                "TeenParty",
                "FrozenHeat",
                "GagarinView",
                "FabledSunset",
                "PerfectBlue",
                "NumPresets"
            ]
        }
    }
    Component {
        file: "private/qquickitemsmodule_p.h"
        name: "QInputDevice"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/InputDevice 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "DeviceTypes"
            alias: "DeviceType"
            isFlag: true
            values: [
                "Unknown",
                "Mouse",
                "TouchScreen",
                "TouchPad",
                "Puck",
                "Stylus",
                "Airbrush",
                "Keyboard",
                "AllDevices"
            ]
        }
        Enum {
            name: "Capabilities"
            alias: "Capability"
            isFlag: true
            values: [
                "None",
                "Position",
                "Area",
                "Pressure",
                "Velocity",
                "NormalizedPosition",
                "MouseEmulation",
                "PixelScroll",
                "Scroll",
                "Hover",
                "Rotation",
                "XTilt",
                "YTilt",
                "TangentialPressure",
                "ZPosition",
                "All"
            ]
        }
        Property { name: "name"; type: "QString"; read: "name"; index: 0; isReadonly: true }
        Property { name: "type"; type: "DeviceType"; read: "type"; index: 1; isReadonly: true }
        Property {
            name: "capabilities"
            type: "Capabilities"
            read: "capabilities"
            index: 2
            isReadonly: true
        }
        Property { name: "systemId"; type: "qlonglong"; read: "systemId"; index: 3; isReadonly: true }
        Property { name: "seatName"; type: "QString"; read: "seatName"; index: 4; isReadonly: true }
        Property {
            name: "availableVirtualGeometry"
            type: "QRect"
            read: "availableVirtualGeometry"
            notify: "availableVirtualGeometryChanged"
            index: 5
            isReadonly: true
        }
        Signal {
            name: "availableVirtualGeometryChanged"
            Parameter { name: "area"; type: "QRect" }
        }
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QInputMethod"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/InputMethod 2.0", "QtQuick/InputMethod 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "Action"
            values: ["Click", "ContextMenu"]
        }
        Property {
            name: "cursorRectangle"
            type: "QRectF"
            read: "cursorRectangle"
            notify: "cursorRectangleChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "anchorRectangle"
            type: "QRectF"
            read: "anchorRectangle"
            notify: "anchorRectangleChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "keyboardRectangle"
            type: "QRectF"
            read: "keyboardRectangle"
            notify: "keyboardRectangleChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "inputItemClipRectangle"
            type: "QRectF"
            read: "inputItemClipRectangle"
            notify: "inputItemClipRectangleChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            notify: "visibleChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "animating"
            type: "bool"
            read: "isAnimating"
            notify: "animatingChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            notify: "localeChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "inputDirection"
            type: "Qt::LayoutDirection"
            read: "inputDirection"
            notify: "inputDirectionChanged"
            index: 7
            isReadonly: true
        }
        Signal { name: "cursorRectangleChanged" }
        Signal { name: "anchorRectangleChanged" }
        Signal { name: "keyboardRectangleChanged" }
        Signal { name: "inputItemClipRectangleChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "animatingChanged" }
        Signal { name: "localeChanged" }
        Signal {
            name: "inputDirectionChanged"
            Parameter { name: "newDirection"; type: "Qt::LayoutDirection" }
        }
        Method { name: "show" }
        Method { name: "hide" }
        Method {
            name: "update"
            Parameter { name: "queries"; type: "Qt::InputMethodQueries" }
        }
        Method { name: "reset" }
        Method { name: "commit" }
        Method {
            name: "invokeAction"
            Parameter { name: "a"; type: "Action" }
            Parameter { name: "cursorPosition"; type: "int" }
        }
    }
    Component {
        file: "qvalidator.h"
        name: "QIntValidator"
        accessSemantics: "reference"
        prototype: "QValidator"
        Property {
            name: "bottom"
            type: "int"
            read: "bottom"
            write: "setBottom"
            notify: "bottomChanged"
            index: 0
        }
        Property { name: "top"; type: "int"; read: "top"; write: "setTop"; notify: "topChanged"; index: 1 }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "int" }
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "int" }
        }
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QKeySequence"
        accessSemantics: "value"
        exports: ["QtQuick/StandardKey 2.2", "QtQuick/StandardKey 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [514, 1536]
        Enum {
            name: "StandardKey"
            values: [
                "UnknownKey",
                "HelpContents",
                "WhatsThis",
                "Open",
                "Close",
                "Save",
                "New",
                "Delete",
                "Cut",
                "Copy",
                "Paste",
                "Undo",
                "Redo",
                "Back",
                "Forward",
                "Refresh",
                "ZoomIn",
                "ZoomOut",
                "Print",
                "AddTab",
                "NextChild",
                "PreviousChild",
                "Find",
                "FindNext",
                "FindPrevious",
                "Replace",
                "SelectAll",
                "Bold",
                "Italic",
                "Underline",
                "MoveToNextChar",
                "MoveToPreviousChar",
                "MoveToNextWord",
                "MoveToPreviousWord",
                "MoveToNextLine",
                "MoveToPreviousLine",
                "MoveToNextPage",
                "MoveToPreviousPage",
                "MoveToStartOfLine",
                "MoveToEndOfLine",
                "MoveToStartOfBlock",
                "MoveToEndOfBlock",
                "MoveToStartOfDocument",
                "MoveToEndOfDocument",
                "SelectNextChar",
                "SelectPreviousChar",
                "SelectNextWord",
                "SelectPreviousWord",
                "SelectNextLine",
                "SelectPreviousLine",
                "SelectNextPage",
                "SelectPreviousPage",
                "SelectStartOfLine",
                "SelectEndOfLine",
                "SelectStartOfBlock",
                "SelectEndOfBlock",
                "SelectStartOfDocument",
                "SelectEndOfDocument",
                "DeleteStartOfWord",
                "DeleteEndOfWord",
                "DeleteEndOfLine",
                "InsertParagraphSeparator",
                "InsertLineSeparator",
                "SaveAs",
                "Preferences",
                "Quit",
                "FullScreen",
                "Deselect",
                "DeleteCompleteLine",
                "Backspace",
                "Cancel"
            ]
        }
    }
    Component {
        file: "private/qquickitemsmodule_p.h"
        name: "QPointingDevice"
        accessSemantics: "reference"
        prototype: "QInputDevice"
        exports: ["QtQuick/PointerDevice 2.12", "QtQuick/PointerDevice 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [524, 1536]
        Enum {
            name: "PointerTypes"
            alias: "PointerType"
            isFlag: true
            values: [
                "Unknown",
                "Generic",
                "Finger",
                "Pen",
                "Eraser",
                "Cursor",
                "AllPointerTypes"
            ]
        }
        Enum {
            name: "GrabTransition"
            values: [
                "GrabPassive",
                "UngrabPassive",
                "CancelGrabPassive",
                "OverrideGrabPassive",
                "GrabExclusive",
                "UngrabExclusive",
                "CancelGrabExclusive"
            ]
        }
        Property {
            name: "pointerType"
            type: "PointerType"
            read: "pointerType"
            index: 0
            isReadonly: true
        }
        Property { name: "maximumPoints"; type: "int"; read: "maximumPoints"; index: 1; isReadonly: true }
        Property { name: "buttonCount"; type: "int"; read: "buttonCount"; index: 2; isReadonly: true }
        Property {
            name: "uniqueId"
            type: "QPointingDeviceUniqueId"
            read: "uniqueId"
            index: 3
            isReadonly: true
        }
        Signal {
            name: "grabChanged"
            Parameter { name: "grabber"; type: "QObject"; isPointer: true }
            Parameter { name: "transition"; type: "GrabTransition" }
            Parameter { name: "event"; type: "const QPointerEvent"; isPointer: true }
            Parameter { name: "point"; type: "QEventPoint" }
        }
    }
    Component {
        file: "private/qquickitemsmodule_p.h"
        name: "QPointingDeviceUniqueId"
        accessSemantics: "value"
        exports: [
            "QtQuick/pointingDeviceUniqueId 2.9",
            "QtQuick/pointingDeviceUniqueId 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [521, 1536]
        Property { name: "numericId"; type: "qlonglong"; read: "numericId"; index: 0; isReadonly: true }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickAbstractAnimation"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus", "QQmlPropertyValueSource"]
        exports: [
            "QtQuick/Animation 2.0",
            "QtQuick/Animation 2.12",
            "QtQuick/Animation 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 524, 1536]
        Enum {
            name: "Loops"
            values: ["Infinite"]
        }
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "runningChanged"
            index: 0
        }
        Property {
            name: "paused"
            type: "bool"
            read: "isPaused"
            write: "setPaused"
            notify: "pausedChanged"
            index: 1
        }
        Property {
            name: "alwaysRunToEnd"
            type: "bool"
            read: "alwaysRunToEnd"
            write: "setAlwaysRunToEnd"
            notify: "alwaysRunToEndChanged"
            index: 2
        }
        Property {
            name: "loops"
            type: "int"
            read: "loops"
            write: "setLoops"
            notify: "loopCountChanged"
            index: 3
        }
        Signal { name: "started" }
        Signal { name: "stopped" }
        Signal {
            name: "runningChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "pausedChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "alwaysRunToEndChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "loopCountChanged"
            Parameter { type: "int" }
        }
        Signal { name: "finished"; revision: 524 }
        Method { name: "restart" }
        Method { name: "start" }
        Method { name: "pause" }
        Method { name: "resume" }
        Method { name: "stop" }
        Method { name: "complete" }
        Method { name: "componentFinalized" }
    }
    Component {
        file: "private/qquickaccessibleattached_p.h"
        name: "QQuickAccessibleAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "QAccessible"
        exports: [
            "QtQuick/Accessible 2.0",
            "QtQuick/Accessible 6.0",
            "QtQuick/Accessible 6.2"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536, 1538]
        attachedType: "QQuickAccessibleAttached"
        Property {
            name: "role"
            type: "QAccessible::Role"
            read: "role"
            write: "setRole"
            notify: "roleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "description"
            type: "QString"
            read: "description"
            write: "setDescription"
            notify: "descriptionChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "ignored"
            type: "bool"
            read: "ignored"
            write: "setIgnored"
            notify: "ignoredChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "checkable"
            type: "bool"
            read: "checkable"
            write: "set_checkable"
            notify: "checkableChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "checked"
            type: "bool"
            read: "checked"
            write: "set_checked"
            notify: "checkedChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "editable"
            type: "bool"
            read: "editable"
            write: "set_editable"
            notify: "editableChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "focusable"
            type: "bool"
            read: "focusable"
            write: "set_focusable"
            notify: "focusableChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "focused"
            type: "bool"
            read: "focused"
            write: "set_focused"
            notify: "focusedChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "multiLine"
            type: "bool"
            read: "multiLine"
            write: "set_multiLine"
            notify: "multiLineChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "readOnly"
            type: "bool"
            read: "readOnly"
            write: "set_readOnly"
            notify: "readOnlyChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "selected"
            type: "bool"
            read: "selected"
            write: "set_selected"
            notify: "selectedChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "selectable"
            type: "bool"
            read: "selectable"
            write: "set_selectable"
            notify: "selectableChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "pressed"
            write: "set_pressed"
            notify: "pressedChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "checkStateMixed"
            type: "bool"
            read: "checkStateMixed"
            write: "set_checkStateMixed"
            notify: "checkStateMixedChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "defaultButton"
            type: "bool"
            read: "defaultButton"
            write: "set_defaultButton"
            notify: "defaultButtonChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "passwordEdit"
            type: "bool"
            read: "passwordEdit"
            write: "set_passwordEdit"
            notify: "passwordEditChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "selectableText"
            type: "bool"
            read: "selectableText"
            write: "set_selectableText"
            notify: "selectableTextChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "searchEdit"
            type: "bool"
            read: "searchEdit"
            write: "set_searchEdit"
            notify: "searchEditChanged"
            index: 18
            isFinal: true
        }
        Signal {
            name: "checkableChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "checkedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "editableChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "focusableChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "focusedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "multiLineChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "readOnlyChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "selectedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "selectableChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "pressedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "checkStateMixedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "defaultButtonChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "passwordEditChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "selectableTextChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "searchEditChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal { name: "roleChanged" }
        Signal { name: "nameChanged" }
        Signal { name: "descriptionChanged" }
        Signal { name: "ignoredChanged" }
        Signal { name: "pressAction" }
        Signal { name: "toggleAction" }
        Signal { name: "increaseAction" }
        Signal { name: "decreaseAction" }
        Signal { name: "scrollUpAction" }
        Signal { name: "scrollDownAction" }
        Signal { name: "scrollLeftAction" }
        Signal { name: "scrollRightAction" }
        Signal { name: "previousPageAction" }
        Signal { name: "nextPageAction" }
        Method { name: "valueChanged" }
        Method { name: "cursorPositionChanged" }
        Method {
            name: "setIgnored"
            Parameter { name: "ignored"; type: "bool" }
        }
        Method {
            name: "stripHtml"
            revision: 1538
            type: "QString"
            Parameter { name: "html"; type: "QString" }
        }
    }
    Component {
        file: "private/qquickitemanimation_p.h"
        name: "QQuickAnchorAnimation"
        accessSemantics: "reference"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/AnchorAnimation 2.0",
            "QtQuick/AnchorAnimation 2.12",
            "QtQuick/AnchorAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property {
            name: "targets"
            type: "QQuickItem"
            isList: true
            read: "targets"
            index: 0
            isReadonly: true
        }
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 1
        }
        Property {
            name: "easing"
            type: "QEasingCurve"
            read: "easing"
            write: "setEasing"
            notify: "easingChanged"
            index: 2
        }
        Signal {
            name: "durationChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "easingChanged"
            Parameter { type: "QEasingCurve" }
        }
    }
    Component {
        file: "private/qquickstateoperations_p.h"
        name: "QQuickAnchorChanges"
        accessSemantics: "reference"
        prototype: "QQuickStateOperation"
        exports: ["QtQuick/AnchorChanges 2.0", "QtQuick/AnchorChanges 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "object"
            write: "setObject"
            index: 0
        }
        Property {
            name: "anchors"
            type: "QQuickAnchorSet"
            isPointer: true
            read: "anchors"
            index: 1
            isReadonly: true
        }
    }
    Component {
        file: "private/qquickanchors_p_p.h"
        name: "QQuickAnchorLine"
        accessSemantics: "value"
    }
    Component {
        file: "private/qquickstateoperations_p.h"
        name: "QQuickAnchorSet"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "left"; type: "QQmlScriptString"; read: "left"; write: "setLeft"; index: 0 }
        Property { name: "right"; type: "QQmlScriptString"; read: "right"; write: "setRight"; index: 1 }
        Property {
            name: "horizontalCenter"
            type: "QQmlScriptString"
            read: "horizontalCenter"
            write: "setHorizontalCenter"
            index: 2
        }
        Property { name: "top"; type: "QQmlScriptString"; read: "top"; write: "setTop"; index: 3 }
        Property { name: "bottom"; type: "QQmlScriptString"; read: "bottom"; write: "setBottom"; index: 4 }
        Property {
            name: "verticalCenter"
            type: "QQmlScriptString"
            read: "verticalCenter"
            write: "setVerticalCenter"
            index: 5
        }
        Property {
            name: "baseline"
            type: "QQmlScriptString"
            read: "baseline"
            write: "setBaseline"
            index: 6
        }
    }
    Component {
        file: "private/qquickanchors_p.h"
        name: "QQuickAnchors"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "Anchors"
            alias: "Anchor"
            isFlag: true
            values: [
                "InvalidAnchor",
                "LeftAnchor",
                "RightAnchor",
                "TopAnchor",
                "BottomAnchor",
                "HCenterAnchor",
                "VCenterAnchor",
                "BaselineAnchor",
                "Horizontal_Mask",
                "Vertical_Mask"
            ]
        }
        Property {
            name: "left"
            type: "QQuickAnchorLine"
            read: "left"
            write: "setLeft"
            notify: "leftChanged"
            index: 0
        }
        Property {
            name: "right"
            type: "QQuickAnchorLine"
            read: "right"
            write: "setRight"
            notify: "rightChanged"
            index: 1
        }
        Property {
            name: "horizontalCenter"
            type: "QQuickAnchorLine"
            read: "horizontalCenter"
            write: "setHorizontalCenter"
            notify: "horizontalCenterChanged"
            index: 2
        }
        Property {
            name: "top"
            type: "QQuickAnchorLine"
            read: "top"
            write: "setTop"
            notify: "topChanged"
            index: 3
        }
        Property {
            name: "bottom"
            type: "QQuickAnchorLine"
            read: "bottom"
            write: "setBottom"
            notify: "bottomChanged"
            index: 4
        }
        Property {
            name: "verticalCenter"
            type: "QQuickAnchorLine"
            read: "verticalCenter"
            write: "setVerticalCenter"
            notify: "verticalCenterChanged"
            index: 5
        }
        Property {
            name: "baseline"
            type: "QQuickAnchorLine"
            read: "baseline"
            write: "setBaseline"
            notify: "baselineChanged"
            index: 6
        }
        Property {
            name: "margins"
            type: "double"
            read: "margins"
            write: "setMargins"
            notify: "marginsChanged"
            index: 7
        }
        Property {
            name: "leftMargin"
            type: "double"
            read: "leftMargin"
            write: "setLeftMargin"
            notify: "leftMarginChanged"
            index: 8
        }
        Property {
            name: "rightMargin"
            type: "double"
            read: "rightMargin"
            write: "setRightMargin"
            notify: "rightMarginChanged"
            index: 9
        }
        Property {
            name: "horizontalCenterOffset"
            type: "double"
            read: "horizontalCenterOffset"
            write: "setHorizontalCenterOffset"
            notify: "horizontalCenterOffsetChanged"
            index: 10
        }
        Property {
            name: "topMargin"
            type: "double"
            read: "topMargin"
            write: "setTopMargin"
            notify: "topMarginChanged"
            index: 11
        }
        Property {
            name: "bottomMargin"
            type: "double"
            read: "bottomMargin"
            write: "setBottomMargin"
            notify: "bottomMarginChanged"
            index: 12
        }
        Property {
            name: "verticalCenterOffset"
            type: "double"
            read: "verticalCenterOffset"
            write: "setVerticalCenterOffset"
            notify: "verticalCenterOffsetChanged"
            index: 13
        }
        Property {
            name: "baselineOffset"
            type: "double"
            read: "baselineOffset"
            write: "setBaselineOffset"
            notify: "baselineOffsetChanged"
            index: 14
        }
        Property {
            name: "fill"
            type: "QQuickItem"
            isPointer: true
            read: "fill"
            write: "setFill"
            notify: "fillChanged"
            index: 15
        }
        Property {
            name: "centerIn"
            type: "QQuickItem"
            isPointer: true
            read: "centerIn"
            write: "setCenterIn"
            notify: "centerInChanged"
            index: 16
        }
        Property {
            name: "alignWhenCentered"
            type: "bool"
            read: "alignWhenCentered"
            write: "setAlignWhenCentered"
            notify: "centerAlignedChanged"
            index: 17
        }
        Signal { name: "leftChanged" }
        Signal { name: "rightChanged" }
        Signal { name: "topChanged" }
        Signal { name: "bottomChanged" }
        Signal { name: "verticalCenterChanged" }
        Signal { name: "horizontalCenterChanged" }
        Signal { name: "baselineChanged" }
        Signal { name: "fillChanged" }
        Signal { name: "centerInChanged" }
        Signal { name: "leftMarginChanged" }
        Signal { name: "rightMarginChanged" }
        Signal { name: "topMarginChanged" }
        Signal { name: "bottomMarginChanged" }
        Signal { name: "marginsChanged" }
        Signal { name: "verticalCenterOffsetChanged" }
        Signal { name: "horizontalCenterOffsetChanged" }
        Signal { name: "baselineOffsetChanged" }
        Signal { name: "centerAlignedChanged" }
    }
    Component {
        file: "private/qquickanimatedimage_p.h"
        name: "QQuickAnimatedImage"
        accessSemantics: "reference"
        prototype: "QQuickImage"
        exports: [
            "QtQuick/AnimatedImage 2.0",
            "QtQuick/AnimatedImage 2.1",
            "QtQuick/AnimatedImage 2.3",
            "QtQuick/AnimatedImage 2.4",
            "QtQuick/AnimatedImage 2.5",
            "QtQuick/AnimatedImage 2.7",
            "QtQuick/AnimatedImage 2.11",
            "QtQuick/AnimatedImage 2.14",
            "QtQuick/AnimatedImage 2.15",
            "QtQuick/AnimatedImage 6.0",
            "QtQuick/AnimatedImage 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            515,
            516,
            517,
            519,
            523,
            526,
            527,
            1536,
            1538
        ]
        Property {
            name: "playing"
            type: "bool"
            read: "isPlaying"
            write: "setPlaying"
            notify: "playingChanged"
            index: 0
        }
        Property {
            name: "paused"
            type: "bool"
            read: "isPaused"
            write: "setPaused"
            notify: "pausedChanged"
            index: 1
        }
        Property {
            name: "currentFrame"
            type: "int"
            read: "currentFrame"
            write: "setCurrentFrame"
            notify: "frameChanged"
            index: 2
        }
        Property {
            name: "frameCount"
            type: "int"
            read: "frameCount"
            notify: "frameCountChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "speed"
            revision: 523
            type: "double"
            read: "speed"
            write: "setSpeed"
            notify: "speedChanged"
            index: 4
        }
        Property {
            name: "sourceSize"
            type: "QSize"
            read: "sourceSize"
            notify: "sourceSizeChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "playingChanged" }
        Signal { name: "pausedChanged" }
        Signal { name: "frameChanged" }
        Signal { name: "currentFrameChanged" }
        Signal { name: "frameCountChanged" }
        Signal { name: "speedChanged"; revision: 523 }
        Method { name: "movieUpdate" }
        Method { name: "movieRequestFinished" }
        Method { name: "playingStatusChanged" }
        Method { name: "onCacheChanged" }
    }
    Component {
        file: "private/qquickanimatedsprite_p.h"
        name: "QQuickAnimatedSprite"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/AnimatedSprite 2.0",
            "QtQuick/AnimatedSprite 2.1",
            "QtQuick/AnimatedSprite 2.4",
            "QtQuick/AnimatedSprite 2.7",
            "QtQuick/AnimatedSprite 2.11",
            "QtQuick/AnimatedSprite 2.12",
            "QtQuick/AnimatedSprite 2.15",
            "QtQuick/AnimatedSprite 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 524, 527, 1536]
        Enum {
            name: "LoopParameters"
            values: ["Infinite"]
        }
        Enum {
            name: "FinishBehavior"
            values: ["FinishAtInitialFrame", "FinishAtFinalFrame"]
        }
        Property {
            name: "running"
            type: "bool"
            read: "running"
            write: "setRunning"
            notify: "runningChanged"
            index: 0
        }
        Property {
            name: "interpolate"
            type: "bool"
            read: "interpolate"
            write: "setInterpolate"
            notify: "interpolateChanged"
            index: 1
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 2
        }
        Property {
            name: "reverse"
            type: "bool"
            read: "reverse"
            write: "setReverse"
            notify: "reverseChanged"
            index: 3
        }
        Property {
            name: "frameSync"
            type: "bool"
            read: "frameSync"
            write: "setFrameSync"
            notify: "frameSyncChanged"
            index: 4
        }
        Property {
            name: "frameCount"
            type: "int"
            read: "frameCount"
            write: "setFrameCount"
            notify: "frameCountChanged"
            index: 5
        }
        Property {
            name: "frameHeight"
            type: "int"
            read: "frameHeight"
            write: "setFrameHeight"
            notify: "frameHeightChanged"
            index: 6
        }
        Property {
            name: "frameWidth"
            type: "int"
            read: "frameWidth"
            write: "setFrameWidth"
            notify: "frameWidthChanged"
            index: 7
        }
        Property {
            name: "frameX"
            type: "int"
            read: "frameX"
            write: "setFrameX"
            notify: "frameXChanged"
            index: 8
        }
        Property {
            name: "frameY"
            type: "int"
            read: "frameY"
            write: "setFrameY"
            notify: "frameYChanged"
            index: 9
        }
        Property {
            name: "frameRate"
            type: "double"
            read: "frameRate"
            write: "setFrameRate"
            notify: "frameRateChanged"
            index: 10
        }
        Property {
            name: "frameDuration"
            type: "int"
            read: "frameDuration"
            write: "setFrameDuration"
            notify: "frameDurationChanged"
            index: 11
        }
        Property {
            name: "loops"
            type: "int"
            read: "loops"
            write: "setLoops"
            notify: "loopsChanged"
            index: 12
        }
        Property {
            name: "paused"
            type: "bool"
            read: "paused"
            write: "setPaused"
            notify: "pausedChanged"
            index: 13
        }
        Property {
            name: "currentFrame"
            type: "int"
            read: "currentFrame"
            write: "setCurrentFrame"
            notify: "currentFrameChanged"
            index: 14
        }
        Property {
            name: "finishBehavior"
            revision: 527
            type: "FinishBehavior"
            read: "finishBehavior"
            write: "setFinishBehavior"
            notify: "finishBehaviorChanged"
            index: 15
        }
        Signal {
            name: "pausedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "runningChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "interpolateChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Signal {
            name: "reverseChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "frameSyncChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "frameCountChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameXChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameYChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameRateChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "frameDurationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "loopsChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "currentFrameChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "finishBehaviorChanged"
            revision: 527
            Parameter { name: "arg"; type: "FinishBehavior" }
        }
        Signal { name: "finished"; revision: 524 }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "restart" }
        Method {
            name: "advance"
            Parameter { name: "frames"; type: "int" }
        }
        Method { name: "advance" }
        Method { name: "pause" }
        Method { name: "resume" }
        Method {
            name: "setRunning"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setPaused"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setInterpolate"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setReverse"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFrameSync"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFrameCount"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameHeight"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameRate"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFrameDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method { name: "resetFrameRate" }
        Method { name: "resetFrameDuration" }
        Method {
            name: "setLoops"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setCurrentFrame"
            Parameter { name: "arg"; type: "int" }
        }
        Method { name: "createEngine" }
        Method { name: "reset" }
    }
    Component {
        file: "private/qquickanimationcontroller_p.h"
        name: "QQuickAnimationController"
        accessSemantics: "reference"
        defaultProperty: "animation"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick/AnimationController 2.0",
            "QtQuick/AnimationController 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            write: "setProgress"
            notify: "progressChanged"
            index: 0
        }
        Property {
            name: "animation"
            type: "QQuickAbstractAnimation"
            isPointer: true
            read: "animation"
            write: "setAnimation"
            notify: "animationChanged"
            index: 1
        }
        Signal { name: "progressChanged" }
        Signal { name: "animationChanged" }
        Method { name: "reload" }
        Method { name: "completeToBeginning" }
        Method { name: "completeToEnd" }
        Method { name: "componentFinalized" }
        Method { name: "updateProgress" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickAnimationGroup"
        accessSemantics: "reference"
        defaultProperty: "animations"
        prototype: "QQuickAbstractAnimation"
        Property {
            name: "animations"
            type: "QQuickAbstractAnimation"
            isList: true
            read: "animations"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickAnimator"
        accessSemantics: "reference"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/Animator 2.2",
            "QtQuick/Animator 2.12",
            "QtQuick/Animator 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [514, 524, 1536]
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "targetItem"
            write: "setTargetItem"
            notify: "targetItemChanged"
            index: 0
        }
        Property {
            name: "easing"
            type: "QEasingCurve"
            read: "easing"
            write: "setEasing"
            notify: "easingChanged"
            index: 1
        }
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 2
        }
        Property { name: "to"; type: "double"; read: "to"; write: "setTo"; notify: "toChanged"; index: 3 }
        Property {
            name: "from"
            type: "double"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 4
        }
        Signal {
            name: "targetItemChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "int" }
        }
        Signal {
            name: "easingChanged"
            Parameter { name: "curve"; type: "QEasingCurve" }
        }
        Signal {
            name: "toChanged"
            Parameter { name: "to"; type: "double" }
        }
        Signal {
            name: "fromChanged"
            Parameter { name: "from"; type: "double" }
        }
    }
    Component {
        file: "private/qquickapplication_p.h"
        name: "QQuickApplication"
        accessSemantics: "reference"
        prototype: "QQmlApplication"
        exports: ["QtQuick/Application 2.0", "QtQuick/Application 6.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "active"
            type: "bool"
            read: "active"
            notify: "activeChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "layoutDirection"
            type: "Qt::LayoutDirection"
            read: "layoutDirection"
            notify: "layoutDirectionChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "supportsMultipleWindows"
            type: "bool"
            read: "supportsMultipleWindows"
            index: 2
            isReadonly: true
        }
        Property {
            name: "state"
            type: "Qt::ApplicationState"
            read: "state"
            notify: "stateChanged"
            index: 3
            isReadonly: true
        }
        Property { name: "font"; type: "QFont"; read: "font"; index: 4; isReadonly: true }
        Property {
            name: "displayName"
            type: "QString"
            read: "displayName"
            write: "setDisplayName"
            notify: "displayNameChanged"
            index: 5
        }
        Property {
            name: "screens"
            type: "QQuickScreenInfo"
            isList: true
            read: "screens"
            notify: "screensChanged"
            index: 6
            isReadonly: true
        }
        Signal { name: "activeChanged" }
        Signal { name: "displayNameChanged" }
        Signal { name: "layoutDirectionChanged" }
        Signal {
            name: "stateChanged"
            Parameter { name: "state"; type: "Qt::ApplicationState" }
        }
        Signal { name: "screensChanged" }
        Method { name: "updateScreens" }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickBasePositioner"
        accessSemantics: "reference"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/Positioner 2.0",
            "QtQuick/Positioner 2.1",
            "QtQuick/Positioner 2.4",
            "QtQuick/Positioner 2.6",
            "QtQuick/Positioner 2.7",
            "QtQuick/Positioner 2.9",
            "QtQuick/Positioner 2.11",
            "QtQuick/Positioner 6.0",
            "QtQuick/Positioner 6.2"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            518,
            519,
            521,
            523,
            1536,
            1538
        ]
        attachedType: "QQuickPositionerAttached"
        Property {
            name: "spacing"
            type: "double"
            read: "spacing"
            write: "setSpacing"
            notify: "spacingChanged"
            index: 0
        }
        Property {
            name: "populate"
            type: "QQuickTransition"
            isPointer: true
            read: "populate"
            write: "setPopulate"
            notify: "populateChanged"
            index: 1
        }
        Property {
            name: "move"
            type: "QQuickTransition"
            isPointer: true
            read: "move"
            write: "setMove"
            notify: "moveChanged"
            index: 2
        }
        Property {
            name: "add"
            type: "QQuickTransition"
            isPointer: true
            read: "add"
            write: "setAdd"
            notify: "addChanged"
            index: 3
        }
        Property {
            name: "padding"
            revision: 518
            type: "double"
            read: "padding"
            write: "setPadding"
            notify: "paddingChanged"
            index: 4
        }
        Property {
            name: "topPadding"
            revision: 518
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            notify: "topPaddingChanged"
            index: 5
        }
        Property {
            name: "leftPadding"
            revision: 518
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            notify: "leftPaddingChanged"
            index: 6
        }
        Property {
            name: "rightPadding"
            revision: 518
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            notify: "rightPaddingChanged"
            index: 7
        }
        Property {
            name: "bottomPadding"
            revision: 518
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            notify: "bottomPaddingChanged"
            index: 8
        }
        Signal { name: "spacingChanged" }
        Signal { name: "populateChanged" }
        Signal { name: "moveChanged" }
        Signal { name: "addChanged" }
        Signal { name: "paddingChanged"; revision: 518 }
        Signal { name: "topPaddingChanged"; revision: 518 }
        Signal { name: "leftPaddingChanged"; revision: 518 }
        Signal { name: "rightPaddingChanged"; revision: 518 }
        Signal { name: "bottomPaddingChanged"; revision: 518 }
        Signal { name: "positioningComplete"; revision: 521 }
        Method { name: "prePositioning" }
        Method { name: "forceLayout"; revision: 521 }
    }
    Component {
        file: "private/qquickbehavior_p.h"
        name: "QQuickBehavior"
        accessSemantics: "reference"
        defaultProperty: "animation"
        prototype: "QObject"
        interfaces: ["QQmlPropertyValueInterceptor"]
        exports: [
            "QtQuick/Behavior 2.0",
            "QtQuick/Behavior 2.13",
            "QtQuick/Behavior 2.15",
            "QtQuick/Behavior 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 527, 1536]
        Property {
            name: "animation"
            type: "QQuickAbstractAnimation"
            isPointer: true
            read: "animation"
            write: "setAnimation"
            index: 0
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 1
        }
        Property {
            name: "targetValue"
            revision: 525
            type: "QVariant"
            read: "targetValue"
            notify: "targetValueChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "targetProperty"
            revision: 527
            type: "QQmlProperty"
            read: "targetProperty"
            notify: "targetPropertyChanged"
            index: 3
            isReadonly: true
        }
        Signal { name: "enabledChanged" }
        Signal { name: "targetValueChanged" }
        Signal { name: "targetPropertyChanged" }
        Method { name: "componentFinalized" }
    }
    Component {
        file: "private/qquickborderimage_p.h"
        name: "QQuickBorderImage"
        accessSemantics: "reference"
        prototype: "QQuickImageBase"
        exports: [
            "QtQuick/BorderImage 2.0",
            "QtQuick/BorderImage 2.1",
            "QtQuick/BorderImage 2.4",
            "QtQuick/BorderImage 2.7",
            "QtQuick/BorderImage 2.11",
            "QtQuick/BorderImage 2.14",
            "QtQuick/BorderImage 2.15",
            "QtQuick/BorderImage 6.0",
            "QtQuick/BorderImage 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            519,
            523,
            526,
            527,
            1536,
            1538
        ]
        Enum {
            name: "TileMode"
            values: ["Stretch", "Repeat", "Round"]
        }
        Property {
            name: "border"
            type: "QQuickScaleGrid"
            isPointer: true
            read: "border"
            index: 0
            isReadonly: true
        }
        Property {
            name: "horizontalTileMode"
            type: "TileMode"
            read: "horizontalTileMode"
            write: "setHorizontalTileMode"
            notify: "horizontalTileModeChanged"
            index: 1
        }
        Property {
            name: "verticalTileMode"
            type: "TileMode"
            read: "verticalTileMode"
            write: "setVerticalTileMode"
            notify: "verticalTileModeChanged"
            index: 2
        }
        Property {
            name: "sourceSize"
            type: "QSize"
            read: "sourceSize"
            notify: "sourceSizeChanged"
            index: 3
            isReadonly: true
        }
        Signal { name: "horizontalTileModeChanged" }
        Signal { name: "verticalTileModeChanged" }
        Signal { name: "sourceSizeChanged" }
        Method { name: "doUpdate" }
        Method { name: "requestFinished" }
        Method { name: "sciRequestFinished" }
    }
    Component {
        file: "private/qquickshadereffectmesh_p.h"
        name: "QQuickBorderImageMesh"
        accessSemantics: "reference"
        prototype: "QQuickShaderEffectMesh"
        exports: [
            "QtQuick/BorderImageMesh 2.8",
            "QtQuick/BorderImageMesh 6.0"
        ]
        exportMetaObjectRevisions: [520, 1536]
        Enum {
            name: "TileMode"
            values: ["Stretch", "Repeat", "Round"]
        }
        Property {
            name: "border"
            type: "QQuickScaleGrid"
            isPointer: true
            read: "border"
            index: 0
            isReadonly: true
        }
        Property {
            name: "size"
            type: "QSize"
            read: "size"
            write: "setSize"
            notify: "sizeChanged"
            index: 1
        }
        Property {
            name: "horizontalTileMode"
            type: "TileMode"
            read: "horizontalTileMode"
            write: "setHorizontalTileMode"
            notify: "horizontalTileModeChanged"
            index: 2
        }
        Property {
            name: "verticalTileMode"
            type: "TileMode"
            read: "verticalTileMode"
            write: "setVerticalTileMode"
            notify: "verticalTileModeChanged"
            index: 3
        }
        Signal { name: "sizeChanged" }
        Signal { name: "horizontalTileModeChanged" }
        Signal { name: "verticalTileModeChanged" }
    }
    Component {
        file: "private/qquickcanvasitem_p.h"
        name: "QQuickCanvasItem"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Canvas 2.0",
            "QtQuick/Canvas 2.1",
            "QtQuick/Canvas 2.4",
            "QtQuick/Canvas 2.7",
            "QtQuick/Canvas 2.11",
            "QtQuick/Canvas 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Enum {
            name: "RenderTarget"
            values: ["Image", "FramebufferObject"]
        }
        Enum {
            name: "RenderStrategy"
            values: ["Immediate", "Threaded", "Cooperative"]
        }
        Property {
            name: "available"
            type: "bool"
            read: "isAvailable"
            notify: "availableChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "contextType"
            type: "QString"
            read: "contextType"
            write: "setContextType"
            notify: "contextTypeChanged"
            index: 1
        }
        Property {
            name: "context"
            type: "QJSValue"
            read: "context"
            notify: "contextChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "canvasSize"
            type: "QSizeF"
            read: "canvasSize"
            write: "setCanvasSize"
            notify: "canvasSizeChanged"
            index: 3
        }
        Property {
            name: "tileSize"
            type: "QSize"
            read: "tileSize"
            write: "setTileSize"
            notify: "tileSizeChanged"
            index: 4
        }
        Property {
            name: "canvasWindow"
            type: "QRectF"
            read: "canvasWindow"
            write: "setCanvasWindow"
            notify: "canvasWindowChanged"
            index: 5
        }
        Property {
            name: "renderTarget"
            type: "RenderTarget"
            read: "renderTarget"
            write: "setRenderTarget"
            notify: "renderTargetChanged"
            index: 6
        }
        Property {
            name: "renderStrategy"
            type: "RenderStrategy"
            read: "renderStrategy"
            write: "setRenderStrategy"
            notify: "renderStrategyChanged"
            index: 7
        }
        Signal {
            name: "paint"
            Parameter { name: "region"; type: "QRect" }
        }
        Signal { name: "painted" }
        Signal { name: "availableChanged" }
        Signal { name: "contextTypeChanged" }
        Signal { name: "contextChanged" }
        Signal { name: "canvasSizeChanged" }
        Signal { name: "tileSizeChanged" }
        Signal { name: "canvasWindowChanged" }
        Signal { name: "renderTargetChanged" }
        Signal { name: "renderStrategyChanged" }
        Signal { name: "imageLoaded" }
        Method {
            name: "loadImage"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "unloadImage"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "isImageLoaded"
            type: "bool"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "isImageLoading"
            type: "bool"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "isImageError"
            type: "bool"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method { name: "sceneGraphInitialized" }
        Method { name: "checkAnimationCallbacks" }
        Method { name: "invalidateSceneGraph" }
        Method { name: "schedulePolish" }
        Method {
            name: "getContext"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "requestAnimationFrame"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "cancelRequestAnimationFrame"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "requestPaint" }
        Method {
            name: "markDirty"
            Parameter { name: "dirtyRect"; type: "QRectF" }
        }
        Method { name: "markDirty" }
        Method {
            name: "save"
            type: "bool"
            Parameter { name: "filename"; type: "QString" }
        }
        Method {
            name: "toDataURL"
            type: "QString"
            Parameter { name: "type"; type: "QString" }
        }
        Method { name: "toDataURL"; type: "QString" }
        Method { name: "delayedCreate" }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickCloseEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "accepted"; type: "bool"; read: "isAccepted"; write: "setAccepted"; index: 0 }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickColorAnimation"
        accessSemantics: "reference"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "QtQuick/ColorAnimation 2.0",
            "QtQuick/ColorAnimation 2.12",
            "QtQuick/ColorAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property { name: "from"; type: "QColor"; read: "from"; write: "setFrom"; index: 0 }
        Property { name: "to"; type: "QColor"; read: "to"; write: "setTo"; index: 1 }
    }
    Component {
        file: "private/qquickcolorgroup_p.h"
        name: "QQuickColorGroup"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/ColorGroup 6.0", "QtQuick/ColorGroup 6.2"]
        exportMetaObjectRevisions: [1536, 1538]
        Property {
            name: "alternateBase"
            type: "QColor"
            read: "alternateBase"
            write: "setAlternateBase"
            notify: "alternateBaseChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "base"
            type: "QColor"
            read: "base"
            write: "setBase"
            notify: "baseChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "brightText"
            type: "QColor"
            read: "brightText"
            write: "setBrightText"
            notify: "brightTextChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "button"
            type: "QColor"
            read: "button"
            write: "setButton"
            notify: "buttonChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "buttonText"
            type: "QColor"
            read: "buttonText"
            write: "setButtonText"
            notify: "buttonTextChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "dark"
            type: "QColor"
            read: "dark"
            write: "setDark"
            notify: "darkChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "highlight"
            type: "QColor"
            read: "highlight"
            write: "setHighlight"
            notify: "highlightChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "highlightedText"
            type: "QColor"
            read: "highlightedText"
            write: "setHighlightedText"
            notify: "highlightedTextChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "light"
            type: "QColor"
            read: "light"
            write: "setLight"
            notify: "lightChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "link"
            type: "QColor"
            read: "link"
            write: "setLink"
            notify: "linkChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "linkVisited"
            type: "QColor"
            read: "linkVisited"
            write: "setLinkVisited"
            notify: "linkVisitedChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "mid"
            type: "QColor"
            read: "mid"
            write: "setMid"
            notify: "midChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "midlight"
            type: "QColor"
            read: "midlight"
            write: "setMidlight"
            notify: "midlightChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "shadow"
            type: "QColor"
            read: "shadow"
            write: "setShadow"
            notify: "shadowChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "text"
            type: "QColor"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "toolTipBase"
            type: "QColor"
            read: "toolTipBase"
            write: "setToolTipBase"
            notify: "toolTipBaseChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "toolTipText"
            type: "QColor"
            read: "toolTipText"
            write: "setToolTipText"
            notify: "toolTipTextChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "window"
            type: "QColor"
            read: "window"
            write: "setWindow"
            notify: "windowChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "windowText"
            type: "QColor"
            read: "windowText"
            write: "setWindowText"
            notify: "windowTextChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "placeholderText"
            revision: 1538
            type: "QColor"
            read: "placeholderText"
            write: "setPlaceholderText"
            notify: "placeholderTextChanged"
            index: 19
            isFinal: true
        }
        Signal { name: "alternateBaseChanged" }
        Signal { name: "baseChanged" }
        Signal { name: "brightTextChanged" }
        Signal { name: "buttonChanged" }
        Signal { name: "buttonTextChanged" }
        Signal { name: "darkChanged" }
        Signal { name: "highlightChanged" }
        Signal { name: "highlightedTextChanged" }
        Signal { name: "lightChanged" }
        Signal { name: "linkChanged" }
        Signal { name: "linkVisitedChanged" }
        Signal { name: "midChanged" }
        Signal { name: "midlightChanged" }
        Signal { name: "shadowChanged" }
        Signal { name: "textChanged" }
        Signal { name: "toolTipBaseChanged" }
        Signal { name: "toolTipTextChanged" }
        Signal { name: "windowChanged" }
        Signal { name: "windowTextChanged" }
        Signal { name: "placeholderTextChanged"; revision: 1538 }
        Signal { name: "changed" }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickColorSpaceEnums"
        accessSemantics: "none"
        exports: ["QtQuick/ColorSpace 2.15", "QtQuick/ColorSpace 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [527, 1536]
        Enum {
            name: "NamedColorSpace"
            values: [
                "Unknown",
                "SRgb",
                "SRgbLinear",
                "AdobeRgb",
                "DisplayP3",
                "ProPhotoRgb"
            ]
        }
        Enum {
            name: "Primaries"
            values: ["Custom", "SRgb", "AdobeRgb", "DciP3D65", "ProPhotoRgb"]
        }
        Enum {
            name: "TransferFunction"
            values: ["Custom", "Linear", "Gamma", "SRgb", "ProPhotoRgb"]
        }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QColorSpace"
        accessSemantics: "value"
        extension: "QQuickColorSpaceValueType"
        Enum {
            name: "NamedColorSpace"
            values: [
                "SRgb",
                "SRgbLinear",
                "AdobeRgb",
                "DisplayP3",
                "ProPhotoRgb"
            ]
        }
        Enum {
            name: "Primaries"
            values: ["Custom", "SRgb", "AdobeRgb", "DciP3D65", "ProPhotoRgb"]
        }
        Enum {
            name: "TransferFunction"
            values: ["Custom", "Linear", "Gamma", "SRgb", "ProPhotoRgb"]
        }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickColorSpaceValueType"
        accessSemantics: "value"
        Property {
            name: "namedColorSpace"
            type: "QQuickColorSpaceEnums::NamedColorSpace"
            read: "namedColorSpace"
            write: "setNamedColorSpace"
            index: 0
            isFinal: true
        }
        Property {
            name: "primaries"
            type: "QQuickColorSpaceEnums::Primaries"
            read: "primaries"
            write: "setPrimaries"
            index: 1
            isFinal: true
        }
        Property {
            name: "transferFunction"
            type: "QQuickColorSpaceEnums::TransferFunction"
            read: "transferFunction"
            write: "setTransferFunction"
            index: 2
            isFinal: true
        }
        Property { name: "gamma"; type: "float"; read: "gamma"; write: "setGamma"; index: 3; isFinal: true }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QColor"
        accessSemantics: "value"
        extension: "QQuickColorValueType"
        exports: ["QtQuick/color 2.0", "QtQuick/color 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickColorValueType"
        accessSemantics: "value"
        Property { name: "r"; type: "double"; read: "r"; write: "setR"; index: 0; isFinal: true }
        Property { name: "g"; type: "double"; read: "g"; write: "setG"; index: 1; isFinal: true }
        Property { name: "b"; type: "double"; read: "b"; write: "setB"; index: 2; isFinal: true }
        Property { name: "a"; type: "double"; read: "a"; write: "setA"; index: 3; isFinal: true }
        Property {
            name: "hsvHue"
            type: "double"
            read: "hsvHue"
            write: "setHsvHue"
            index: 4
            isFinal: true
        }
        Property {
            name: "hsvSaturation"
            type: "double"
            read: "hsvSaturation"
            write: "setHsvSaturation"
            index: 5
            isFinal: true
        }
        Property {
            name: "hsvValue"
            type: "double"
            read: "hsvValue"
            write: "setHsvValue"
            index: 6
            isFinal: true
        }
        Property {
            name: "hslHue"
            type: "double"
            read: "hslHue"
            write: "setHslHue"
            index: 7
            isFinal: true
        }
        Property {
            name: "hslSaturation"
            type: "double"
            read: "hslSaturation"
            write: "setHslSaturation"
            index: 8
            isFinal: true
        }
        Property {
            name: "hslLightness"
            type: "double"
            read: "hslLightness"
            write: "setHslLightness"
            index: 9
            isFinal: true
        }
        Property { name: "valid"; type: "bool"; read: "isValid"; index: 10; isReadonly: true }
        Method { name: "toString"; type: "QString" }
        Method {
            name: "alpha"
            type: "QVariant"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "lighter"
            type: "QVariant"
            Parameter { name: "factor"; type: "double" }
        }
        Method { name: "lighter"; type: "QVariant" }
        Method {
            name: "darker"
            type: "QVariant"
            Parameter { name: "factor"; type: "double" }
        }
        Method { name: "darker"; type: "QVariant" }
        Method {
            name: "tint"
            type: "QVariant"
            Parameter { name: "factor"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickColumn"
        accessSemantics: "reference"
        prototype: "QQuickBasePositioner"
        exports: [
            "QtQuick/Column 2.0",
            "QtQuick/Column 2.1",
            "QtQuick/Column 2.4",
            "QtQuick/Column 2.6",
            "QtQuick/Column 2.7",
            "QtQuick/Column 2.9",
            "QtQuick/Column 2.11",
            "QtQuick/Column 6.0",
            "QtQuick/Column 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            518,
            519,
            521,
            523,
            1536,
            1538
        ]
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickCurve"
        accessSemantics: "reference"
        prototype: "QQuickPathElement"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; notify: "xChanged"; index: 0 }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; notify: "yChanged"; index: 1 }
        Property {
            name: "relativeX"
            type: "double"
            read: "relativeX"
            write: "setRelativeX"
            notify: "relativeXChanged"
            index: 2
        }
        Property {
            name: "relativeY"
            type: "double"
            read: "relativeY"
            write: "setRelativeY"
            notify: "relativeYChanged"
            index: 3
        }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "relativeXChanged" }
        Signal { name: "relativeYChanged" }
    }
    Component {
        file: "private/qquickvalidator_p.h"
        name: "QQuickDoubleValidator"
        accessSemantics: "reference"
        prototype: "QDoubleValidator"
        exports: [
            "QtQuick/DoubleValidator 2.0",
            "QtQuick/DoubleValidator 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "locale"
            type: "QString"
            read: "localeName"
            write: "setLocaleName"
            notify: "localeNameChanged"
            index: 0
        }
        Signal { name: "localeNameChanged" }
    }
    Component {
        file: "private/qquickdrag_p.h"
        name: "QQuickDrag"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/Drag 2.0", "QtQuick/Drag 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        attachedType: "QQuickDragAttached"
        Enum {
            name: "DragType"
            values: ["None", "Automatic", "Internal"]
        }
        Enum {
            name: "Axis"
            values: ["XAxis", "YAxis", "XAndYAxis", "XandYAxis"]
        }
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 0
        }
        Property {
            name: "axis"
            type: "Axis"
            read: "axis"
            write: "setAxis"
            notify: "axisChanged"
            index: 1
        }
        Property {
            name: "minimumX"
            type: "double"
            read: "xmin"
            write: "setXmin"
            notify: "minimumXChanged"
            index: 2
        }
        Property {
            name: "maximumX"
            type: "double"
            read: "xmax"
            write: "setXmax"
            notify: "maximumXChanged"
            index: 3
        }
        Property {
            name: "minimumY"
            type: "double"
            read: "ymin"
            write: "setYmin"
            notify: "minimumYChanged"
            index: 4
        }
        Property {
            name: "maximumY"
            type: "double"
            read: "ymax"
            write: "setYmax"
            notify: "maximumYChanged"
            index: 5
        }
        Property {
            name: "active"
            type: "bool"
            read: "active"
            notify: "activeChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "filterChildren"
            type: "bool"
            read: "filterChildren"
            write: "setFilterChildren"
            notify: "filterChildrenChanged"
            index: 7
        }
        Property {
            name: "smoothed"
            type: "bool"
            read: "smoothed"
            write: "setSmoothed"
            notify: "smoothedChanged"
            index: 8
        }
        Property {
            name: "threshold"
            type: "double"
            read: "threshold"
            write: "setThreshold"
            notify: "thresholdChanged"
            index: 9
        }
        Signal { name: "targetChanged" }
        Signal { name: "axisChanged" }
        Signal { name: "minimumXChanged" }
        Signal { name: "maximumXChanged" }
        Signal { name: "minimumYChanged" }
        Signal { name: "maximumYChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "filterChildrenChanged" }
        Signal { name: "smoothedChanged" }
        Signal { name: "thresholdChanged" }
    }
    Component {
        file: "private/qquickdrag_p.h"
        name: "QQuickDragAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "source"
            type: "QObject"
            isPointer: true
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 1
        }
        Property {
            name: "target"
            type: "QObject"
            isPointer: true
            read: "target"
            notify: "targetChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "hotSpot"
            type: "QPointF"
            read: "hotSpot"
            write: "setHotSpot"
            notify: "hotSpotChanged"
            index: 3
        }
        Property {
            name: "imageSource"
            type: "QUrl"
            read: "imageSource"
            write: "setImageSource"
            notify: "imageSourceChanged"
            index: 4
        }
        Property {
            name: "keys"
            type: "QStringList"
            read: "keys"
            write: "setKeys"
            notify: "keysChanged"
            index: 5
        }
        Property {
            name: "mimeData"
            type: "QVariantMap"
            read: "mimeData"
            write: "setMimeData"
            notify: "mimeDataChanged"
            index: 6
        }
        Property {
            name: "supportedActions"
            type: "Qt::DropActions"
            read: "supportedActions"
            write: "setSupportedActions"
            notify: "supportedActionsChanged"
            index: 7
        }
        Property {
            name: "proposedAction"
            type: "Qt::DropAction"
            read: "proposedAction"
            write: "setProposedAction"
            notify: "proposedActionChanged"
            index: 8
        }
        Property {
            name: "dragType"
            type: "QQuickDrag::DragType"
            read: "dragType"
            write: "setDragType"
            notify: "dragTypeChanged"
            index: 9
        }
        Signal { name: "dragStarted" }
        Signal {
            name: "dragFinished"
            Parameter { name: "dropAction"; type: "Qt::DropAction" }
        }
        Signal { name: "activeChanged" }
        Signal { name: "sourceChanged" }
        Signal { name: "targetChanged" }
        Signal { name: "hotSpotChanged" }
        Signal { name: "imageSourceChanged" }
        Signal { name: "keysChanged" }
        Signal { name: "mimeDataChanged" }
        Signal { name: "supportedActionsChanged" }
        Signal { name: "proposedActionChanged" }
        Signal { name: "dragTypeChanged" }
        Method {
            name: "start"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "startDrag"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "cancel" }
        Method { name: "drop"; type: "int" }
    }
    Component {
        file: "private/qquickdragaxis_p.h"
        name: "QQuickDragAxis"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/DragAxis 2.12", "QtQuick/DragAxis 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [524, 1536]
        Property {
            name: "minimum"
            type: "double"
            read: "minimum"
            write: "setMinimum"
            notify: "minimumChanged"
            index: 0
        }
        Property {
            name: "maximum"
            type: "double"
            read: "maximum"
            write: "setMaximum"
            notify: "maximumChanged"
            index: 1
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 2
        }
        Signal { name: "minimumChanged" }
        Signal { name: "maximumChanged" }
        Signal { name: "enabledChanged" }
    }
    Component {
        file: "private/qquickdroparea_p.h"
        name: "QQuickDragEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/DragEvent 2.0", "QtQuick/DragEvent 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Property { name: "x"; type: "double"; read: "x"; index: 0; isReadonly: true }
        Property { name: "y"; type: "double"; read: "y"; index: 1; isReadonly: true }
        Property {
            name: "source"
            type: "QObject"
            isPointer: true
            read: "source"
            index: 2
            isReadonly: true
        }
        Property { name: "keys"; type: "QStringList"; read: "keys"; index: 3; isReadonly: true }
        Property {
            name: "supportedActions"
            type: "Qt::DropActions"
            read: "supportedActions"
            index: 4
            isReadonly: true
        }
        Property {
            name: "proposedAction"
            type: "Qt::DropActions"
            read: "proposedAction"
            index: 5
            isReadonly: true
        }
        Property { name: "action"; type: "Qt::DropAction"; read: "action"; write: "setAction"; index: 6 }
        Property { name: "accepted"; type: "bool"; read: "accepted"; write: "setAccepted"; index: 7 }
        Property { name: "hasColor"; type: "bool"; read: "hasColor"; index: 8; isReadonly: true }
        Property { name: "hasHtml"; type: "bool"; read: "hasHtml"; index: 9; isReadonly: true }
        Property { name: "hasText"; type: "bool"; read: "hasText"; index: 10; isReadonly: true }
        Property { name: "hasUrls"; type: "bool"; read: "hasUrls"; index: 11; isReadonly: true }
        Property { name: "colorData"; type: "QVariant"; read: "colorData"; index: 12; isReadonly: true }
        Property { name: "html"; type: "QString"; read: "html"; index: 13; isReadonly: true }
        Property { name: "text"; type: "QString"; read: "text"; index: 14; isReadonly: true }
        Property { name: "urls"; type: "QList<QUrl>"; read: "urls"; index: 15; isReadonly: true }
        Property { name: "formats"; type: "QStringList"; read: "formats"; index: 16; isReadonly: true }
        Method {
            name: "getDataAsString"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "getDataAsArrayBuffer"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "acceptProposedAction"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "accept"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickdraghandler_p.h"
        name: "QQuickDragHandler"
        accessSemantics: "reference"
        prototype: "QQuickMultiPointHandler"
        exports: [
            "QtQuick/DragHandler 2.12",
            "QtQuick/DragHandler 2.14",
            "QtQuick/DragHandler 2.15",
            "QtQuick/DragHandler 6.0",
            "QtQuick/DragHandler 6.2"
        ]
        exportMetaObjectRevisions: [524, 526, 527, 1536, 1538]
        Enum {
            name: "SnapMode"
            values: [
                "NoSnap",
                "SnapAuto",
                "SnapIfPressedOutsideTarget",
                "SnapAlways"
            ]
        }
        Property {
            name: "xAxis"
            type: "QQuickDragAxis"
            isPointer: true
            read: "xAxis"
            index: 0
            isReadonly: true
        }
        Property {
            name: "yAxis"
            type: "QQuickDragAxis"
            isPointer: true
            read: "yAxis"
            index: 1
            isReadonly: true
        }
        Property {
            name: "translation"
            type: "QVector2D"
            read: "translation"
            notify: "translationChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "activeTranslation"
            revision: 1538
            type: "QVector2D"
            read: "activeTranslation"
            notify: "translationChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "persistentTranslation"
            revision: 1538
            type: "QVector2D"
            read: "persistentTranslation"
            write: "setPersistentTranslation"
            notify: "translationChanged"
            index: 4
        }
        Property {
            name: "snapMode"
            revision: 526
            type: "SnapMode"
            read: "snapMode"
            write: "setSnapMode"
            notify: "snapModeChanged"
            index: 5
        }
        Signal { name: "translationChanged" }
        Signal { name: "snapModeChanged"; revision: 526 }
    }
    Component {
        file: "private/qquickdroparea_p.h"
        name: "QQuickDropArea"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/DropArea 2.0",
            "QtQuick/DropArea 2.1",
            "QtQuick/DropArea 2.4",
            "QtQuick/DropArea 2.7",
            "QtQuick/DropArea 2.11",
            "QtQuick/DropArea 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Property {
            name: "containsDrag"
            type: "bool"
            read: "containsDrag"
            notify: "containsDragChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "keys"
            type: "QStringList"
            read: "keys"
            write: "setKeys"
            notify: "keysChanged"
            index: 1
        }
        Property {
            name: "drag"
            type: "QQuickDropAreaDrag"
            isPointer: true
            read: "drag"
            index: 2
            isReadonly: true
        }
        Signal { name: "containsDragChanged" }
        Signal { name: "keysChanged" }
        Signal { name: "sourceChanged" }
        Signal {
            name: "entered"
            Parameter { name: "drag"; type: "QQuickDragEvent"; isPointer: true }
        }
        Signal { name: "exited" }
        Signal {
            name: "positionChanged"
            Parameter { name: "drag"; type: "QQuickDragEvent"; isPointer: true }
        }
        Signal {
            name: "dropped"
            Parameter { name: "drop"; type: "QQuickDragEvent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickdroparea_p.h"
        name: "QQuickDropAreaDrag"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "x"
            type: "double"
            read: "x"
            notify: "positionChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "y"
            type: "double"
            read: "y"
            notify: "positionChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "source"
            type: "QObject"
            isPointer: true
            read: "source"
            notify: "sourceChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "positionChanged" }
        Signal { name: "sourceChanged" }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickEnterKeyAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/EnterKey 2.6", "QtQuick/EnterKey 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [518, 1536]
        attachedType: "QQuickEnterKeyAttached"
        Property {
            name: "type"
            type: "Qt::EnterKeyType"
            read: "type"
            write: "setType"
            notify: "typeChanged"
            index: 0
        }
        Signal { name: "typeChanged" }
    }
    Component {
        file: "private/qquickflickable_p.h"
        name: "QQuickFlickable"
        accessSemantics: "reference"
        defaultProperty: "flickableData"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Flickable 2.0",
            "QtQuick/Flickable 2.1",
            "QtQuick/Flickable 2.4",
            "QtQuick/Flickable 2.7",
            "QtQuick/Flickable 2.9",
            "QtQuick/Flickable 2.10",
            "QtQuick/Flickable 2.11",
            "QtQuick/Flickable 2.12",
            "QtQuick/Flickable 6.0"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            519,
            521,
            522,
            523,
            524,
            1536
        ]
        Enum {
            name: "BoundsBehavior"
            alias: "BoundsBehaviorFlag"
            isFlag: true
            values: [
                "StopAtBounds",
                "DragOverBounds",
                "OvershootBounds",
                "DragAndOvershootBounds"
            ]
        }
        Enum {
            name: "BoundsMovement"
            values: ["FollowBoundsBehavior"]
        }
        Enum {
            name: "FlickableDirection"
            values: [
                "AutoFlickDirection",
                "HorizontalFlick",
                "VerticalFlick",
                "HorizontalAndVerticalFlick",
                "AutoFlickIfNeeded"
            ]
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            write: "setContentWidth"
            notify: "contentWidthChanged"
            index: 0
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            write: "setContentHeight"
            notify: "contentHeightChanged"
            index: 1
        }
        Property {
            name: "contentX"
            type: "double"
            read: "contentX"
            write: "setContentX"
            notify: "contentXChanged"
            index: 2
        }
        Property {
            name: "contentY"
            type: "double"
            read: "contentY"
            write: "setContentY"
            notify: "contentYChanged"
            index: 3
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            index: 4
            isReadonly: true
        }
        Property {
            name: "topMargin"
            type: "double"
            read: "topMargin"
            write: "setTopMargin"
            notify: "topMarginChanged"
            index: 5
        }
        Property {
            name: "bottomMargin"
            type: "double"
            read: "bottomMargin"
            write: "setBottomMargin"
            notify: "bottomMarginChanged"
            index: 6
        }
        Property {
            name: "originY"
            type: "double"
            read: "originY"
            notify: "originYChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "leftMargin"
            type: "double"
            read: "leftMargin"
            write: "setLeftMargin"
            notify: "leftMarginChanged"
            index: 8
        }
        Property {
            name: "rightMargin"
            type: "double"
            read: "rightMargin"
            write: "setRightMargin"
            notify: "rightMarginChanged"
            index: 9
        }
        Property {
            name: "originX"
            type: "double"
            read: "originX"
            notify: "originXChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "horizontalVelocity"
            type: "double"
            read: "horizontalVelocity"
            notify: "horizontalVelocityChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "verticalVelocity"
            type: "double"
            read: "verticalVelocity"
            notify: "verticalVelocityChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "boundsBehavior"
            type: "BoundsBehavior"
            read: "boundsBehavior"
            write: "setBoundsBehavior"
            notify: "boundsBehaviorChanged"
            index: 13
        }
        Property {
            name: "boundsMovement"
            revision: 522
            type: "BoundsMovement"
            read: "boundsMovement"
            write: "setBoundsMovement"
            notify: "boundsMovementChanged"
            index: 14
        }
        Property {
            name: "rebound"
            type: "QQuickTransition"
            isPointer: true
            read: "rebound"
            write: "setRebound"
            notify: "reboundChanged"
            index: 15
        }
        Property {
            name: "maximumFlickVelocity"
            type: "double"
            read: "maximumFlickVelocity"
            write: "setMaximumFlickVelocity"
            notify: "maximumFlickVelocityChanged"
            index: 16
        }
        Property {
            name: "flickDeceleration"
            type: "double"
            read: "flickDeceleration"
            write: "setFlickDeceleration"
            notify: "flickDecelerationChanged"
            index: 17
        }
        Property {
            name: "moving"
            type: "bool"
            read: "isMoving"
            notify: "movingChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "movingHorizontally"
            type: "bool"
            read: "isMovingHorizontally"
            notify: "movingHorizontallyChanged"
            index: 19
            isReadonly: true
        }
        Property {
            name: "movingVertically"
            type: "bool"
            read: "isMovingVertically"
            notify: "movingVerticallyChanged"
            index: 20
            isReadonly: true
        }
        Property {
            name: "flicking"
            type: "bool"
            read: "isFlicking"
            notify: "flickingChanged"
            index: 21
            isReadonly: true
        }
        Property {
            name: "flickingHorizontally"
            type: "bool"
            read: "isFlickingHorizontally"
            notify: "flickingHorizontallyChanged"
            index: 22
            isReadonly: true
        }
        Property {
            name: "flickingVertically"
            type: "bool"
            read: "isFlickingVertically"
            notify: "flickingVerticallyChanged"
            index: 23
            isReadonly: true
        }
        Property {
            name: "dragging"
            type: "bool"
            read: "isDragging"
            notify: "draggingChanged"
            index: 24
            isReadonly: true
        }
        Property {
            name: "draggingHorizontally"
            type: "bool"
            read: "isDraggingHorizontally"
            notify: "draggingHorizontallyChanged"
            index: 25
            isReadonly: true
        }
        Property {
            name: "draggingVertically"
            type: "bool"
            read: "isDraggingVertically"
            notify: "draggingVerticallyChanged"
            index: 26
            isReadonly: true
        }
        Property {
            name: "flickableDirection"
            type: "FlickableDirection"
            read: "flickableDirection"
            write: "setFlickableDirection"
            notify: "flickableDirectionChanged"
            index: 27
        }
        Property {
            name: "interactive"
            type: "bool"
            read: "isInteractive"
            write: "setInteractive"
            notify: "interactiveChanged"
            index: 28
        }
        Property {
            name: "pressDelay"
            type: "int"
            read: "pressDelay"
            write: "setPressDelay"
            notify: "pressDelayChanged"
            index: 29
        }
        Property {
            name: "atXEnd"
            type: "bool"
            read: "isAtXEnd"
            notify: "atXEndChanged"
            index: 30
            isReadonly: true
        }
        Property {
            name: "atYEnd"
            type: "bool"
            read: "isAtYEnd"
            notify: "atYEndChanged"
            index: 31
            isReadonly: true
        }
        Property {
            name: "atXBeginning"
            type: "bool"
            read: "isAtXBeginning"
            notify: "atXBeginningChanged"
            index: 32
            isReadonly: true
        }
        Property {
            name: "atYBeginning"
            type: "bool"
            read: "isAtYBeginning"
            notify: "atYBeginningChanged"
            index: 33
            isReadonly: true
        }
        Property {
            name: "visibleArea"
            type: "QQuickFlickableVisibleArea"
            isPointer: true
            read: "visibleArea"
            index: 34
            isReadonly: true
        }
        Property {
            name: "pixelAligned"
            type: "bool"
            read: "pixelAligned"
            write: "setPixelAligned"
            notify: "pixelAlignedChanged"
            index: 35
        }
        Property {
            name: "synchronousDrag"
            revision: 524
            type: "bool"
            read: "synchronousDrag"
            write: "setSynchronousDrag"
            notify: "synchronousDragChanged"
            index: 36
        }
        Property {
            name: "horizontalOvershoot"
            revision: 521
            type: "double"
            read: "horizontalOvershoot"
            notify: "horizontalOvershootChanged"
            index: 37
            isReadonly: true
        }
        Property {
            name: "verticalOvershoot"
            revision: 521
            type: "double"
            read: "verticalOvershoot"
            notify: "verticalOvershootChanged"
            index: 38
            isReadonly: true
        }
        Property {
            name: "flickableData"
            type: "QObject"
            isList: true
            read: "flickableData"
            index: 39
            isReadonly: true
        }
        Property {
            name: "flickableChildren"
            type: "QQuickItem"
            isList: true
            read: "flickableChildren"
            index: 40
            isReadonly: true
        }
        Signal { name: "contentWidthChanged" }
        Signal { name: "contentHeightChanged" }
        Signal { name: "contentXChanged" }
        Signal { name: "contentYChanged" }
        Signal { name: "topMarginChanged" }
        Signal { name: "bottomMarginChanged" }
        Signal { name: "leftMarginChanged" }
        Signal { name: "rightMarginChanged" }
        Signal { name: "originYChanged" }
        Signal { name: "originXChanged" }
        Signal { name: "movingChanged" }
        Signal { name: "movingHorizontallyChanged" }
        Signal { name: "movingVerticallyChanged" }
        Signal { name: "flickingChanged" }
        Signal { name: "flickingHorizontallyChanged" }
        Signal { name: "flickingVerticallyChanged" }
        Signal { name: "draggingChanged" }
        Signal { name: "draggingHorizontallyChanged" }
        Signal { name: "draggingVerticallyChanged" }
        Signal { name: "horizontalVelocityChanged" }
        Signal { name: "verticalVelocityChanged" }
        Signal { name: "isAtBoundaryChanged" }
        Signal { name: "flickableDirectionChanged" }
        Signal { name: "interactiveChanged" }
        Signal { name: "boundsBehaviorChanged" }
        Signal { name: "boundsMovementChanged"; revision: 522 }
        Signal { name: "reboundChanged" }
        Signal { name: "maximumFlickVelocityChanged" }
        Signal { name: "flickDecelerationChanged" }
        Signal { name: "pressDelayChanged" }
        Signal { name: "movementStarted" }
        Signal { name: "movementEnded" }
        Signal { name: "flickStarted" }
        Signal { name: "flickEnded" }
        Signal { name: "dragStarted" }
        Signal { name: "dragEnded" }
        Signal { name: "pixelAlignedChanged" }
        Signal { name: "synchronousDragChanged"; revision: 524 }
        Signal { name: "horizontalOvershootChanged"; revision: 521 }
        Signal { name: "verticalOvershootChanged"; revision: 521 }
        Signal { name: "atXEndChanged" }
        Signal { name: "atYEndChanged" }
        Signal { name: "atXBeginningChanged" }
        Signal { name: "atYBeginningChanged" }
        Method { name: "movementStarting" }
        Method { name: "movementEnding" }
        Method {
            name: "movementEnding"
            Parameter { name: "hMovementEnding"; type: "bool" }
            Parameter { name: "vMovementEnding"; type: "bool" }
        }
        Method { name: "velocityTimelineCompleted" }
        Method { name: "timelineCompleted" }
        Method {
            name: "resizeContent"
            Parameter { name: "w"; type: "double" }
            Parameter { name: "h"; type: "double" }
            Parameter { name: "center"; type: "QPointF" }
        }
        Method { name: "returnToBounds" }
        Method {
            name: "flick"
            Parameter { name: "xVelocity"; type: "double" }
            Parameter { name: "yVelocity"; type: "double" }
        }
        Method { name: "cancelFlick" }
    }
    Component {
        file: "private/qquickflickable_p_p.h"
        name: "QQuickFlickableVisibleArea"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "xPosition"
            type: "double"
            read: "xPosition"
            notify: "xPositionChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "yPosition"
            type: "double"
            read: "yPosition"
            notify: "yPositionChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "widthRatio"
            type: "double"
            read: "widthRatio"
            notify: "widthRatioChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "heightRatio"
            type: "double"
            read: "heightRatio"
            notify: "heightRatioChanged"
            index: 3
            isReadonly: true
        }
        Signal {
            name: "xPositionChanged"
            Parameter { name: "xPosition"; type: "double" }
        }
        Signal {
            name: "yPositionChanged"
            Parameter { name: "yPosition"; type: "double" }
        }
        Signal {
            name: "widthRatioChanged"
            Parameter { name: "widthRatio"; type: "double" }
        }
        Signal {
            name: "heightRatioChanged"
            Parameter { name: "heightRatio"; type: "double" }
        }
    }
    Component {
        file: "private/qquickflipable_p.h"
        name: "QQuickFlipable"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Flipable 2.0",
            "QtQuick/Flipable 2.1",
            "QtQuick/Flipable 2.4",
            "QtQuick/Flipable 2.7",
            "QtQuick/Flipable 2.11",
            "QtQuick/Flipable 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Enum {
            name: "Side"
            values: ["Front", "Back"]
        }
        Property {
            name: "front"
            type: "QQuickItem"
            isPointer: true
            read: "front"
            write: "setFront"
            notify: "frontChanged"
            index: 0
        }
        Property {
            name: "back"
            type: "QQuickItem"
            isPointer: true
            read: "back"
            write: "setBack"
            notify: "backChanged"
            index: 1
        }
        Property {
            name: "side"
            type: "Side"
            read: "side"
            notify: "sideChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "frontChanged" }
        Signal { name: "backChanged" }
        Signal { name: "sideChanged" }
        Method { name: "retransformBack" }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickFlow"
        accessSemantics: "reference"
        prototype: "QQuickBasePositioner"
        exports: [
            "QtQuick/Flow 2.0",
            "QtQuick/Flow 2.1",
            "QtQuick/Flow 2.4",
            "QtQuick/Flow 2.6",
            "QtQuick/Flow 2.7",
            "QtQuick/Flow 2.9",
            "QtQuick/Flow 2.11",
            "QtQuick/Flow 6.0",
            "QtQuick/Flow 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            518,
            519,
            521,
            523,
            1536,
            1538
        ]
        Enum {
            name: "Flow"
            values: ["LeftToRight", "TopToBottom"]
        }
        Property {
            name: "flow"
            type: "Flow"
            read: "flow"
            write: "setFlow"
            notify: "flowChanged"
            index: 0
        }
        Property {
            name: "layoutDirection"
            type: "Qt::LayoutDirection"
            read: "layoutDirection"
            write: "setLayoutDirection"
            notify: "layoutDirectionChanged"
            index: 1
        }
        Property {
            name: "effectiveLayoutDirection"
            type: "Qt::LayoutDirection"
            read: "effectiveLayoutDirection"
            notify: "effectiveLayoutDirectionChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "flowChanged" }
        Signal { name: "layoutDirectionChanged" }
        Signal { name: "effectiveLayoutDirectionChanged" }
    }
    Component {
        file: "private/qquickfocusscope_p.h"
        name: "QQuickFocusScope"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/FocusScope 2.0",
            "QtQuick/FocusScope 2.1",
            "QtQuick/FocusScope 2.4",
            "QtQuick/FocusScope 2.7",
            "QtQuick/FocusScope 2.11",
            "QtQuick/FocusScope 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickFontEnums"
        accessSemantics: "none"
        exports: ["QtQuick/Font 2.0", "QtQuick/Font 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "FontWeight"
            values: [
                "Thin",
                "ExtraLight",
                "Light",
                "Normal",
                "Medium",
                "DemiBold",
                "Bold",
                "ExtraBold",
                "Black"
            ]
        }
        Enum {
            name: "Capitalization"
            values: [
                "MixedCase",
                "AllUppercase",
                "AllLowercase",
                "SmallCaps",
                "Capitalize"
            ]
        }
        Enum {
            name: "HintingPreference"
            values: [
                "PreferDefaultHinting",
                "PreferNoHinting",
                "PreferVerticalHinting",
                "PreferFullHinting"
            ]
        }
    }
    Component {
        file: "private/qquickfontloader_p.h"
        name: "QQuickFontLoader"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/FontLoader 2.0", "QtQuick/FontLoader 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            notify: "nameChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            notify: "fontChanged"
            index: 3
            isReadonly: true
        }
        Signal { name: "sourceChanged" }
        Signal { name: "nameChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "statusChanged" }
        Method {
            name: "updateFontInfo"
            Parameter { type: "int" }
        }
    }
    Component {
        file: "private/qquickfontmetrics_p.h"
        name: "QQuickFontMetrics"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/FontMetrics 2.4", "QtQuick/FontMetrics 6.0"]
        exportMetaObjectRevisions: [516, 1536]
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 0
        }
        Property {
            name: "ascent"
            type: "double"
            read: "ascent"
            notify: "fontChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "descent"
            type: "double"
            read: "descent"
            notify: "fontChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "height"
            type: "double"
            read: "height"
            notify: "fontChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "leading"
            type: "double"
            read: "leading"
            notify: "fontChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "lineSpacing"
            type: "double"
            read: "lineSpacing"
            notify: "fontChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "minimumLeftBearing"
            type: "double"
            read: "minimumLeftBearing"
            notify: "fontChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "minimumRightBearing"
            type: "double"
            read: "minimumRightBearing"
            notify: "fontChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "maximumCharacterWidth"
            type: "double"
            read: "maximumCharacterWidth"
            notify: "fontChanged"
            index: 8
            isReadonly: true
        }
        Property {
            name: "xHeight"
            type: "double"
            read: "xHeight"
            notify: "fontChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "averageCharacterWidth"
            type: "double"
            read: "averageCharacterWidth"
            notify: "fontChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "underlinePosition"
            type: "double"
            read: "underlinePosition"
            notify: "fontChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "overlinePosition"
            type: "double"
            read: "overlinePosition"
            notify: "fontChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "strikeOutPosition"
            type: "double"
            read: "strikeOutPosition"
            notify: "fontChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "lineWidth"
            type: "double"
            read: "lineWidth"
            notify: "fontChanged"
            index: 14
            isReadonly: true
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Method {
            name: "advanceWidth"
            type: "double"
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "boundingRect"
            type: "QRectF"
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "tightBoundingRect"
            type: "QRectF"
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "elidedText"
            type: "QString"
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "mode"; type: "Qt::TextElideMode" }
            Parameter { name: "width"; type: "double" }
            Parameter { name: "flags"; type: "int" }
        }
        Method {
            name: "elidedText"
            type: "QString"
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "mode"; type: "Qt::TextElideMode" }
            Parameter { name: "width"; type: "double" }
        }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QFont"
        accessSemantics: "value"
        extension: "QQuickFontValueType"
        exports: ["QtQuick/font 2.0", "QtQuick/font 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "StyleHint"
            values: [
                "Helvetica",
                "SansSerif",
                "Times",
                "Serif",
                "Courier",
                "TypeWriter",
                "OldEnglish",
                "Decorative",
                "System",
                "AnyStyle",
                "Cursive",
                "Monospace",
                "Fantasy"
            ]
        }
        Enum {
            name: "StyleStrategy"
            values: [
                "PreferDefault",
                "PreferBitmap",
                "PreferDevice",
                "PreferOutline",
                "ForceOutline",
                "PreferMatch",
                "PreferQuality",
                "PreferAntialias",
                "NoAntialias",
                "NoSubpixelAntialias",
                "PreferNoShaping",
                "NoFontMerging"
            ]
        }
        Enum {
            name: "HintingPreference"
            values: [
                "PreferDefaultHinting",
                "PreferNoHinting",
                "PreferVerticalHinting",
                "PreferFullHinting"
            ]
        }
        Enum {
            name: "Weight"
            values: [
                "Thin",
                "ExtraLight",
                "Light",
                "Normal",
                "Medium",
                "DemiBold",
                "Bold",
                "ExtraBold",
                "Black"
            ]
        }
        Enum {
            name: "Style"
            values: ["StyleNormal", "StyleItalic", "StyleOblique"]
        }
        Enum {
            name: "Stretch"
            values: [
                "AnyStretch",
                "UltraCondensed",
                "ExtraCondensed",
                "Condensed",
                "SemiCondensed",
                "Unstretched",
                "SemiExpanded",
                "Expanded",
                "ExtraExpanded",
                "UltraExpanded"
            ]
        }
        Enum {
            name: "Capitalization"
            values: [
                "MixedCase",
                "AllUppercase",
                "AllLowercase",
                "SmallCaps",
                "Capitalize"
            ]
        }
        Enum {
            name: "SpacingType"
            values: ["PercentageSpacing", "AbsoluteSpacing"]
        }
        Enum {
            name: "ResolveProperties"
            values: [
                "NoPropertiesResolved",
                "FamilyResolved",
                "SizeResolved",
                "StyleHintResolved",
                "StyleStrategyResolved",
                "WeightResolved",
                "StyleResolved",
                "UnderlineResolved",
                "OverlineResolved",
                "StrikeOutResolved",
                "FixedPitchResolved",
                "StretchResolved",
                "KerningResolved",
                "CapitalizationResolved",
                "LetterSpacingResolved",
                "WordSpacingResolved",
                "HintingPreferenceResolved",
                "StyleNameResolved",
                "FamiliesResolved",
                "AllPropertiesResolved"
            ]
        }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickFontValueType"
        accessSemantics: "value"
        Property {
            name: "family"
            type: "QString"
            read: "family"
            write: "setFamily"
            index: 0
            isFinal: true
        }
        Property {
            name: "styleName"
            type: "QString"
            read: "styleName"
            write: "setStyleName"
            index: 1
            isFinal: true
        }
        Property { name: "bold"; type: "bool"; read: "bold"; write: "setBold"; index: 2; isFinal: true }
        Property { name: "weight"; type: "int"; read: "weight"; write: "setWeight"; index: 3; isFinal: true }
        Property { name: "italic"; type: "bool"; read: "italic"; write: "setItalic"; index: 4; isFinal: true }
        Property {
            name: "underline"
            type: "bool"
            read: "underline"
            write: "setUnderline"
            index: 5
            isFinal: true
        }
        Property {
            name: "overline"
            type: "bool"
            read: "overline"
            write: "setOverline"
            index: 6
            isFinal: true
        }
        Property {
            name: "strikeout"
            type: "bool"
            read: "strikeout"
            write: "setStrikeout"
            index: 7
            isFinal: true
        }
        Property {
            name: "pointSize"
            type: "double"
            read: "pointSize"
            write: "setPointSize"
            index: 8
            isFinal: true
        }
        Property {
            name: "pixelSize"
            type: "int"
            read: "pixelSize"
            write: "setPixelSize"
            index: 9
            isFinal: true
        }
        Property {
            name: "capitalization"
            type: "QQuickFontEnums::Capitalization"
            read: "capitalization"
            write: "setCapitalization"
            index: 10
            isFinal: true
        }
        Property {
            name: "letterSpacing"
            type: "double"
            read: "letterSpacing"
            write: "setLetterSpacing"
            index: 11
            isFinal: true
        }
        Property {
            name: "wordSpacing"
            type: "double"
            read: "wordSpacing"
            write: "setWordSpacing"
            index: 12
            isFinal: true
        }
        Property {
            name: "hintingPreference"
            type: "QQuickFontEnums::HintingPreference"
            read: "hintingPreference"
            write: "setHintingPreference"
            index: 13
            isFinal: true
        }
        Property {
            name: "kerning"
            type: "bool"
            read: "kerning"
            write: "setKerning"
            index: 14
            isFinal: true
        }
        Property {
            name: "preferShaping"
            type: "bool"
            read: "preferShaping"
            write: "setPreferShaping"
            index: 15
            isFinal: true
        }
        Method { name: "toString"; type: "QString" }
    }
    Component {
        file: "private/qquickmultipointtoucharea_p.h"
        name: "QQuickGrabGestureEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/GestureEvent 2.0", "QtQuick/GestureEvent 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "touchPoints"
            type: "QObject"
            isList: true
            read: "touchPoints"
            index: 0
            isReadonly: true
        }
        Property {
            name: "dragThreshold"
            type: "double"
            read: "dragThreshold"
            index: 1
            isReadonly: true
        }
        Method { name: "grab" }
    }
    Component {
        file: "private/qquickrectangle_p.h"
        name: "QQuickGradient"
        accessSemantics: "reference"
        defaultProperty: "stops"
        prototype: "QObject"
        extension: "QGradient"
        exports: [
            "QtQuick/Gradient 2.0",
            "QtQuick/Gradient 2.12",
            "QtQuick/Gradient 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Enum {
            name: "Orientation"
            values: ["Vertical", "Horizontal"]
        }
        Property {
            name: "stops"
            type: "QQuickGradientStop"
            isList: true
            read: "stops"
            index: 0
            isReadonly: true
        }
        Property {
            name: "orientation"
            revision: 524
            type: "Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 1
        }
        Signal { name: "updated" }
        Signal { name: "orientationChanged" }
    }
    Component {
        file: "private/qquickrectangle_p.h"
        name: "QQuickGradientStop"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/GradientStop 2.0", "QtQuick/GradientStop 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property { name: "position"; type: "double"; read: "position"; write: "setPosition"; index: 0 }
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 1 }
    }
    Component {
        file: "private/qquickgraphicsinfo_p.h"
        name: "QQuickGraphicsInfo"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/GraphicsInfo 2.8", "QtQuick/GraphicsInfo 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [520, 1536]
        attachedType: "QQuickGraphicsInfo"
        Enum {
            name: "GraphicsApi"
            values: [
                "Unknown",
                "Software",
                "OpenVG",
                "OpenGL",
                "Direct3D11",
                "Vulkan",
                "Metal",
                "Null",
                "OpenGLRhi",
                "Direct3D11Rhi",
                "VulkanRhi",
                "MetalRhi",
                "NullRhi"
            ]
        }
        Enum {
            name: "ShaderType"
            values: ["UnknownShadingLanguage", "GLSL", "HLSL", "RhiShader"]
        }
        Enum {
            name: "ShaderCompilationType"
            values: ["RuntimeCompilation", "OfflineCompilation"]
        }
        Enum {
            name: "ShaderSourceType"
            values: [
                "ShaderSourceString",
                "ShaderSourceFile",
                "ShaderByteCode"
            ]
        }
        Enum {
            name: "OpenGLContextProfile"
            values: [
                "OpenGLNoProfile",
                "OpenGLCoreProfile",
                "OpenGLCompatibilityProfile"
            ]
        }
        Enum {
            name: "RenderableType"
            values: [
                "SurfaceFormatUnspecified",
                "SurfaceFormatOpenGL",
                "SurfaceFormatOpenGLES"
            ]
        }
        Property {
            name: "api"
            type: "GraphicsApi"
            read: "api"
            notify: "apiChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "shaderType"
            type: "ShaderType"
            read: "shaderType"
            notify: "shaderTypeChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "shaderCompilationType"
            type: "ShaderCompilationType"
            read: "shaderCompilationType"
            notify: "shaderCompilationTypeChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "shaderSourceType"
            type: "ShaderSourceType"
            read: "shaderSourceType"
            notify: "shaderSourceTypeChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "majorVersion"
            type: "int"
            read: "majorVersion"
            notify: "majorVersionChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "minorVersion"
            type: "int"
            read: "minorVersion"
            notify: "minorVersionChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "profile"
            type: "OpenGLContextProfile"
            read: "profile"
            notify: "profileChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "renderableType"
            type: "RenderableType"
            read: "renderableType"
            notify: "renderableTypeChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Signal { name: "apiChanged" }
        Signal { name: "shaderTypeChanged" }
        Signal { name: "shaderCompilationTypeChanged" }
        Signal { name: "shaderSourceTypeChanged" }
        Signal { name: "majorVersionChanged" }
        Signal { name: "minorVersionChanged" }
        Signal { name: "profileChanged" }
        Signal { name: "renderableTypeChanged" }
        Method { name: "updateInfo" }
        Method {
            name: "setWindow"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickGrid"
        accessSemantics: "reference"
        prototype: "QQuickBasePositioner"
        exports: [
            "QtQuick/Grid 2.0",
            "QtQuick/Grid 2.1",
            "QtQuick/Grid 2.4",
            "QtQuick/Grid 2.6",
            "QtQuick/Grid 2.7",
            "QtQuick/Grid 2.9",
            "QtQuick/Grid 2.11",
            "QtQuick/Grid 6.0",
            "QtQuick/Grid 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            518,
            519,
            521,
            523,
            1536,
            1538
        ]
        Enum {
            name: "Flow"
            values: ["LeftToRight", "TopToBottom"]
        }
        Enum {
            name: "HAlignment"
            values: ["AlignLeft", "AlignRight", "AlignHCenter"]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Property {
            name: "rows"
            type: "int"
            read: "rows"
            write: "setRows"
            notify: "rowsChanged"
            index: 0
        }
        Property {
            name: "columns"
            type: "int"
            read: "columns"
            write: "setColumns"
            notify: "columnsChanged"
            index: 1
        }
        Property {
            name: "rowSpacing"
            type: "double"
            read: "rowSpacing"
            write: "setRowSpacing"
            notify: "rowSpacingChanged"
            index: 2
        }
        Property {
            name: "columnSpacing"
            type: "double"
            read: "columnSpacing"
            write: "setColumnSpacing"
            notify: "columnSpacingChanged"
            index: 3
        }
        Property {
            name: "flow"
            type: "Flow"
            read: "flow"
            write: "setFlow"
            notify: "flowChanged"
            index: 4
        }
        Property {
            name: "layoutDirection"
            type: "Qt::LayoutDirection"
            read: "layoutDirection"
            write: "setLayoutDirection"
            notify: "layoutDirectionChanged"
            index: 5
        }
        Property {
            name: "effectiveLayoutDirection"
            type: "Qt::LayoutDirection"
            read: "effectiveLayoutDirection"
            notify: "effectiveLayoutDirectionChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "horizontalItemAlignment"
            revision: 513
            type: "HAlignment"
            read: "hItemAlign"
            write: "setHItemAlign"
            notify: "horizontalAlignmentChanged"
            index: 7
        }
        Property {
            name: "effectiveHorizontalItemAlignment"
            revision: 513
            type: "HAlignment"
            read: "effectiveHAlign"
            notify: "effectiveHorizontalAlignmentChanged"
            index: 8
            isReadonly: true
        }
        Property {
            name: "verticalItemAlignment"
            revision: 513
            type: "VAlignment"
            read: "vItemAlign"
            write: "setVItemAlign"
            notify: "verticalAlignmentChanged"
            index: 9
        }
        Signal { name: "rowsChanged" }
        Signal { name: "columnsChanged" }
        Signal { name: "flowChanged" }
        Signal { name: "layoutDirectionChanged" }
        Signal { name: "effectiveLayoutDirectionChanged" }
        Signal { name: "rowSpacingChanged" }
        Signal { name: "columnSpacingChanged" }
        Signal {
            name: "horizontalAlignmentChanged"
            revision: 513
            Parameter { name: "alignment"; type: "HAlignment" }
        }
        Signal {
            name: "effectiveHorizontalAlignmentChanged"
            revision: 513
            Parameter { name: "alignment"; type: "HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            revision: 513
            Parameter { name: "alignment"; type: "VAlignment" }
        }
    }
    Component {
        file: "private/qquickshadereffectmesh_p.h"
        name: "QQuickGridMesh"
        accessSemantics: "reference"
        prototype: "QQuickShaderEffectMesh"
        exports: ["QtQuick/GridMesh 2.0", "QtQuick/GridMesh 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "resolution"
            type: "QSize"
            read: "resolution"
            write: "setResolution"
            notify: "resolutionChanged"
            index: 0
        }
        Signal { name: "resolutionChanged" }
    }
    Component {
        file: "private/qquickgridview_p.h"
        name: "QQuickGridView"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickItemView"
        exports: [
            "QtQuick/GridView 2.0",
            "QtQuick/GridView 2.1",
            "QtQuick/GridView 2.3",
            "QtQuick/GridView 2.4",
            "QtQuick/GridView 2.7",
            "QtQuick/GridView 2.9",
            "QtQuick/GridView 2.10",
            "QtQuick/GridView 2.11",
            "QtQuick/GridView 2.12",
            "QtQuick/GridView 2.13",
            "QtQuick/GridView 2.15",
            "QtQuick/GridView 6.0"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            515,
            516,
            519,
            521,
            522,
            523,
            524,
            525,
            527,
            1536
        ]
        attachedType: "QQuickGridViewAttached"
        Enum {
            name: "Flow"
            values: ["FlowLeftToRight", "FlowTopToBottom"]
        }
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapToRow", "SnapOneRow"]
        }
        Property {
            name: "flow"
            type: "Flow"
            read: "flow"
            write: "setFlow"
            notify: "flowChanged"
            index: 0
        }
        Property {
            name: "cellWidth"
            type: "double"
            read: "cellWidth"
            write: "setCellWidth"
            notify: "cellWidthChanged"
            index: 1
        }
        Property {
            name: "cellHeight"
            type: "double"
            read: "cellHeight"
            write: "setCellHeight"
            notify: "cellHeightChanged"
            index: 2
        }
        Property {
            name: "snapMode"
            type: "SnapMode"
            read: "snapMode"
            write: "setSnapMode"
            notify: "snapModeChanged"
            index: 3
        }
        Signal { name: "cellWidthChanged" }
        Signal { name: "cellHeightChanged" }
        Signal { name: "highlightMoveDurationChanged" }
        Signal { name: "flowChanged" }
        Signal { name: "snapModeChanged" }
        Method { name: "moveCurrentIndexUp" }
        Method { name: "moveCurrentIndexDown" }
        Method { name: "moveCurrentIndexLeft" }
        Method { name: "moveCurrentIndexRight" }
    }
    Component {
        file: "private/qquickgridview_p.h"
        name: "QQuickGridViewAttached"
        accessSemantics: "reference"
        prototype: "QQuickItemViewAttached"
    }
    Component {
        file: "private/qquickhoverhandler_p.h"
        name: "QQuickHoverHandler"
        accessSemantics: "reference"
        prototype: "QQuickSinglePointHandler"
        exports: [
            "QtQuick/HoverHandler 2.12",
            "QtQuick/HoverHandler 2.15",
            "QtQuick/HoverHandler 6.0"
        ]
        exportMetaObjectRevisions: [524, 527, 1536]
        Property {
            name: "hovered"
            type: "bool"
            read: "isHovered"
            notify: "hoveredChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "hoveredChanged" }
    }
    Component {
        file: "private/qquickimage_p.h"
        name: "QQuickImage"
        accessSemantics: "reference"
        prototype: "QQuickImageBase"
        exports: [
            "QtQuick/Image 2.0",
            "QtQuick/Image 2.1",
            "QtQuick/Image 2.3",
            "QtQuick/Image 2.4",
            "QtQuick/Image 2.5",
            "QtQuick/Image 2.7",
            "QtQuick/Image 2.11",
            "QtQuick/Image 2.14",
            "QtQuick/Image 2.15",
            "QtQuick/Image 6.0",
            "QtQuick/Image 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            515,
            516,
            517,
            519,
            523,
            526,
            527,
            1536,
            1538
        ]
        Enum {
            name: "HAlignment"
            values: ["AlignLeft", "AlignRight", "AlignHCenter"]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "FillMode"
            values: [
                "Stretch",
                "PreserveAspectFit",
                "PreserveAspectCrop",
                "Tile",
                "TileVertically",
                "TileHorizontally",
                "Pad"
            ]
        }
        Property {
            name: "fillMode"
            type: "FillMode"
            read: "fillMode"
            write: "setFillMode"
            notify: "fillModeChanged"
            index: 0
        }
        Property {
            name: "paintedWidth"
            type: "double"
            read: "paintedWidth"
            notify: "paintedGeometryChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "paintedHeight"
            type: "double"
            read: "paintedHeight"
            notify: "paintedGeometryChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "horizontalAlignment"
            type: "HAlignment"
            read: "horizontalAlignment"
            write: "setHorizontalAlignment"
            notify: "horizontalAlignmentChanged"
            index: 3
        }
        Property {
            name: "verticalAlignment"
            type: "VAlignment"
            read: "verticalAlignment"
            write: "setVerticalAlignment"
            notify: "verticalAlignmentChanged"
            index: 4
        }
        Property {
            name: "mipmap"
            revision: 515
            type: "bool"
            read: "mipmap"
            write: "setMipmap"
            notify: "mipmapChanged"
            index: 5
        }
        Property {
            name: "autoTransform"
            revision: 517
            type: "bool"
            read: "autoTransform"
            write: "setAutoTransform"
            notify: "autoTransformChanged"
            index: 6
        }
        Property {
            name: "sourceClipRect"
            revision: 527
            type: "QRectF"
            read: "sourceClipRect"
            write: "setSourceClipRect"
            notify: "sourceClipRectChanged"
            index: 7
        }
        Signal { name: "fillModeChanged" }
        Signal { name: "paintedGeometryChanged" }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "VAlignment" }
        }
        Signal {
            name: "mipmapChanged"
            revision: 515
            Parameter { type: "bool" }
        }
        Signal { name: "autoTransformChanged"; revision: 517 }
        Method { name: "invalidateSceneGraph" }
    }
    Component {
        file: "private/qquickimagebase_p.h"
        name: "QQuickImageBase"
        accessSemantics: "reference"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/ImageBase 2.14",
            "QtQuick/ImageBase 2.15",
            "QtQuick/ImageBase 6.0",
            "QtQuick/ImageBase 6.2"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [526, 527, 1536, 1538]
        Enum {
            name: "LoadPixmapOptions"
            alias: "LoadPixmapOption"
            isFlag: true
            values: ["NoOption", "HandleDPR", "UseProviderOptions"]
        }
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 1
        }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            notify: "progressChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 3
        }
        Property {
            name: "cache"
            type: "bool"
            read: "cache"
            write: "setCache"
            notify: "cacheChanged"
            index: 4
        }
        Property {
            name: "sourceSize"
            type: "QSize"
            read: "sourceSize"
            write: "setSourceSize"
            notify: "sourceSizeChanged"
            index: 5
        }
        Property {
            name: "mirror"
            type: "bool"
            read: "mirror"
            write: "setMirror"
            notify: "mirrorChanged"
            index: 6
        }
        Property {
            name: "mirrorVertically"
            revision: 1538
            type: "bool"
            read: "mirrorVertically"
            write: "setMirrorVertically"
            notify: "mirrorVerticallyChanged"
            index: 7
        }
        Property {
            name: "currentFrame"
            revision: 526
            type: "int"
            read: "currentFrame"
            write: "setCurrentFrame"
            notify: "currentFrameChanged"
            index: 8
        }
        Property {
            name: "frameCount"
            revision: 526
            type: "int"
            read: "frameCount"
            notify: "frameCountChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "colorSpace"
            revision: 527
            type: "QColorSpace"
            read: "colorSpace"
            write: "setColorSpace"
            notify: "colorSpaceChanged"
            index: 10
        }
        Signal {
            name: "sourceChanged"
            Parameter { type: "QUrl" }
        }
        Signal { name: "sourceSizeChanged" }
        Signal {
            name: "statusChanged"
            Parameter { type: "QQuickImageBase::Status" }
        }
        Signal {
            name: "progressChanged"
            Parameter { name: "progress"; type: "double" }
        }
        Signal { name: "asynchronousChanged" }
        Signal { name: "cacheChanged" }
        Signal { name: "mirrorChanged" }
        Signal { name: "currentFrameChanged"; revision: 526 }
        Signal { name: "frameCountChanged"; revision: 526 }
        Signal { name: "sourceClipRectChanged"; revision: 527 }
        Signal { name: "colorSpaceChanged"; revision: 527 }
        Signal { name: "mirrorVerticallyChanged"; revision: 1538 }
        Method { name: "requestFinished" }
        Method {
            name: "requestProgress"
            Parameter { type: "qlonglong" }
            Parameter { type: "qlonglong" }
        }
    }
    Component {
        file: "private/qquickimplicitsizeitem_p.h"
        name: "QQuickImplicitSizeItem"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        Property {
            name: "implicitWidth"
            type: "double"
            read: "implicitWidth"
            notify: "implicitWidthChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "implicitHeight"
            type: "double"
            read: "implicitHeight"
            notify: "implicitHeightChanged"
            index: 1
            isReadonly: true
        }
    }
    Component {
        file: "private/qquickvalidator_p.h"
        name: "QQuickIntValidator"
        accessSemantics: "reference"
        prototype: "QIntValidator"
        exports: ["QtQuick/IntValidator 2.0", "QtQuick/IntValidator 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "locale"
            type: "QString"
            read: "localeName"
            write: "setLocaleName"
            notify: "localeNameChanged"
            index: 0
        }
        Signal { name: "localeNameChanged" }
    }
    Component {
        file: "qquickitem.h"
        name: "QQuickItem"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick/Item 2.0",
            "QtQuick/Item 2.1",
            "QtQuick/Item 2.4",
            "QtQuick/Item 2.7",
            "QtQuick/Item 2.11",
            "QtQuick/Item 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Enum {
            name: "Flags"
            alias: "Flag"
            isFlag: true
            values: [
                "ItemClipsChildrenToShape",
                "ItemAcceptsInputMethod",
                "ItemIsFocusScope",
                "ItemHasContents",
                "ItemAcceptsDrops"
            ]
        }
        Enum {
            name: "TransformOrigin"
            values: [
                "TopLeft",
                "Top",
                "TopRight",
                "Left",
                "Center",
                "Right",
                "BottomLeft",
                "Bottom",
                "BottomRight"
            ]
        }
        Property {
            name: "parent"
            type: "QQuickItem"
            isPointer: true
            read: "parentItem"
            write: "setParentItem"
            notify: "parentChanged"
            index: 0
            isFinal: true
        }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "resources"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "children"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property {
            name: "x"
            type: "double"
            bindable: "bindableX"
            read: "x"
            write: "setX"
            notify: "xChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "y"
            type: "double"
            bindable: "bindableY"
            read: "y"
            write: "setY"
            notify: "yChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "z"
            type: "double"
            read: "z"
            write: "setZ"
            notify: "zChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "width"
            type: "double"
            bindable: "bindableWidth"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "height"
            type: "double"
            bindable: "bindableHeight"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "opacity"
            type: "double"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 10
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 11
            isFinal: true
        }
        Property { name: "palette"; revision: 1536; type: "QQuickPalette"; isPointer: true }
        Property { name: "visibleChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Property {
            name: "state"
            type: "QString"
            read: "state"
            write: "setState"
            notify: "stateChanged"
            index: 16
        }
        Property {
            name: "childrenRect"
            type: "QRectF"
            read: "childrenRect"
            notify: "childrenRectChanged"
            index: 17
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "anchors"
            type: "QQuickAnchors"
            isPointer: true
            isReadonly: true
            isFinal: true
        }
        Property { name: "left"; type: "QQuickAnchorLine"; isReadonly: true; isFinal: true }
        Property { name: "right"; type: "QQuickAnchorLine"; isReadonly: true; isFinal: true }
        Property { name: "horizontalCenter"; type: "QQuickAnchorLine"; isReadonly: true; isFinal: true }
        Property { name: "top"; type: "QQuickAnchorLine"; isReadonly: true; isFinal: true }
        Property { name: "bottom"; type: "QQuickAnchorLine"; isReadonly: true; isFinal: true }
        Property { name: "verticalCenter"; type: "QQuickAnchorLine"; isReadonly: true; isFinal: true }
        Property { name: "baseline"; type: "QQuickAnchorLine"; isReadonly: true; isFinal: true }
        Property {
            name: "baselineOffset"
            type: "double"
            read: "baselineOffset"
            write: "setBaselineOffset"
            notify: "baselineOffsetChanged"
            index: 26
        }
        Property {
            name: "clip"
            type: "bool"
            read: "clip"
            write: "setClip"
            notify: "clipChanged"
            index: 27
        }
        Property {
            name: "focus"
            type: "bool"
            read: "hasFocus"
            write: "setFocus"
            notify: "focusChanged"
            index: 28
            isFinal: true
        }
        Property {
            name: "activeFocus"
            type: "bool"
            read: "hasActiveFocus"
            notify: "activeFocusChanged"
            index: 29
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "activeFocusOnTab"
            revision: 513
            type: "bool"
            read: "activeFocusOnTab"
            write: "setActiveFocusOnTab"
            notify: "activeFocusOnTabChanged"
            index: 30
            isFinal: true
        }
        Property {
            name: "rotation"
            type: "double"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 31
        }
        Property {
            name: "scale"
            type: "double"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 32
        }
        Property {
            name: "transformOrigin"
            type: "TransformOrigin"
            read: "transformOrigin"
            write: "setTransformOrigin"
            notify: "transformOriginChanged"
            index: 33
        }
        Property {
            name: "transformOriginPoint"
            type: "QPointF"
            read: "transformOriginPoint"
            index: 34
            isReadonly: true
        }
        Property {
            name: "transform"
            type: "QQuickTransform"
            isList: true
            read: "transform"
            index: 35
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "smooth"
            type: "bool"
            read: "smooth"
            write: "setSmooth"
            notify: "smoothChanged"
            index: 36
        }
        Property {
            name: "antialiasing"
            type: "bool"
            read: "antialiasing"
            write: "setAntialiasing"
            notify: "antialiasingChanged"
            index: 37
        }
        Property {
            name: "implicitWidth"
            type: "double"
            read: "implicitWidth"
            write: "setImplicitWidth"
            notify: "implicitWidthChanged"
            index: 38
        }
        Property {
            name: "implicitHeight"
            type: "double"
            read: "implicitHeight"
            write: "setImplicitHeight"
            notify: "implicitHeightChanged"
            index: 39
        }
        Property {
            name: "containmentMask"
            revision: 523
            type: "QObject"
            isPointer: true
            read: "containmentMask"
            write: "setContainmentMask"
            notify: "containmentMaskChanged"
            index: 40
        }
        Property {
            name: "layer"
            type: "QQuickItemLayer"
            isPointer: true
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "childrenRectChanged"
            Parameter { type: "QRectF" }
        }
        Signal {
            name: "baselineOffsetChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "stateChanged"
            Parameter { type: "QString" }
        }
        Signal {
            name: "focusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusOnTabChanged"
            revision: 513
            Parameter { type: "bool" }
        }
        Signal {
            name: "parentChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "transformOriginChanged"
            Parameter { type: "TransformOrigin" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "antialiasingChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "clipChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "windowChanged"
            revision: 513
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "childrenChanged" }
        Signal { name: "opacityChanged" }
        Signal { name: "enabledChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "visibleChildrenChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
        Signal { name: "zChanged" }
        Signal { name: "implicitWidthChanged" }
        Signal { name: "implicitHeightChanged" }
        Signal { name: "containmentMaskChanged"; revision: 523 }
        Signal { name: "paletteChanged"; revision: 1536 }
        Signal { name: "paletteCreated"; revision: 1536 }
        Method { name: "update" }
        Method {
            name: "_q_resourceObjectDeleted"
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "_q_createJSWrapper"
            type: "qulonglong"
            Parameter { type: "QV4::ExecutionEngine"; isPointer: true }
        }
        Method {
            name: "grabToImage"
            revision: 516
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "targetSize"; type: "QSize" }
        }
        Method {
            name: "grabToImage"
            revision: 516
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "contains"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapFromItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapFromGlobal"
            revision: 519
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToGlobal"
            revision: 519
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "forceActiveFocus" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method {
            name: "nextItemInFocusChain"
            revision: 513
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "forward"; type: "bool" }
        }
        Method { name: "nextItemInFocusChain"; revision: 513; type: "QQuickItem"; isPointer: true }
        Method {
            name: "childAt"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        file: "qquickitemgrabresult.h"
        name: "QQuickItemGrabResult"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "image"; type: "QImage"; read: "image"; index: 0; isReadonly: true }
        Property { name: "url"; type: "QUrl"; read: "url"; index: 1; isReadonly: true }
        Signal { name: "ready" }
        Method { name: "setup" }
        Method { name: "render" }
        Method {
            name: "saveToFile"
            type: "bool"
            Parameter { name: "fileName"; type: "QString" }
        }
        Method {
            name: "saveToFile"
            revision: 1538
            type: "bool"
            Parameter { name: "fileName"; type: "QUrl" }
        }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickItemLayer"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Property {
            name: "textureSize"
            type: "QSize"
            read: "size"
            write: "setSize"
            notify: "sizeChanged"
            index: 1
        }
        Property {
            name: "sourceRect"
            type: "QRectF"
            read: "sourceRect"
            write: "setSourceRect"
            notify: "sourceRectChanged"
            index: 2
        }
        Property {
            name: "mipmap"
            type: "bool"
            read: "mipmap"
            write: "setMipmap"
            notify: "mipmapChanged"
            index: 3
        }
        Property {
            name: "smooth"
            type: "bool"
            read: "smooth"
            write: "setSmooth"
            notify: "smoothChanged"
            index: 4
        }
        Property {
            name: "wrapMode"
            type: "QQuickShaderEffectSource::WrapMode"
            read: "wrapMode"
            write: "setWrapMode"
            notify: "wrapModeChanged"
            index: 5
        }
        Property {
            name: "format"
            type: "QQuickShaderEffectSource::Format"
            read: "format"
            write: "setFormat"
            notify: "formatChanged"
            index: 6
        }
        Property {
            name: "samplerName"
            type: "QByteArray"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 7
        }
        Property {
            name: "effect"
            type: "QQmlComponent"
            isPointer: true
            read: "effect"
            write: "setEffect"
            notify: "effectChanged"
            index: 8
        }
        Property {
            name: "textureMirroring"
            type: "QQuickShaderEffectSource::TextureMirroring"
            read: "textureMirroring"
            write: "setTextureMirroring"
            notify: "textureMirroringChanged"
            index: 9
        }
        Property {
            name: "samples"
            type: "int"
            read: "samples"
            write: "setSamples"
            notify: "samplesChanged"
            index: 10
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "sizeChanged"
            Parameter { name: "size"; type: "QSize" }
        }
        Signal {
            name: "mipmapChanged"
            Parameter { name: "mipmap"; type: "bool" }
        }
        Signal {
            name: "wrapModeChanged"
            Parameter { name: "mode"; type: "QQuickShaderEffectSource::WrapMode" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QByteArray" }
        }
        Signal {
            name: "effectChanged"
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
        }
        Signal {
            name: "smoothChanged"
            Parameter { name: "smooth"; type: "bool" }
        }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "QQuickShaderEffectSource::Format" }
        }
        Signal {
            name: "sourceRectChanged"
            Parameter { name: "sourceRect"; type: "QRectF" }
        }
        Signal {
            name: "textureMirroringChanged"
            Parameter { name: "mirroring"; type: "QQuickShaderEffectSource::TextureMirroring" }
        }
        Signal {
            name: "samplesChanged"
            Parameter { name: "count"; type: "int" }
        }
    }
    Component {
        file: "private/qquickitemview_p.h"
        name: "QQuickItemView"
        accessSemantics: "reference"
        defaultProperty: "flickableData"
        prototype: "QQuickFlickable"
        exports: [
            "QtQuick/ItemView 2.1",
            "QtQuick/ItemView 2.3",
            "QtQuick/ItemView 2.4",
            "QtQuick/ItemView 2.7",
            "QtQuick/ItemView 2.9",
            "QtQuick/ItemView 2.10",
            "QtQuick/ItemView 2.11",
            "QtQuick/ItemView 2.12",
            "QtQuick/ItemView 2.13",
            "QtQuick/ItemView 2.15",
            "QtQuick/ItemView 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [
            513,
            515,
            516,
            519,
            521,
            522,
            523,
            524,
            525,
            527,
            1536
        ]
        Enum {
            name: "LayoutDirection"
            values: [
                "LeftToRight",
                "RightToLeft",
                "VerticalTopToBottom",
                "VerticalBottomToTop"
            ]
        }
        Enum {
            name: "VerticalLayoutDirection"
            values: ["TopToBottom", "BottomToTop"]
        }
        Enum {
            name: "HighlightRangeMode"
            values: ["NoHighlightRange", "ApplyRange", "StrictlyEnforceRange"]
        }
        Enum {
            name: "PositionMode"
            values: [
                "Beginning",
                "Center",
                "End",
                "Visible",
                "Contain",
                "SnapPosition"
            ]
        }
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 0
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 3
        }
        Property {
            name: "currentItem"
            type: "QQuickItem"
            isPointer: true
            read: "currentItem"
            notify: "currentItemChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "keyNavigationWraps"
            type: "bool"
            read: "isWrapEnabled"
            write: "setWrapEnabled"
            notify: "keyNavigationWrapsChanged"
            index: 5
        }
        Property {
            name: "keyNavigationEnabled"
            revision: 519
            type: "bool"
            read: "isKeyNavigationEnabled"
            write: "setKeyNavigationEnabled"
            notify: "keyNavigationEnabledChanged"
            index: 6
        }
        Property {
            name: "cacheBuffer"
            type: "int"
            read: "cacheBuffer"
            write: "setCacheBuffer"
            notify: "cacheBufferChanged"
            index: 7
        }
        Property {
            name: "displayMarginBeginning"
            revision: 515
            type: "int"
            read: "displayMarginBeginning"
            write: "setDisplayMarginBeginning"
            notify: "displayMarginBeginningChanged"
            index: 8
        }
        Property {
            name: "displayMarginEnd"
            revision: 515
            type: "int"
            read: "displayMarginEnd"
            write: "setDisplayMarginEnd"
            notify: "displayMarginEndChanged"
            index: 9
        }
        Property {
            name: "layoutDirection"
            type: "Qt::LayoutDirection"
            read: "layoutDirection"
            write: "setLayoutDirection"
            notify: "layoutDirectionChanged"
            index: 10
        }
        Property {
            name: "effectiveLayoutDirection"
            type: "Qt::LayoutDirection"
            read: "effectiveLayoutDirection"
            notify: "effectiveLayoutDirectionChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "verticalLayoutDirection"
            type: "VerticalLayoutDirection"
            read: "verticalLayoutDirection"
            write: "setVerticalLayoutDirection"
            notify: "verticalLayoutDirectionChanged"
            index: 12
        }
        Property {
            name: "header"
            type: "QQmlComponent"
            isPointer: true
            read: "header"
            write: "setHeader"
            notify: "headerChanged"
            index: 13
        }
        Property {
            name: "headerItem"
            type: "QQuickItem"
            isPointer: true
            read: "headerItem"
            notify: "headerItemChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "footer"
            type: "QQmlComponent"
            isPointer: true
            read: "footer"
            write: "setFooter"
            notify: "footerChanged"
            index: 15
        }
        Property {
            name: "footerItem"
            type: "QQuickItem"
            isPointer: true
            read: "footerItem"
            notify: "footerItemChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "populate"
            type: "QQuickTransition"
            isPointer: true
            read: "populateTransition"
            write: "setPopulateTransition"
            notify: "populateTransitionChanged"
            index: 17
        }
        Property {
            name: "add"
            type: "QQuickTransition"
            isPointer: true
            read: "addTransition"
            write: "setAddTransition"
            notify: "addTransitionChanged"
            index: 18
        }
        Property {
            name: "addDisplaced"
            type: "QQuickTransition"
            isPointer: true
            read: "addDisplacedTransition"
            write: "setAddDisplacedTransition"
            notify: "addDisplacedTransitionChanged"
            index: 19
        }
        Property {
            name: "move"
            type: "QQuickTransition"
            isPointer: true
            read: "moveTransition"
            write: "setMoveTransition"
            notify: "moveTransitionChanged"
            index: 20
        }
        Property {
            name: "moveDisplaced"
            type: "QQuickTransition"
            isPointer: true
            read: "moveDisplacedTransition"
            write: "setMoveDisplacedTransition"
            notify: "moveDisplacedTransitionChanged"
            index: 21
        }
        Property {
            name: "remove"
            type: "QQuickTransition"
            isPointer: true
            read: "removeTransition"
            write: "setRemoveTransition"
            notify: "removeTransitionChanged"
            index: 22
        }
        Property {
            name: "removeDisplaced"
            type: "QQuickTransition"
            isPointer: true
            read: "removeDisplacedTransition"
            write: "setRemoveDisplacedTransition"
            notify: "removeDisplacedTransitionChanged"
            index: 23
        }
        Property {
            name: "displaced"
            type: "QQuickTransition"
            isPointer: true
            read: "displacedTransition"
            write: "setDisplacedTransition"
            notify: "displacedTransitionChanged"
            index: 24
        }
        Property {
            name: "highlight"
            type: "QQmlComponent"
            isPointer: true
            read: "highlight"
            write: "setHighlight"
            notify: "highlightChanged"
            index: 25
        }
        Property {
            name: "highlightItem"
            type: "QQuickItem"
            isPointer: true
            read: "highlightItem"
            notify: "highlightItemChanged"
            index: 26
            isReadonly: true
        }
        Property {
            name: "highlightFollowsCurrentItem"
            type: "bool"
            read: "highlightFollowsCurrentItem"
            write: "setHighlightFollowsCurrentItem"
            notify: "highlightFollowsCurrentItemChanged"
            index: 27
        }
        Property {
            name: "highlightRangeMode"
            type: "HighlightRangeMode"
            read: "highlightRangeMode"
            write: "setHighlightRangeMode"
            notify: "highlightRangeModeChanged"
            index: 28
        }
        Property {
            name: "preferredHighlightBegin"
            type: "double"
            read: "preferredHighlightBegin"
            write: "setPreferredHighlightBegin"
            notify: "preferredHighlightBeginChanged"
            index: 29
        }
        Property {
            name: "preferredHighlightEnd"
            type: "double"
            read: "preferredHighlightEnd"
            write: "setPreferredHighlightEnd"
            notify: "preferredHighlightEndChanged"
            index: 30
        }
        Property {
            name: "highlightMoveDuration"
            type: "int"
            read: "highlightMoveDuration"
            write: "setHighlightMoveDuration"
            notify: "highlightMoveDurationChanged"
            index: 31
        }
        Property {
            name: "reuseItems"
            revision: 527
            type: "bool"
            read: "reuseItems"
            write: "setReuseItems"
            notify: "reuseItemsChanged"
            index: 32
        }
        Signal { name: "modelChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "countChanged" }
        Signal { name: "currentIndexChanged" }
        Signal { name: "currentItemChanged" }
        Signal { name: "keyNavigationWrapsChanged" }
        Signal { name: "keyNavigationEnabledChanged"; revision: 519 }
        Signal { name: "cacheBufferChanged" }
        Signal { name: "displayMarginBeginningChanged" }
        Signal { name: "displayMarginEndChanged" }
        Signal { name: "layoutDirectionChanged" }
        Signal { name: "effectiveLayoutDirectionChanged" }
        Signal { name: "verticalLayoutDirectionChanged" }
        Signal { name: "headerChanged" }
        Signal { name: "footerChanged" }
        Signal { name: "headerItemChanged" }
        Signal { name: "footerItemChanged" }
        Signal { name: "populateTransitionChanged" }
        Signal { name: "addTransitionChanged" }
        Signal { name: "addDisplacedTransitionChanged" }
        Signal { name: "moveTransitionChanged" }
        Signal { name: "moveDisplacedTransitionChanged" }
        Signal { name: "removeTransitionChanged" }
        Signal { name: "removeDisplacedTransitionChanged" }
        Signal { name: "displacedTransitionChanged" }
        Signal { name: "highlightChanged" }
        Signal { name: "highlightItemChanged" }
        Signal { name: "highlightFollowsCurrentItemChanged" }
        Signal { name: "highlightRangeModeChanged" }
        Signal { name: "preferredHighlightBeginChanged" }
        Signal { name: "preferredHighlightEndChanged" }
        Signal { name: "highlightMoveDurationChanged" }
        Signal { name: "reuseItemsChanged"; revision: 527 }
        Method { name: "destroyRemoved" }
        Method {
            name: "createdItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "initItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Method {
            name: "destroyingItem"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onItemPooled"
            revision: 527
            Parameter { name: "modelIndex"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onItemReused"
            revision: 527
            Parameter { name: "modelIndex"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method { name: "animStopped" }
        Method { name: "trackedPositionChanged" }
        Method {
            name: "positionViewAtIndex"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "mode"; type: "int" }
        }
        Method {
            name: "indexAt"
            type: "int"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "itemAt"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "itemAtIndex"
            revision: 525
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "positionViewAtBeginning" }
        Method { name: "positionViewAtEnd" }
        Method { name: "forceLayout"; revision: 513 }
    }
    Component {
        file: "private/qquickitemview_p.h"
        name: "QQuickItemViewAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "view"
            type: "QQuickItemView"
            isPointer: true
            read: "view"
            notify: "viewChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "isCurrentItem"
            type: "bool"
            read: "isCurrentItem"
            notify: "currentItemChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "delayRemove"
            type: "bool"
            read: "delayRemove"
            write: "setDelayRemove"
            notify: "delayRemoveChanged"
            index: 2
        }
        Property {
            name: "section"
            type: "QString"
            read: "section"
            notify: "sectionChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "previousSection"
            type: "QString"
            read: "prevSection"
            notify: "prevSectionChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "nextSection"
            type: "QString"
            read: "nextSection"
            notify: "nextSectionChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "viewChanged" }
        Signal { name: "currentItemChanged" }
        Signal { name: "delayRemoveChanged" }
        Signal { name: "add" }
        Signal { name: "remove" }
        Signal { name: "sectionChanged" }
        Signal { name: "prevSectionChanged" }
        Signal { name: "nextSectionChanged" }
        Signal { name: "pooled" }
        Signal { name: "reused" }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickKeyEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "key"; type: "int"; read: "key"; index: 0; isReadonly: true }
        Property { name: "text"; type: "QString"; read: "text"; index: 1; isReadonly: true }
        Property { name: "modifiers"; type: "int"; read: "modifiers"; index: 2; isReadonly: true }
        Property { name: "isAutoRepeat"; type: "bool"; read: "isAutoRepeat"; index: 3; isReadonly: true }
        Property { name: "count"; type: "int"; read: "count"; index: 4; isReadonly: true }
        Property {
            name: "nativeScanCode"
            type: "uint"
            read: "nativeScanCode"
            index: 5
            isReadonly: true
        }
        Property { name: "accepted"; type: "bool"; read: "isAccepted"; write: "setAccepted"; index: 6 }
        Method {
            name: "matches"
            revision: 514
            type: "bool"
            Parameter { name: "key"; type: "QKeySequence::StandardKey" }
        }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickKeyNavigationAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/KeyNavigation 2.0", "QtQuick/KeyNavigation 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        attachedType: "QQuickKeyNavigationAttached"
        Enum {
            name: "Priority"
            values: ["BeforeItem", "AfterItem"]
        }
        Property {
            name: "left"
            type: "QQuickItem"
            isPointer: true
            read: "left"
            write: "setLeft"
            notify: "leftChanged"
            index: 0
        }
        Property {
            name: "right"
            type: "QQuickItem"
            isPointer: true
            read: "right"
            write: "setRight"
            notify: "rightChanged"
            index: 1
        }
        Property {
            name: "up"
            type: "QQuickItem"
            isPointer: true
            read: "up"
            write: "setUp"
            notify: "upChanged"
            index: 2
        }
        Property {
            name: "down"
            type: "QQuickItem"
            isPointer: true
            read: "down"
            write: "setDown"
            notify: "downChanged"
            index: 3
        }
        Property {
            name: "tab"
            type: "QQuickItem"
            isPointer: true
            read: "tab"
            write: "setTab"
            notify: "tabChanged"
            index: 4
        }
        Property {
            name: "backtab"
            type: "QQuickItem"
            isPointer: true
            read: "backtab"
            write: "setBacktab"
            notify: "backtabChanged"
            index: 5
        }
        Property {
            name: "priority"
            type: "Priority"
            read: "priority"
            write: "setPriority"
            notify: "priorityChanged"
            index: 6
        }
        Signal { name: "leftChanged" }
        Signal { name: "rightChanged" }
        Signal { name: "upChanged" }
        Signal { name: "downChanged" }
        Signal { name: "tabChanged" }
        Signal { name: "backtabChanged" }
        Signal { name: "priorityChanged" }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickKeysAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/Keys 2.0", "QtQuick/Keys 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        attachedType: "QQuickKeysAttached"
        Enum {
            name: "Priority"
            values: ["BeforeItem", "AfterItem"]
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Property {
            name: "forwardTo"
            type: "QQuickItem"
            isList: true
            read: "forwardTo"
            index: 1
            isReadonly: true
        }
        Property {
            name: "priority"
            type: "Priority"
            read: "priority"
            write: "setPriority"
            notify: "priorityChanged"
            index: 2
        }
        Signal { name: "enabledChanged" }
        Signal { name: "priorityChanged" }
        Signal {
            name: "pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "shortcutOverride"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit0Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit1Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit2Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit3Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit4Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit5Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit6Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit7Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit8Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit9Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "leftPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "rightPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "upPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "downPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "tabPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "backtabPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "asteriskPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "numberSignPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "escapePressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "returnPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "enterPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "deletePressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "spacePressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "backPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "cancelPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "selectPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "yesPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "noPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context1Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context2Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context3Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context4Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "callPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "hangupPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "flipPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "menuPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "volumeUpPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "volumeDownPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickLayoutMirroringAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick/LayoutMirroring 2.0",
            "QtQuick/LayoutMirroring 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        attachedType: "QQuickLayoutMirroringAttached"
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Property {
            name: "childrenInherit"
            type: "bool"
            read: "childrenInherit"
            write: "setChildrenInherit"
            notify: "childrenInheritChanged"
            index: 1
        }
        Signal { name: "enabledChanged" }
        Signal { name: "childrenInheritChanged" }
    }
    Component {
        file: "private/qquicklistview_p.h"
        name: "QQuickListView"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickItemView"
        exports: [
            "QtQuick/ListView 2.0",
            "QtQuick/ListView 2.1",
            "QtQuick/ListView 2.3",
            "QtQuick/ListView 2.4",
            "QtQuick/ListView 2.7",
            "QtQuick/ListView 2.9",
            "QtQuick/ListView 2.10",
            "QtQuick/ListView 2.11",
            "QtQuick/ListView 2.12",
            "QtQuick/ListView 2.13",
            "QtQuick/ListView 2.15",
            "QtQuick/ListView 6.0"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            515,
            516,
            519,
            521,
            522,
            523,
            524,
            525,
            527,
            1536
        ]
        attachedType: "QQuickListViewAttached"
        Enum {
            name: "Orientation"
            values: ["Horizontal", "Vertical"]
        }
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapToItem", "SnapOneItem"]
        }
        Enum {
            name: "HeaderPositioning"
            values: ["InlineHeader", "OverlayHeader", "PullBackHeader"]
        }
        Enum {
            name: "FooterPositioning"
            values: ["InlineFooter", "OverlayFooter", "PullBackFooter"]
        }
        Property {
            name: "highlightMoveVelocity"
            type: "double"
            read: "highlightMoveVelocity"
            write: "setHighlightMoveVelocity"
            notify: "highlightMoveVelocityChanged"
            index: 0
        }
        Property {
            name: "highlightResizeVelocity"
            type: "double"
            read: "highlightResizeVelocity"
            write: "setHighlightResizeVelocity"
            notify: "highlightResizeVelocityChanged"
            index: 1
        }
        Property {
            name: "highlightResizeDuration"
            type: "int"
            read: "highlightResizeDuration"
            write: "setHighlightResizeDuration"
            notify: "highlightResizeDurationChanged"
            index: 2
        }
        Property {
            name: "spacing"
            type: "double"
            read: "spacing"
            write: "setSpacing"
            notify: "spacingChanged"
            index: 3
        }
        Property {
            name: "orientation"
            type: "Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 4
        }
        Property {
            name: "section"
            type: "QQuickViewSection"
            isPointer: true
            read: "sectionCriteria"
            index: 5
            isReadonly: true
        }
        Property {
            name: "currentSection"
            type: "QString"
            read: "currentSection"
            notify: "currentSectionChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "snapMode"
            type: "SnapMode"
            read: "snapMode"
            write: "setSnapMode"
            notify: "snapModeChanged"
            index: 7
        }
        Property {
            name: "headerPositioning"
            revision: 516
            type: "HeaderPositioning"
            read: "headerPositioning"
            write: "setHeaderPositioning"
            notify: "headerPositioningChanged"
            index: 8
        }
        Property {
            name: "footerPositioning"
            revision: 516
            type: "FooterPositioning"
            read: "footerPositioning"
            write: "setFooterPositioning"
            notify: "footerPositioningChanged"
            index: 9
        }
        Signal { name: "spacingChanged" }
        Signal { name: "orientationChanged" }
        Signal { name: "currentSectionChanged" }
        Signal { name: "highlightMoveVelocityChanged" }
        Signal { name: "highlightResizeVelocityChanged" }
        Signal { name: "highlightResizeDurationChanged" }
        Signal { name: "snapModeChanged" }
        Signal { name: "headerPositioningChanged"; revision: 516 }
        Signal { name: "footerPositioningChanged"; revision: 516 }
        Method { name: "incrementCurrentIndex" }
        Method { name: "decrementCurrentIndex" }
    }
    Component {
        file: "private/qquicklistview_p.h"
        name: "QQuickListViewAttached"
        accessSemantics: "reference"
        prototype: "QQuickItemViewAttached"
    }
    Component {
        file: "private/qquickloader_p.h"
        name: "QQuickLoader"
        accessSemantics: "reference"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/Loader 2.0",
            "QtQuick/Loader 2.1",
            "QtQuick/Loader 2.4",
            "QtQuick/Loader 2.7",
            "QtQuick/Loader 2.11",
            "QtQuick/Loader 6.0",
            "QtQuick/Loader 6.2"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1538]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "active"
            type: "bool"
            read: "active"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 1
        }
        Property {
            name: "sourceComponent"
            type: "QQmlComponent"
            isPointer: true
            read: "sourceComponent"
            write: "setSourceComponent"
            notify: "sourceComponentChanged"
            index: 2
        }
        Property {
            name: "item"
            type: "QObject"
            isPointer: true
            read: "item"
            notify: "itemChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            notify: "progressChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 6
        }
        Signal { name: "itemChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "sourceChanged" }
        Signal { name: "sourceComponentChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "progressChanged" }
        Signal { name: "loaded" }
        Signal { name: "asynchronousChanged" }
        Method { name: "_q_sourceLoaded" }
        Method { name: "_q_updateSize" }
        Method {
            name: "setSource"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
    }
    Component {
        file: "private/qquicktranslate_p.h"
        name: "QQuickMatrix4x4"
        accessSemantics: "reference"
        prototype: "QQuickTransform"
        exports: ["QtQuick/Matrix4x4 2.3", "QtQuick/Matrix4x4 6.0"]
        exportMetaObjectRevisions: [515, 1536]
        Property {
            name: "matrix"
            type: "QMatrix4x4"
            read: "matrix"
            write: "setMatrix"
            notify: "matrixChanged"
            index: 0
        }
        Signal { name: "matrixChanged" }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QMatrix4x4"
        accessSemantics: "value"
        extension: "QQuickMatrix4x4ValueType"
        exports: ["QtQuick/matrix4x4 2.0", "QtQuick/matrix4x4 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickMatrix4x4ValueType"
        accessSemantics: "value"
        Property { name: "m11"; type: "double"; read: "m11"; write: "setM11"; index: 0; isFinal: true }
        Property { name: "m12"; type: "double"; read: "m12"; write: "setM12"; index: 1; isFinal: true }
        Property { name: "m13"; type: "double"; read: "m13"; write: "setM13"; index: 2; isFinal: true }
        Property { name: "m14"; type: "double"; read: "m14"; write: "setM14"; index: 3; isFinal: true }
        Property { name: "m21"; type: "double"; read: "m21"; write: "setM21"; index: 4; isFinal: true }
        Property { name: "m22"; type: "double"; read: "m22"; write: "setM22"; index: 5; isFinal: true }
        Property { name: "m23"; type: "double"; read: "m23"; write: "setM23"; index: 6; isFinal: true }
        Property { name: "m24"; type: "double"; read: "m24"; write: "setM24"; index: 7; isFinal: true }
        Property { name: "m31"; type: "double"; read: "m31"; write: "setM31"; index: 8; isFinal: true }
        Property { name: "m32"; type: "double"; read: "m32"; write: "setM32"; index: 9; isFinal: true }
        Property { name: "m33"; type: "double"; read: "m33"; write: "setM33"; index: 10; isFinal: true }
        Property { name: "m34"; type: "double"; read: "m34"; write: "setM34"; index: 11; isFinal: true }
        Property { name: "m41"; type: "double"; read: "m41"; write: "setM41"; index: 12; isFinal: true }
        Property { name: "m42"; type: "double"; read: "m42"; write: "setM42"; index: 13; isFinal: true }
        Property { name: "m43"; type: "double"; read: "m43"; write: "setM43"; index: 14; isFinal: true }
        Property { name: "m44"; type: "double"; read: "m44"; write: "setM44"; index: 15; isFinal: true }
        Method {
            name: "translate"
            Parameter { name: "t"; type: "QVector3D" }
        }
        Method {
            name: "rotate"
            Parameter { name: "angle"; type: "float" }
            Parameter { name: "axis"; type: "QVector3D" }
        }
        Method {
            name: "scale"
            Parameter { name: "s"; type: "float" }
        }
        Method {
            name: "scale"
            Parameter { name: "sx"; type: "float" }
            Parameter { name: "sy"; type: "float" }
            Parameter { name: "sz"; type: "float" }
        }
        Method {
            name: "scale"
            Parameter { name: "s"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            Parameter { name: "eye"; type: "QVector3D" }
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "up"; type: "QVector3D" }
        }
        Method {
            name: "times"
            type: "QMatrix4x4"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "times"
            type: "QVector4D"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method {
            name: "times"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "times"
            type: "QMatrix4x4"
            Parameter { name: "factor"; type: "double" }
        }
        Method {
            name: "plus"
            type: "QMatrix4x4"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "minus"
            type: "QMatrix4x4"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "row"
            type: "QVector4D"
            Parameter { name: "n"; type: "int" }
        }
        Method {
            name: "column"
            type: "QVector4D"
            Parameter { name: "m"; type: "int" }
        }
        Method { name: "determinant"; type: "double" }
        Method { name: "inverted"; type: "QMatrix4x4" }
        Method { name: "transposed"; type: "QMatrix4x4" }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "m"; type: "QMatrix4x4" }
            Parameter { name: "epsilon"; type: "double" }
        }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
    }
    Component {
        file: "private/qquickmousearea_p.h"
        name: "QQuickMouseArea"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/MouseArea 2.0",
            "QtQuick/MouseArea 2.1",
            "QtQuick/MouseArea 2.4",
            "QtQuick/MouseArea 2.5",
            "QtQuick/MouseArea 2.7",
            "QtQuick/MouseArea 2.9",
            "QtQuick/MouseArea 2.11",
            "QtQuick/MouseArea 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 517, 519, 521, 523, 1536]
        Property {
            name: "mouseX"
            type: "double"
            read: "mouseX"
            notify: "mouseXChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "mouseY"
            type: "double"
            read: "mouseY"
            notify: "mouseYChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "containsMouse"
            type: "bool"
            read: "hovered"
            notify: "hoveredChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "pressed"
            notify: "pressedChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 4
        }
        Property {
            name: "scrollGestureEnabled"
            revision: 517
            type: "bool"
            read: "isScrollGestureEnabled"
            write: "setScrollGestureEnabled"
            notify: "scrollGestureEnabledChanged"
            index: 5
        }
        Property {
            name: "pressedButtons"
            type: "Qt::MouseButtons"
            read: "pressedButtons"
            notify: "pressedButtonsChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "acceptedButtons"
            type: "Qt::MouseButtons"
            read: "acceptedButtons"
            write: "setAcceptedButtons"
            notify: "acceptedButtonsChanged"
            index: 7
        }
        Property {
            name: "hoverEnabled"
            type: "bool"
            read: "hoverEnabled"
            write: "setHoverEnabled"
            notify: "hoverEnabledChanged"
            index: 8
        }
        Property {
            name: "drag"
            type: "QQuickDrag"
            isPointer: true
            read: "drag"
            index: 9
            isReadonly: true
        }
        Property {
            name: "preventStealing"
            type: "bool"
            read: "preventStealing"
            write: "setPreventStealing"
            notify: "preventStealingChanged"
            index: 10
        }
        Property {
            name: "propagateComposedEvents"
            type: "bool"
            read: "propagateComposedEvents"
            write: "setPropagateComposedEvents"
            notify: "propagateComposedEventsChanged"
            index: 11
        }
        Property {
            name: "cursorShape"
            type: "Qt::CursorShape"
            read: "cursorShape"
            write: "setCursorShape"
            notify: "cursorShapeChanged"
            index: 12
        }
        Property {
            name: "containsPress"
            revision: 516
            type: "bool"
            read: "containsPress"
            notify: "containsPressChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "pressAndHoldInterval"
            revision: 521
            type: "int"
            read: "pressAndHoldInterval"
            write: "setPressAndHoldInterval"
            notify: "pressAndHoldIntervalChanged"
            index: 14
        }
        Signal { name: "hoveredChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "enabledChanged" }
        Signal { name: "scrollGestureEnabledChanged"; revision: 517 }
        Signal { name: "pressedButtonsChanged" }
        Signal { name: "acceptedButtonsChanged" }
        Signal { name: "hoverEnabledChanged" }
        Signal { name: "cursorShapeChanged" }
        Signal {
            name: "positionChanged"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "mouseXChanged"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "mouseYChanged"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal { name: "preventStealingChanged" }
        Signal { name: "propagateComposedEventsChanged" }
        Signal {
            name: "pressed"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressAndHold"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "clicked"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "wheel"
            Parameter { name: "wheel"; type: "QQuickWheelEvent"; isPointer: true }
        }
        Signal { name: "entered" }
        Signal { name: "exited" }
        Signal { name: "canceled" }
        Signal { name: "containsPressChanged"; revision: 516 }
        Signal { name: "pressAndHoldIntervalChanged"; revision: 521 }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickMouseEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "x"; type: "double"; read: "x"; index: 0; isReadonly: true }
        Property { name: "y"; type: "double"; read: "y"; index: 1; isReadonly: true }
        Property { name: "button"; type: "int"; read: "button"; index: 2; isReadonly: true }
        Property { name: "buttons"; type: "int"; read: "buttons"; index: 3; isReadonly: true }
        Property { name: "modifiers"; type: "int"; read: "modifiers"; index: 4; isReadonly: true }
        Property { name: "source"; revision: 519; type: "int"; read: "source"; index: 5; isReadonly: true }
        Property { name: "wasHeld"; type: "bool"; read: "wasHeld"; index: 6; isReadonly: true }
        Property { name: "isClick"; type: "bool"; read: "isClick"; index: 7; isReadonly: true }
        Property { name: "accepted"; type: "bool"; read: "isAccepted"; write: "setAccepted"; index: 8 }
        Property { name: "flags"; revision: 523; type: "int"; read: "flags"; index: 9; isReadonly: true }
    }
    Component {
        file: "private/qquickmultipointhandler_p.h"
        name: "QQuickMultiPointHandler"
        accessSemantics: "reference"
        prototype: "QQuickPointerDeviceHandler"
        Property {
            name: "minimumPointCount"
            type: "int"
            read: "minimumPointCount"
            write: "setMinimumPointCount"
            notify: "minimumPointCountChanged"
            index: 0
        }
        Property {
            name: "maximumPointCount"
            type: "int"
            read: "maximumPointCount"
            write: "setMaximumPointCount"
            notify: "maximumPointCountChanged"
            index: 1
        }
        Property {
            name: "centroid"
            type: "QQuickHandlerPoint"
            read: "centroid"
            notify: "centroidChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "minimumPointCountChanged" }
        Signal { name: "maximumPointCountChanged" }
        Signal { name: "centroidChanged" }
    }
    Component {
        file: "private/qquickmultipointtoucharea_p.h"
        name: "QQuickMultiPointTouchArea"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/MultiPointTouchArea 2.0",
            "QtQuick/MultiPointTouchArea 2.1",
            "QtQuick/MultiPointTouchArea 2.4",
            "QtQuick/MultiPointTouchArea 2.7",
            "QtQuick/MultiPointTouchArea 2.11",
            "QtQuick/MultiPointTouchArea 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Property {
            name: "touchPoints"
            type: "QQuickTouchPoint"
            isList: true
            read: "touchPoints"
            index: 0
            isReadonly: true
        }
        Property {
            name: "minimumTouchPoints"
            type: "int"
            read: "minimumTouchPoints"
            write: "setMinimumTouchPoints"
            notify: "minimumTouchPointsChanged"
            index: 1
        }
        Property {
            name: "maximumTouchPoints"
            type: "int"
            read: "maximumTouchPoints"
            write: "setMaximumTouchPoints"
            notify: "maximumTouchPointsChanged"
            index: 2
        }
        Property {
            name: "mouseEnabled"
            type: "bool"
            read: "mouseEnabled"
            write: "setMouseEnabled"
            notify: "mouseEnabledChanged"
            index: 3
        }
        Signal {
            name: "pressed"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal {
            name: "updated"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal {
            name: "released"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal {
            name: "canceled"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal {
            name: "gestureStarted"
            Parameter { name: "gesture"; type: "QQuickGrabGestureEvent"; isPointer: true }
        }
        Signal {
            name: "touchUpdated"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal { name: "minimumTouchPointsChanged" }
        Signal { name: "maximumTouchPointsChanged" }
        Signal { name: "mouseEnabledChanged" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickNumberAnimation"
        accessSemantics: "reference"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "QtQuick/NumberAnimation 2.0",
            "QtQuick/NumberAnimation 2.12",
            "QtQuick/NumberAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property {
            name: "from"
            type: "double"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
        }
        Property { name: "to"; type: "double"; read: "to"; write: "setTo"; notify: "toChanged"; index: 1 }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickOpacityAnimator"
        accessSemantics: "reference"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/OpacityAnimator 2.2",
            "QtQuick/OpacityAnimator 2.12",
            "QtQuick/OpacityAnimator 6.0"
        ]
        exportMetaObjectRevisions: [514, 524, 1536]
    }
    Component {
        file: "qquickpainteditem.h"
        name: "QQuickPaintedItem"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/PaintedItem 2.0",
            "QtQuick/PaintedItem 2.1",
            "QtQuick/PaintedItem 2.4",
            "QtQuick/PaintedItem 2.7",
            "QtQuick/PaintedItem 2.11",
            "QtQuick/PaintedItem 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Enum {
            name: "RenderTarget"
            values: [
                "Image",
                "FramebufferObject",
                "InvertedYFramebufferObject"
            ]
        }
        Enum {
            name: "PerformanceHints"
            alias: "PerformanceHint"
            isFlag: true
            values: ["FastFBOResizing"]
        }
        Property {
            name: "contentsSize"
            type: "QSize"
            read: "contentsSize"
            write: "setContentsSize"
            notify: "contentsSizeChanged"
            index: 0
        }
        Property {
            name: "fillColor"
            type: "QColor"
            read: "fillColor"
            write: "setFillColor"
            notify: "fillColorChanged"
            index: 1
        }
        Property {
            name: "contentsScale"
            type: "double"
            read: "contentsScale"
            write: "setContentsScale"
            notify: "contentsScaleChanged"
            index: 2
        }
        Property {
            name: "renderTarget"
            type: "RenderTarget"
            read: "renderTarget"
            write: "setRenderTarget"
            notify: "renderTargetChanged"
            index: 3
        }
        Property {
            name: "textureSize"
            type: "QSize"
            read: "textureSize"
            write: "setTextureSize"
            notify: "textureSizeChanged"
            index: 4
        }
        Signal { name: "fillColorChanged" }
        Signal { name: "contentsSizeChanged" }
        Signal { name: "contentsScaleChanged" }
        Signal { name: "renderTargetChanged" }
        Signal { name: "textureSizeChanged" }
        Method { name: "invalidateSceneGraph" }
    }
    Component {
        file: "private/qquickpalette_p.h"
        name: "QQuickPalette"
        accessSemantics: "reference"
        prototype: "QQuickColorGroup"
        exports: ["QtQuick/Palette 6.0", "QtQuick/Palette 6.2"]
        exportMetaObjectRevisions: [1536, 1538]
        Property {
            name: "active"
            type: "QQuickColorGroup"
            isPointer: true
            read: "active"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "inactive"
            type: "QQuickColorGroup"
            isPointer: true
            read: "inactive"
            write: "setInactive"
            notify: "inactiveChanged"
            index: 1
        }
        Property {
            name: "disabled"
            type: "QQuickColorGroup"
            isPointer: true
            read: "disabled"
            write: "setDisabled"
            notify: "disabledChanged"
            index: 2
        }
        Signal { name: "activeChanged" }
        Signal { name: "inactiveChanged" }
        Signal { name: "disabledChanged" }
        Method {
            name: "setActive"
            Parameter { name: "active"; type: "QQuickColorGroup"; isPointer: true }
        }
        Method {
            name: "setInactive"
            Parameter { name: "inactive"; type: "QQuickColorGroup"; isPointer: true }
        }
        Method {
            name: "setDisabled"
            Parameter { name: "disabled"; type: "QQuickColorGroup"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickParallelAnimation"
        accessSemantics: "reference"
        defaultProperty: "animations"
        prototype: "QQuickAnimationGroup"
        exports: [
            "QtQuick/ParallelAnimation 2.0",
            "QtQuick/ParallelAnimation 2.12",
            "QtQuick/ParallelAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
    }
    Component {
        file: "private/qquickitemanimation_p.h"
        name: "QQuickParentAnimation"
        accessSemantics: "reference"
        defaultProperty: "animations"
        prototype: "QQuickAnimationGroup"
        exports: [
            "QtQuick/ParentAnimation 2.0",
            "QtQuick/ParentAnimation 2.12",
            "QtQuick/ParentAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "target"
            write: "setTargetObject"
            notify: "targetChanged"
            index: 0
        }
        Property {
            name: "newParent"
            type: "QQuickItem"
            isPointer: true
            read: "newParent"
            write: "setNewParent"
            notify: "newParentChanged"
            index: 1
        }
        Property {
            name: "via"
            type: "QQuickItem"
            isPointer: true
            read: "via"
            write: "setVia"
            notify: "viaChanged"
            index: 2
        }
        Signal { name: "targetChanged" }
        Signal { name: "newParentChanged" }
        Signal { name: "viaChanged" }
    }
    Component {
        file: "private/qquickstateoperations_p.h"
        name: "QQuickParentChange"
        accessSemantics: "reference"
        parentProperty: "parent"
        prototype: "QQuickStateOperation"
        exports: ["QtQuick/ParentChange 2.0", "QtQuick/ParentChange 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "object"
            write: "setObject"
            index: 0
        }
        Property {
            name: "parent"
            type: "QQuickItem"
            isPointer: true
            read: "parent"
            write: "setParent"
            index: 1
        }
        Property { name: "x"; type: "QQmlScriptString"; read: "x"; write: "setX"; index: 2 }
        Property { name: "y"; type: "QQmlScriptString"; read: "y"; write: "setY"; index: 3 }
        Property { name: "width"; type: "QQmlScriptString"; read: "width"; write: "setWidth"; index: 4 }
        Property { name: "height"; type: "QQmlScriptString"; read: "height"; write: "setHeight"; index: 5 }
        Property { name: "scale"; type: "QQmlScriptString"; read: "scale"; write: "setScale"; index: 6 }
        Property {
            name: "rotation"
            type: "QQmlScriptString"
            read: "rotation"
            write: "setRotation"
            index: 7
        }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPath"
        accessSemantics: "reference"
        defaultProperty: "pathElements"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick/Path 2.0", "QtQuick/Path 2.14", "QtQuick/Path 6.0"]
        exportMetaObjectRevisions: [512, 526, 1536]
        Property {
            name: "pathElements"
            type: "QQuickPathElement"
            isList: true
            read: "pathElements"
            index: 0
            isReadonly: true
        }
        Property {
            name: "startX"
            type: "double"
            read: "startX"
            write: "setStartX"
            notify: "startXChanged"
            index: 1
        }
        Property {
            name: "startY"
            type: "double"
            read: "startY"
            write: "setStartY"
            notify: "startYChanged"
            index: 2
        }
        Property {
            name: "closed"
            type: "bool"
            read: "isClosed"
            notify: "changed"
            index: 3
            isReadonly: true
        }
        Property {
            name: "scale"
            revision: 526
            type: "QSizeF"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 4
        }
        Signal { name: "changed" }
        Signal { name: "startXChanged" }
        Signal { name: "startYChanged" }
        Signal { name: "scaleChanged"; revision: 526 }
        Method { name: "processPath" }
        Method {
            name: "pointAtPercent"
            revision: 526
            type: "QPointF"
            Parameter { name: "t"; type: "double" }
        }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathAngleArc"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathAngleArc 2.11", "QtQuick/PathAngleArc 6.0"]
        exportMetaObjectRevisions: [523, 1536]
        Property {
            name: "centerX"
            type: "double"
            read: "centerX"
            write: "setCenterX"
            notify: "centerXChanged"
            index: 0
        }
        Property {
            name: "centerY"
            type: "double"
            read: "centerY"
            write: "setCenterY"
            notify: "centerYChanged"
            index: 1
        }
        Property {
            name: "radiusX"
            type: "double"
            read: "radiusX"
            write: "setRadiusX"
            notify: "radiusXChanged"
            index: 2
        }
        Property {
            name: "radiusY"
            type: "double"
            read: "radiusY"
            write: "setRadiusY"
            notify: "radiusYChanged"
            index: 3
        }
        Property {
            name: "startAngle"
            type: "double"
            read: "startAngle"
            write: "setStartAngle"
            notify: "startAngleChanged"
            index: 4
        }
        Property {
            name: "sweepAngle"
            type: "double"
            read: "sweepAngle"
            write: "setSweepAngle"
            notify: "sweepAngleChanged"
            index: 5
        }
        Property {
            name: "moveToStart"
            type: "bool"
            read: "moveToStart"
            write: "setMoveToStart"
            notify: "moveToStartChanged"
            index: 6
        }
        Signal { name: "centerXChanged" }
        Signal { name: "centerYChanged" }
        Signal { name: "radiusXChanged" }
        Signal { name: "radiusYChanged" }
        Signal { name: "startAngleChanged" }
        Signal { name: "sweepAngleChanged" }
        Signal { name: "moveToStartChanged" }
    }
    Component {
        file: "private/qquickitemanimation_p.h"
        name: "QQuickPathAnimation"
        accessSemantics: "reference"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/PathAnimation 2.0",
            "QtQuick/PathAnimation 2.12",
            "QtQuick/PathAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Enum {
            name: "Orientation"
            values: [
                "Fixed",
                "RightFirst",
                "LeftFirst",
                "BottomFirst",
                "TopFirst"
            ]
        }
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 0
        }
        Property {
            name: "easing"
            type: "QEasingCurve"
            read: "easing"
            write: "setEasing"
            notify: "easingChanged"
            index: 1
        }
        Property {
            name: "path"
            type: "QQuickPath"
            isPointer: true
            read: "path"
            write: "setPath"
            notify: "pathChanged"
            index: 2
        }
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "target"
            write: "setTargetObject"
            notify: "targetChanged"
            index: 3
        }
        Property {
            name: "orientation"
            type: "Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 4
        }
        Property {
            name: "anchorPoint"
            type: "QPointF"
            read: "anchorPoint"
            write: "setAnchorPoint"
            notify: "anchorPointChanged"
            index: 5
        }
        Property {
            name: "orientationEntryDuration"
            type: "int"
            read: "orientationEntryDuration"
            write: "setOrientationEntryDuration"
            notify: "orientationEntryDurationChanged"
            index: 6
        }
        Property {
            name: "orientationExitDuration"
            type: "int"
            read: "orientationExitDuration"
            write: "setOrientationExitDuration"
            notify: "orientationExitDurationChanged"
            index: 7
        }
        Property {
            name: "endRotation"
            type: "double"
            read: "endRotation"
            write: "setEndRotation"
            notify: "endRotationChanged"
            index: 8
        }
        Signal {
            name: "durationChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "easingChanged"
            Parameter { type: "QEasingCurve" }
        }
        Signal { name: "pathChanged" }
        Signal { name: "targetChanged" }
        Signal {
            name: "orientationChanged"
            Parameter { type: "Orientation" }
        }
        Signal {
            name: "anchorPointChanged"
            Parameter { type: "QPointF" }
        }
        Signal {
            name: "orientationEntryDurationChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "orientationExitDurationChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "endRotationChanged"
            Parameter { type: "double" }
        }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathArc"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: [
            "QtQuick/PathArc 2.0",
            "QtQuick/PathArc 2.9",
            "QtQuick/PathArc 6.0"
        ]
        exportMetaObjectRevisions: [512, 521, 1536]
        Enum {
            name: "ArcDirection"
            values: ["Clockwise", "Counterclockwise"]
        }
        Property {
            name: "radiusX"
            type: "double"
            read: "radiusX"
            write: "setRadiusX"
            notify: "radiusXChanged"
            index: 0
        }
        Property {
            name: "radiusY"
            type: "double"
            read: "radiusY"
            write: "setRadiusY"
            notify: "radiusYChanged"
            index: 1
        }
        Property {
            name: "useLargeArc"
            type: "bool"
            read: "useLargeArc"
            write: "setUseLargeArc"
            notify: "useLargeArcChanged"
            index: 2
        }
        Property {
            name: "direction"
            type: "ArcDirection"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 3
        }
        Property {
            name: "xAxisRotation"
            revision: 521
            type: "double"
            read: "xAxisRotation"
            write: "setXAxisRotation"
            notify: "xAxisRotationChanged"
            index: 4
        }
        Signal { name: "radiusXChanged" }
        Signal { name: "radiusYChanged" }
        Signal { name: "useLargeArcChanged" }
        Signal { name: "directionChanged" }
        Signal { name: "xAxisRotationChanged"; revision: 521 }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathAttribute"
        accessSemantics: "reference"
        prototype: "QQuickPathElement"
        exports: ["QtQuick/PathAttribute 2.0", "QtQuick/PathAttribute 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 1
        }
        Signal { name: "nameChanged" }
        Signal { name: "valueChanged" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathCatmullRomCurve"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathCurve 2.0", "QtQuick/PathCurve 6.0"]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathCubic"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathCubic 2.0", "QtQuick/PathCubic 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "control1X"
            type: "double"
            read: "control1X"
            write: "setControl1X"
            notify: "control1XChanged"
            index: 0
        }
        Property {
            name: "control1Y"
            type: "double"
            read: "control1Y"
            write: "setControl1Y"
            notify: "control1YChanged"
            index: 1
        }
        Property {
            name: "control2X"
            type: "double"
            read: "control2X"
            write: "setControl2X"
            notify: "control2XChanged"
            index: 2
        }
        Property {
            name: "control2Y"
            type: "double"
            read: "control2Y"
            write: "setControl2Y"
            notify: "control2YChanged"
            index: 3
        }
        Property {
            name: "relativeControl1X"
            type: "double"
            read: "relativeControl1X"
            write: "setRelativeControl1X"
            notify: "relativeControl1XChanged"
            index: 4
        }
        Property {
            name: "relativeControl1Y"
            type: "double"
            read: "relativeControl1Y"
            write: "setRelativeControl1Y"
            notify: "relativeControl1YChanged"
            index: 5
        }
        Property {
            name: "relativeControl2X"
            type: "double"
            read: "relativeControl2X"
            write: "setRelativeControl2X"
            notify: "relativeControl2XChanged"
            index: 6
        }
        Property {
            name: "relativeControl2Y"
            type: "double"
            read: "relativeControl2Y"
            write: "setRelativeControl2Y"
            notify: "relativeControl2YChanged"
            index: 7
        }
        Signal { name: "control1XChanged" }
        Signal { name: "control1YChanged" }
        Signal { name: "control2XChanged" }
        Signal { name: "control2YChanged" }
        Signal { name: "relativeControl1XChanged" }
        Signal { name: "relativeControl1YChanged" }
        Signal { name: "relativeControl2XChanged" }
        Signal { name: "relativeControl2YChanged" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathElement"
        accessSemantics: "reference"
        prototype: "QObject"
        Signal { name: "changed" }
    }
    Component {
        file: "private/qquickpathinterpolator_p.h"
        name: "QQuickPathInterpolator"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick/PathInterpolator 2.0",
            "QtQuick/PathInterpolator 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "path"
            type: "QQuickPath"
            isPointer: true
            read: "path"
            write: "setPath"
            notify: "pathChanged"
            index: 0
        }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            write: "setProgress"
            notify: "progressChanged"
            index: 1
        }
        Property { name: "x"; type: "double"; read: "x"; notify: "xChanged"; index: 2; isReadonly: true }
        Property { name: "y"; type: "double"; read: "y"; notify: "yChanged"; index: 3; isReadonly: true }
        Property {
            name: "angle"
            type: "double"
            read: "angle"
            notify: "angleChanged"
            index: 4
            isReadonly: true
        }
        Signal { name: "pathChanged" }
        Signal { name: "progressChanged" }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "angleChanged" }
        Method { name: "_q_pathUpdated" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathLine"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathLine 2.0", "QtQuick/PathLine 6.0"]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathMove"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathMove 2.9", "QtQuick/PathMove 6.0"]
        exportMetaObjectRevisions: [521, 1536]
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathMultiline"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathMultiline 2.14", "QtQuick/PathMultiline 6.0"]
        exportMetaObjectRevisions: [526, 1536]
        Property {
            name: "start"
            type: "QPointF"
            read: "start"
            notify: "startChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "paths"
            type: "QVariant"
            read: "paths"
            write: "setPaths"
            notify: "pathsChanged"
            index: 1
        }
        Signal { name: "pathsChanged" }
        Signal { name: "startChanged" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathPercent"
        accessSemantics: "reference"
        prototype: "QQuickPathElement"
        exports: ["QtQuick/PathPercent 2.0", "QtQuick/PathPercent 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 0
        }
        Signal { name: "valueChanged" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathPolyline"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathPolyline 2.14", "QtQuick/PathPolyline 6.0"]
        exportMetaObjectRevisions: [526, 1536]
        Property {
            name: "start"
            type: "QPointF"
            read: "start"
            notify: "startChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "path"
            type: "QVariant"
            read: "path"
            write: "setPath"
            notify: "pathChanged"
            index: 1
        }
        Signal { name: "pathChanged" }
        Signal { name: "startChanged" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathQuad"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathQuad 2.0", "QtQuick/PathQuad 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "controlX"
            type: "double"
            read: "controlX"
            write: "setControlX"
            notify: "controlXChanged"
            index: 0
        }
        Property {
            name: "controlY"
            type: "double"
            read: "controlY"
            write: "setControlY"
            notify: "controlYChanged"
            index: 1
        }
        Property {
            name: "relativeControlX"
            type: "double"
            read: "relativeControlX"
            write: "setRelativeControlX"
            notify: "relativeControlXChanged"
            index: 2
        }
        Property {
            name: "relativeControlY"
            type: "double"
            read: "relativeControlY"
            write: "setRelativeControlY"
            notify: "relativeControlYChanged"
            index: 3
        }
        Signal { name: "controlXChanged" }
        Signal { name: "controlYChanged" }
        Signal { name: "relativeControlXChanged" }
        Signal { name: "relativeControlYChanged" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathSvg"
        accessSemantics: "reference"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathSvg 2.0", "QtQuick/PathSvg 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "path"
            type: "QString"
            read: "path"
            write: "setPath"
            notify: "pathChanged"
            index: 0
        }
        Signal { name: "pathChanged" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathText"
        accessSemantics: "reference"
        prototype: "QQuickPathElement"
        exports: ["QtQuick/PathText 2.15", "QtQuick/PathText 6.0"]
        exportMetaObjectRevisions: [527, 1536]
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; notify: "xChanged"; index: 0 }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; notify: "yChanged"; index: 1 }
        Property {
            name: "width"
            type: "double"
            read: "width"
            notify: "changed"
            index: 2
            isReadonly: true
        }
        Property {
            name: "height"
            type: "double"
            read: "height"
            notify: "changed"
            index: 3
            isReadonly: true
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 4
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 5
        }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "textChanged" }
        Signal { name: "fontChanged" }
        Method { name: "invalidate" }
    }
    Component {
        file: "private/qquickpathview_p.h"
        name: "QQuickPathView"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/PathView 2.0",
            "QtQuick/PathView 2.1",
            "QtQuick/PathView 2.4",
            "QtQuick/PathView 2.7",
            "QtQuick/PathView 2.11",
            "QtQuick/PathView 2.13",
            "QtQuick/PathView 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 525, 1536]
        attachedType: "QQuickPathViewAttached"
        Enum {
            name: "HighlightRangeMode"
            values: ["NoHighlightRange", "ApplyRange", "StrictlyEnforceRange"]
        }
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapToItem", "SnapOneItem"]
        }
        Enum {
            name: "MovementDirection"
            values: ["Shortest", "Negative", "Positive"]
        }
        Enum {
            name: "PositionMode"
            values: ["Beginning", "Center", "End", "Contain", "SnapPosition"]
        }
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 0
        }
        Property {
            name: "path"
            type: "QQuickPath"
            isPointer: true
            read: "path"
            write: "setPath"
            notify: "pathChanged"
            index: 1
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 2
        }
        Property {
            name: "currentItem"
            type: "QQuickItem"
            isPointer: true
            read: "currentItem"
            notify: "currentItemChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "offset"
            type: "double"
            read: "offset"
            write: "setOffset"
            notify: "offsetChanged"
            index: 4
        }
        Property {
            name: "highlight"
            type: "QQmlComponent"
            isPointer: true
            read: "highlight"
            write: "setHighlight"
            notify: "highlightChanged"
            index: 5
        }
        Property {
            name: "highlightItem"
            type: "QQuickItem"
            isPointer: true
            read: "highlightItem"
            notify: "highlightItemChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "preferredHighlightBegin"
            type: "double"
            read: "preferredHighlightBegin"
            write: "setPreferredHighlightBegin"
            notify: "preferredHighlightBeginChanged"
            index: 7
        }
        Property {
            name: "preferredHighlightEnd"
            type: "double"
            read: "preferredHighlightEnd"
            write: "setPreferredHighlightEnd"
            notify: "preferredHighlightEndChanged"
            index: 8
        }
        Property {
            name: "highlightRangeMode"
            type: "HighlightRangeMode"
            read: "highlightRangeMode"
            write: "setHighlightRangeMode"
            notify: "highlightRangeModeChanged"
            index: 9
        }
        Property {
            name: "highlightMoveDuration"
            type: "int"
            read: "highlightMoveDuration"
            write: "setHighlightMoveDuration"
            notify: "highlightMoveDurationChanged"
            index: 10
        }
        Property {
            name: "dragMargin"
            type: "double"
            read: "dragMargin"
            write: "setDragMargin"
            notify: "dragMarginChanged"
            index: 11
        }
        Property {
            name: "maximumFlickVelocity"
            type: "double"
            read: "maximumFlickVelocity"
            write: "setMaximumFlickVelocity"
            notify: "maximumFlickVelocityChanged"
            index: 12
        }
        Property {
            name: "flickDeceleration"
            type: "double"
            read: "flickDeceleration"
            write: "setFlickDeceleration"
            notify: "flickDecelerationChanged"
            index: 13
        }
        Property {
            name: "interactive"
            type: "bool"
            read: "isInteractive"
            write: "setInteractive"
            notify: "interactiveChanged"
            index: 14
        }
        Property {
            name: "moving"
            type: "bool"
            read: "isMoving"
            notify: "movingChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "flicking"
            type: "bool"
            read: "isFlicking"
            notify: "flickingChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "dragging"
            type: "bool"
            read: "isDragging"
            notify: "draggingChanged"
            index: 17
            isReadonly: true
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 19
        }
        Property {
            name: "pathItemCount"
            type: "int"
            read: "pathItemCount"
            write: "setPathItemCount"
            notify: "pathItemCountChanged"
            index: 20
        }
        Property {
            name: "snapMode"
            type: "SnapMode"
            read: "snapMode"
            write: "setSnapMode"
            notify: "snapModeChanged"
            index: 21
        }
        Property {
            name: "movementDirection"
            revision: 519
            type: "MovementDirection"
            read: "movementDirection"
            write: "setMovementDirection"
            notify: "movementDirectionChanged"
            index: 22
        }
        Property {
            name: "cacheItemCount"
            type: "int"
            read: "cacheItemCount"
            write: "setCacheItemCount"
            notify: "cacheItemCountChanged"
            index: 23
        }
        Signal { name: "currentIndexChanged" }
        Signal { name: "currentItemChanged" }
        Signal { name: "offsetChanged" }
        Signal { name: "modelChanged" }
        Signal { name: "countChanged" }
        Signal { name: "pathChanged" }
        Signal { name: "preferredHighlightBeginChanged" }
        Signal { name: "preferredHighlightEndChanged" }
        Signal { name: "highlightRangeModeChanged" }
        Signal { name: "dragMarginChanged" }
        Signal { name: "snapPositionChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "pathItemCountChanged" }
        Signal { name: "maximumFlickVelocityChanged" }
        Signal { name: "flickDecelerationChanged" }
        Signal { name: "interactiveChanged" }
        Signal { name: "movingChanged" }
        Signal { name: "flickingChanged" }
        Signal { name: "draggingChanged" }
        Signal { name: "highlightChanged" }
        Signal { name: "highlightItemChanged" }
        Signal { name: "highlightMoveDurationChanged" }
        Signal { name: "movementStarted" }
        Signal { name: "movementEnded" }
        Signal { name: "movementDirectionChanged"; revision: 519 }
        Signal { name: "flickStarted" }
        Signal { name: "flickEnded" }
        Signal { name: "dragStarted" }
        Signal { name: "dragEnded" }
        Signal { name: "snapModeChanged" }
        Signal { name: "cacheItemCountChanged" }
        Method { name: "incrementCurrentIndex" }
        Method { name: "decrementCurrentIndex" }
        Method { name: "refill" }
        Method { name: "ticked" }
        Method { name: "movementEnding" }
        Method {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Method {
            name: "createdItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "initItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "destroyingItem"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method { name: "pathUpdated" }
        Method {
            name: "positionViewAtIndex"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "mode"; type: "int" }
        }
        Method {
            name: "indexAt"
            type: "int"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "itemAt"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "itemAtIndex"
            revision: 525
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qquickpathview_p.h"
        name: "QQuickPathViewAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "view"
            type: "QQuickPathView"
            isPointer: true
            read: "view"
            index: 0
            isReadonly: true
        }
        Property {
            name: "isCurrentItem"
            type: "bool"
            read: "isCurrentItem"
            notify: "currentItemChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "onPath"
            type: "bool"
            read: "isOnPath"
            notify: "pathChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "currentItemChanged" }
        Signal { name: "pathChanged" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickPauseAnimation"
        accessSemantics: "reference"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/PauseAnimation 2.0",
            "QtQuick/PauseAnimation 2.12",
            "QtQuick/PauseAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 0
        }
        Signal {
            name: "durationChanged"
            Parameter { type: "int" }
        }
    }
    Component {
        file: "private/qquickrectangle_p.h"
        name: "QQuickPen"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "width"
            type: "double"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 0
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "pixelAligned"
            type: "bool"
            read: "pixelAligned"
            write: "setPixelAligned"
            notify: "pixelAlignedChanged"
            index: 2
        }
        Signal { name: "widthChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "pixelAlignedChanged" }
    }
    Component {
        file: "private/qquickpincharea_p.h"
        name: "QQuickPinch"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/Pinch 2.0", "QtQuick/Pinch 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "Axis"
            values: ["NoDrag", "XAxis", "YAxis", "XAndYAxis", "XandYAxis"]
        }
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 0
        }
        Property {
            name: "minimumScale"
            type: "double"
            read: "minimumScale"
            write: "setMinimumScale"
            notify: "minimumScaleChanged"
            index: 1
        }
        Property {
            name: "maximumScale"
            type: "double"
            read: "maximumScale"
            write: "setMaximumScale"
            notify: "maximumScaleChanged"
            index: 2
        }
        Property {
            name: "minimumRotation"
            type: "double"
            read: "minimumRotation"
            write: "setMinimumRotation"
            notify: "minimumRotationChanged"
            index: 3
        }
        Property {
            name: "maximumRotation"
            type: "double"
            read: "maximumRotation"
            write: "setMaximumRotation"
            notify: "maximumRotationChanged"
            index: 4
        }
        Property {
            name: "dragAxis"
            type: "Axis"
            read: "axis"
            write: "setAxis"
            notify: "dragAxisChanged"
            index: 5
        }
        Property {
            name: "minimumX"
            type: "double"
            read: "xmin"
            write: "setXmin"
            notify: "minimumXChanged"
            index: 6
        }
        Property {
            name: "maximumX"
            type: "double"
            read: "xmax"
            write: "setXmax"
            notify: "maximumXChanged"
            index: 7
        }
        Property {
            name: "minimumY"
            type: "double"
            read: "ymin"
            write: "setYmin"
            notify: "minimumYChanged"
            index: 8
        }
        Property {
            name: "maximumY"
            type: "double"
            read: "ymax"
            write: "setYmax"
            notify: "maximumYChanged"
            index: 9
        }
        Property {
            name: "active"
            type: "bool"
            read: "active"
            notify: "activeChanged"
            index: 10
            isReadonly: true
        }
        Signal { name: "targetChanged" }
        Signal { name: "minimumScaleChanged" }
        Signal { name: "maximumScaleChanged" }
        Signal { name: "minimumRotationChanged" }
        Signal { name: "maximumRotationChanged" }
        Signal { name: "dragAxisChanged" }
        Signal { name: "minimumXChanged" }
        Signal { name: "maximumXChanged" }
        Signal { name: "minimumYChanged" }
        Signal { name: "maximumYChanged" }
        Signal { name: "activeChanged" }
    }
    Component {
        file: "private/qquickpincharea_p.h"
        name: "QQuickPinchArea"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/PinchArea 2.0",
            "QtQuick/PinchArea 2.1",
            "QtQuick/PinchArea 2.4",
            "QtQuick/PinchArea 2.5",
            "QtQuick/PinchArea 2.7",
            "QtQuick/PinchArea 2.11",
            "QtQuick/PinchArea 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 517, 519, 523, 1536]
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Property {
            name: "pinch"
            type: "QQuickPinch"
            isPointer: true
            read: "pinch"
            index: 1
            isReadonly: true
        }
        Signal { name: "enabledChanged" }
        Signal {
            name: "pinchStarted"
            Parameter { name: "pinch"; type: "QQuickPinchEvent"; isPointer: true }
        }
        Signal {
            name: "pinchUpdated"
            Parameter { name: "pinch"; type: "QQuickPinchEvent"; isPointer: true }
        }
        Signal {
            name: "pinchFinished"
            Parameter { name: "pinch"; type: "QQuickPinchEvent"; isPointer: true }
        }
        Signal {
            name: "smartZoom"
            revision: 517
            Parameter { name: "pinch"; type: "QQuickPinchEvent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickpincharea_p.h"
        name: "QQuickPinchEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "center"; type: "QPointF"; read: "center"; index: 0; isReadonly: true }
        Property { name: "startCenter"; type: "QPointF"; read: "startCenter"; index: 1; isReadonly: true }
        Property {
            name: "previousCenter"
            type: "QPointF"
            read: "previousCenter"
            index: 2
            isReadonly: true
        }
        Property { name: "scale"; type: "double"; read: "scale"; index: 3; isReadonly: true }
        Property {
            name: "previousScale"
            type: "double"
            read: "previousScale"
            index: 4
            isReadonly: true
        }
        Property { name: "angle"; type: "double"; read: "angle"; index: 5; isReadonly: true }
        Property {
            name: "previousAngle"
            type: "double"
            read: "previousAngle"
            index: 6
            isReadonly: true
        }
        Property { name: "rotation"; type: "double"; read: "rotation"; index: 7; isReadonly: true }
        Property { name: "point1"; type: "QPointF"; read: "point1"; index: 8; isReadonly: true }
        Property { name: "startPoint1"; type: "QPointF"; read: "startPoint1"; index: 9; isReadonly: true }
        Property { name: "point2"; type: "QPointF"; read: "point2"; index: 10; isReadonly: true }
        Property { name: "startPoint2"; type: "QPointF"; read: "startPoint2"; index: 11; isReadonly: true }
        Property { name: "pointCount"; type: "int"; read: "pointCount"; index: 12; isReadonly: true }
        Property { name: "accepted"; type: "bool"; read: "accepted"; write: "setAccepted"; index: 13 }
    }
    Component {
        file: "private/qquickpinchhandler_p.h"
        name: "QQuickPinchHandler"
        accessSemantics: "reference"
        prototype: "QQuickMultiPointHandler"
        exports: [
            "QtQuick/PinchHandler 2.12",
            "QtQuick/PinchHandler 2.15",
            "QtQuick/PinchHandler 6.0"
        ]
        exportMetaObjectRevisions: [524, 527, 1536]
        Property {
            name: "minimumScale"
            type: "double"
            read: "minimumScale"
            write: "setMinimumScale"
            notify: "minimumScaleChanged"
            index: 0
        }
        Property {
            name: "maximumScale"
            type: "double"
            read: "maximumScale"
            write: "setMaximumScale"
            notify: "maximumScaleChanged"
            index: 1
        }
        Property {
            name: "minimumRotation"
            type: "double"
            read: "minimumRotation"
            write: "setMinimumRotation"
            notify: "minimumRotationChanged"
            index: 2
        }
        Property {
            name: "maximumRotation"
            type: "double"
            read: "maximumRotation"
            write: "setMaximumRotation"
            notify: "maximumRotationChanged"
            index: 3
        }
        Property {
            name: "scale"
            type: "double"
            read: "scale"
            notify: "updated"
            index: 4
            isReadonly: true
        }
        Property {
            name: "activeScale"
            type: "double"
            read: "activeScale"
            notify: "updated"
            index: 5
            isReadonly: true
        }
        Property {
            name: "rotation"
            type: "double"
            read: "rotation"
            notify: "updated"
            index: 6
            isReadonly: true
        }
        Property {
            name: "translation"
            type: "QVector2D"
            read: "translation"
            notify: "updated"
            index: 7
            isReadonly: true
        }
        Property {
            name: "xAxis"
            type: "QQuickDragAxis"
            isPointer: true
            read: "xAxis"
            index: 8
            isReadonly: true
        }
        Property {
            name: "yAxis"
            type: "QQuickDragAxis"
            isPointer: true
            read: "yAxis"
            index: 9
            isReadonly: true
        }
        Signal { name: "minimumScaleChanged" }
        Signal { name: "maximumScaleChanged" }
        Signal { name: "minimumRotationChanged" }
        Signal { name: "maximumRotationChanged" }
        Signal { name: "updated" }
    }
    Component {
        file: "private/qquickpointhandler_p.h"
        name: "QQuickPointHandler"
        accessSemantics: "reference"
        prototype: "QQuickSinglePointHandler"
        exports: [
            "QtQuick/PointHandler 2.12",
            "QtQuick/PointHandler 2.15",
            "QtQuick/PointHandler 6.0"
        ]
        exportMetaObjectRevisions: [524, 527, 1536]
        Property {
            name: "translation"
            type: "QVector2D"
            read: "translation"
            notify: "translationChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "translationChanged" }
    }
    Component {
        file: "private/qquickpointerdevicehandler_p.h"
        name: "QQuickPointerDeviceHandler"
        accessSemantics: "reference"
        parentProperty: "parent"
        prototype: "QQuickPointerHandler"
        Property {
            name: "acceptedDevices"
            type: "QPointingDevice::DeviceTypes"
            read: "acceptedDevices"
            write: "setAcceptedDevices"
            notify: "acceptedDevicesChanged"
            index: 0
        }
        Property {
            name: "acceptedPointerTypes"
            type: "QPointingDevice::PointerTypes"
            read: "acceptedPointerTypes"
            write: "setAcceptedPointerTypes"
            notify: "acceptedPointerTypesChanged"
            index: 1
        }
        Property {
            name: "acceptedButtons"
            type: "Qt::MouseButtons"
            read: "acceptedButtons"
            write: "setAcceptedButtons"
            notify: "acceptedButtonsChanged"
            index: 2
        }
        Property {
            name: "acceptedModifiers"
            type: "Qt::KeyboardModifiers"
            read: "acceptedModifiers"
            write: "setAcceptedModifiers"
            notify: "acceptedModifiersChanged"
            index: 3
        }
        Signal { name: "acceptedDevicesChanged" }
        Signal { name: "acceptedPointerTypesChanged" }
        Signal { name: "acceptedButtonsChanged" }
        Signal { name: "acceptedModifiersChanged" }
        Method {
            name: "setAcceptedDevices"
            Parameter { name: "acceptedDevices"; type: "QPointingDevice::DeviceTypes" }
        }
        Method {
            name: "setAcceptedPointerTypes"
            Parameter { name: "acceptedPointerTypes"; type: "QPointingDevice::PointerTypes" }
        }
        Method {
            name: "setAcceptedButtons"
            Parameter { name: "buttons"; type: "Qt::MouseButtons" }
        }
        Method {
            name: "setAcceptedModifiers"
            Parameter { name: "acceptedModifiers"; type: "Qt::KeyboardModifiers" }
        }
    }
    Component {
        file: "private/qquickpointerhandler_p.h"
        name: "QQuickPointerHandler"
        accessSemantics: "reference"
        parentProperty: "parent"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick/PointerHandler 2.12",
            "QtQuick/PointerHandler 2.15",
            "QtQuick/PointerHandler 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [524, 527, 1536]
        Enum {
            name: "GrabPermissions"
            alias: "GrabPermission"
            isFlag: true
            values: [
                "TakeOverForbidden",
                "CanTakeOverFromHandlersOfSameType",
                "CanTakeOverFromHandlersOfDifferentType",
                "CanTakeOverFromItems",
                "CanTakeOverFromAnything",
                "ApprovesTakeOverByHandlersOfSameType",
                "ApprovesTakeOverByHandlersOfDifferentType",
                "ApprovesTakeOverByItems",
                "ApprovesCancellation",
                "ApprovesTakeOverByAnything"
            ]
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Property {
            name: "active"
            type: "bool"
            read: "active"
            notify: "activeChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 2
        }
        Property {
            name: "parent"
            type: "QQuickItem"
            isPointer: true
            read: "parentItem"
            index: 3
            isReadonly: true
        }
        Property {
            name: "grabPermissions"
            type: "GrabPermissions"
            read: "grabPermissions"
            write: "setGrabPermissions"
            notify: "grabPermissionChanged"
            index: 4
        }
        Property {
            name: "margin"
            type: "double"
            read: "margin"
            write: "setMargin"
            notify: "marginChanged"
            index: 5
        }
        Property {
            name: "dragThreshold"
            revision: 527
            type: "int"
            read: "dragThreshold"
            write: "setDragThreshold"
            notify: "dragThresholdChanged"
            index: 6
        }
        Property {
            name: "cursorShape"
            revision: 527
            type: "Qt::CursorShape"
            read: "cursorShape"
            write: "setCursorShape"
            notify: "cursorShapeChanged"
            index: 7
        }
        Signal { name: "enabledChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "targetChanged" }
        Signal { name: "marginChanged" }
        Signal { name: "dragThresholdChanged"; revision: 527 }
        Signal {
            name: "grabChanged"
            Parameter { name: "transition"; type: "QPointingDevice::GrabTransition" }
            Parameter { name: "point"; type: "QEventPoint" }
        }
        Signal { name: "grabPermissionChanged" }
        Signal {
            name: "canceled"
            Parameter { name: "point"; type: "QEventPoint" }
        }
        Signal { name: "cursorShapeChanged"; revision: 527 }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickPositionerAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            notify: "indexChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "isFirstItem"
            type: "bool"
            read: "isFirstItem"
            notify: "isFirstItemChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "isLastItem"
            type: "bool"
            read: "isLastItem"
            notify: "isLastItemChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "indexChanged" }
        Signal { name: "isFirstItemChanged" }
        Signal { name: "isLastItemChanged" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickPropertyAction"
        accessSemantics: "reference"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/PropertyAction 2.0",
            "QtQuick/PropertyAction 2.12",
            "QtQuick/PropertyAction 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property {
            name: "target"
            type: "QObject"
            isPointer: true
            read: "target"
            write: "setTargetObject"
            notify: "targetChanged"
            index: 0
        }
        Property {
            name: "property"
            type: "QString"
            read: "property"
            write: "setProperty"
            notify: "propertyChanged"
            index: 1
        }
        Property {
            name: "properties"
            type: "QString"
            read: "properties"
            write: "setProperties"
            notify: "propertiesChanged"
            index: 2
        }
        Property {
            name: "targets"
            type: "QObject"
            isList: true
            read: "targets"
            index: 3
            isReadonly: true
        }
        Property {
            name: "exclude"
            type: "QObject"
            isList: true
            read: "exclude"
            index: 4
            isReadonly: true
        }
        Property {
            name: "value"
            type: "QVariant"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 5
        }
        Signal {
            name: "valueChanged"
            Parameter { type: "QVariant" }
        }
        Signal {
            name: "propertiesChanged"
            Parameter { type: "QString" }
        }
        Signal { name: "targetChanged" }
        Signal { name: "propertyChanged" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickPropertyAnimation"
        accessSemantics: "reference"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/PropertyAnimation 2.0",
            "QtQuick/PropertyAnimation 2.12",
            "QtQuick/PropertyAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 0
        }
        Property {
            name: "from"
            type: "QVariant"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 1
        }
        Property { name: "to"; type: "QVariant"; read: "to"; write: "setTo"; notify: "toChanged"; index: 2 }
        Property {
            name: "easing"
            type: "QEasingCurve"
            read: "easing"
            write: "setEasing"
            notify: "easingChanged"
            index: 3
        }
        Property {
            name: "target"
            type: "QObject"
            isPointer: true
            read: "target"
            write: "setTargetObject"
            notify: "targetChanged"
            index: 4
        }
        Property {
            name: "property"
            type: "QString"
            read: "property"
            write: "setProperty"
            notify: "propertyChanged"
            index: 5
        }
        Property {
            name: "properties"
            type: "QString"
            read: "properties"
            write: "setProperties"
            notify: "propertiesChanged"
            index: 6
        }
        Property {
            name: "targets"
            type: "QObject"
            isList: true
            read: "targets"
            index: 7
            isReadonly: true
        }
        Property {
            name: "exclude"
            type: "QObject"
            isList: true
            read: "exclude"
            index: 8
            isReadonly: true
        }
        Signal {
            name: "durationChanged"
            Parameter { type: "int" }
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal {
            name: "easingChanged"
            Parameter { type: "QEasingCurve" }
        }
        Signal {
            name: "propertiesChanged"
            Parameter { type: "QString" }
        }
        Signal { name: "targetChanged" }
        Signal { name: "propertyChanged" }
    }
    Component {
        file: "private/qquickpropertychanges_p.h"
        name: "QQuickPropertyChanges"
        accessSemantics: "reference"
        prototype: "QQuickStateOperation"
        exports: [
            "QtQuick/PropertyChanges 2.0",
            "QtQuick/PropertyChanges 6.0"
        ]
        hasCustomParser: true
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "target"
            type: "QObject"
            isPointer: true
            read: "object"
            write: "setObject"
            index: 0
        }
        Property {
            name: "restoreEntryValues"
            type: "bool"
            read: "restoreEntryValues"
            write: "setRestoreEntryValues"
            index: 1
        }
        Property { name: "explicit"; type: "bool"; read: "isExplicit"; write: "setIsExplicit"; index: 2 }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuaternion"
        accessSemantics: "value"
        extension: "QQuickQuaternionValueType"
        exports: ["QtQuick/quaternion 2.0", "QtQuick/quaternion 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickQuaternionValueType"
        accessSemantics: "value"
        Property { name: "scalar"; type: "double"; read: "scalar"; write: "setScalar"; index: 0 }
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 1 }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 2 }
        Property { name: "z"; type: "double"; read: "z"; write: "setZ"; index: 3 }
        Method { name: "toString"; type: "QString" }
    }
    Component {
        file: "private/qquickrectangle_p.h"
        name: "QQuickRectangle"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Rectangle 2.0",
            "QtQuick/Rectangle 2.1",
            "QtQuick/Rectangle 2.4",
            "QtQuick/Rectangle 2.7",
            "QtQuick/Rectangle 2.11",
            "QtQuick/Rectangle 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
        }
        Property { name: "gradient"; type: "QJSValue"; read: "gradient"; write: "setGradient"; index: 1 }
        Property {
            name: "border"
            type: "QQuickPen"
            isPointer: true
            read: "border"
            index: 2
            isReadonly: true
        }
        Property {
            name: "radius"
            type: "double"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 3
        }
        Signal { name: "colorChanged" }
        Signal { name: "radiusChanged" }
        Method { name: "doUpdate" }
    }
    Component {
        file: "private/qquickrepeater_p.h"
        name: "QQuickRepeater"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Repeater 2.0",
            "QtQuick/Repeater 2.1",
            "QtQuick/Repeater 2.4",
            "QtQuick/Repeater 2.7",
            "QtQuick/Repeater 2.11",
            "QtQuick/Repeater 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 0
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "modelChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "countChanged" }
        Signal {
            name: "itemAdded"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "itemRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "createdItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "initItem"
            Parameter { type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Method {
            name: "itemAt"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qquickwindow_p.h"
        name: "QQuickRootItem"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        Method {
            name: "setWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "h"; type: "int" }
        }
    }
    Component {
        file: "private/qquicktranslate_p.h"
        name: "QQuickRotation"
        accessSemantics: "reference"
        prototype: "QQuickTransform"
        exports: ["QtQuick/Rotation 2.0", "QtQuick/Rotation 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "origin"
            type: "QVector3D"
            read: "origin"
            write: "setOrigin"
            notify: "originChanged"
            index: 0
        }
        Property {
            name: "angle"
            type: "double"
            read: "angle"
            write: "setAngle"
            notify: "angleChanged"
            index: 1
        }
        Property {
            name: "axis"
            type: "QVector3D"
            read: "axis"
            write: "setAxis"
            notify: "axisChanged"
            index: 2
        }
        Signal { name: "originChanged" }
        Signal { name: "angleChanged" }
        Signal { name: "axisChanged" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickRotationAnimation"
        accessSemantics: "reference"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "QtQuick/RotationAnimation 2.0",
            "QtQuick/RotationAnimation 2.12",
            "QtQuick/RotationAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Enum {
            name: "RotationDirection"
            values: ["Numerical", "Shortest", "Clockwise", "Counterclockwise"]
        }
        Property {
            name: "from"
            type: "double"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
        }
        Property { name: "to"; type: "double"; read: "to"; write: "setTo"; notify: "toChanged"; index: 1 }
        Property {
            name: "direction"
            type: "RotationDirection"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 2
        }
        Signal { name: "directionChanged" }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickRotationAnimator"
        accessSemantics: "reference"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/RotationAnimator 2.2",
            "QtQuick/RotationAnimator 2.12",
            "QtQuick/RotationAnimator 6.0"
        ]
        exportMetaObjectRevisions: [514, 524, 1536]
        Enum {
            name: "RotationDirection"
            values: ["Numerical", "Shortest", "Clockwise", "Counterclockwise"]
        }
        Property {
            name: "direction"
            type: "RotationDirection"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 0
        }
        Signal {
            name: "directionChanged"
            Parameter { name: "dir"; type: "RotationDirection" }
        }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickRow"
        accessSemantics: "reference"
        prototype: "QQuickBasePositioner"
        exports: [
            "QtQuick/Row 2.0",
            "QtQuick/Row 2.1",
            "QtQuick/Row 2.4",
            "QtQuick/Row 2.6",
            "QtQuick/Row 2.7",
            "QtQuick/Row 2.9",
            "QtQuick/Row 2.11",
            "QtQuick/Row 6.0",
            "QtQuick/Row 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            518,
            519,
            521,
            523,
            1536,
            1538
        ]
        Property {
            name: "layoutDirection"
            type: "Qt::LayoutDirection"
            read: "layoutDirection"
            write: "setLayoutDirection"
            notify: "layoutDirectionChanged"
            index: 0
        }
        Property {
            name: "effectiveLayoutDirection"
            type: "Qt::LayoutDirection"
            read: "effectiveLayoutDirection"
            notify: "effectiveLayoutDirectionChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "layoutDirectionChanged" }
        Signal { name: "effectiveLayoutDirectionChanged" }
    }
    Component {
        file: "private/qquicktranslate_p.h"
        name: "QQuickScale"
        accessSemantics: "reference"
        prototype: "QQuickTransform"
        exports: ["QtQuick/Scale 2.0", "QtQuick/Scale 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "origin"
            type: "QVector3D"
            read: "origin"
            write: "setOrigin"
            notify: "originChanged"
            index: 0
        }
        Property {
            name: "xScale"
            type: "double"
            read: "xScale"
            write: "setXScale"
            notify: "xScaleChanged"
            index: 1
        }
        Property {
            name: "yScale"
            type: "double"
            read: "yScale"
            write: "setYScale"
            notify: "yScaleChanged"
            index: 2
        }
        Property {
            name: "zScale"
            type: "double"
            read: "zScale"
            write: "setZScale"
            notify: "zScaleChanged"
            index: 3
        }
        Signal { name: "originChanged" }
        Signal { name: "xScaleChanged" }
        Signal { name: "yScaleChanged" }
        Signal { name: "zScaleChanged" }
        Signal { name: "scaleChanged" }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickScaleAnimator"
        accessSemantics: "reference"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/ScaleAnimator 2.2",
            "QtQuick/ScaleAnimator 2.12",
            "QtQuick/ScaleAnimator 6.0"
        ]
        exportMetaObjectRevisions: [514, 524, 1536]
    }
    Component {
        file: "private/qquickscalegrid_p_p.h"
        name: "QQuickScaleGrid"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "left"
            type: "int"
            read: "left"
            write: "setLeft"
            notify: "leftBorderChanged"
            index: 0
        }
        Property {
            name: "top"
            type: "int"
            read: "top"
            write: "setTop"
            notify: "topBorderChanged"
            index: 1
        }
        Property {
            name: "right"
            type: "int"
            read: "right"
            write: "setRight"
            notify: "rightBorderChanged"
            index: 2
        }
        Property {
            name: "bottom"
            type: "int"
            read: "bottom"
            write: "setBottom"
            notify: "bottomBorderChanged"
            index: 3
        }
        Signal { name: "borderChanged" }
        Signal { name: "leftBorderChanged" }
        Signal { name: "topBorderChanged" }
        Signal { name: "rightBorderChanged" }
        Signal { name: "bottomBorderChanged" }
    }
    Component {
        file: "private/qquickscreen_p.h"
        name: "QQuickScreen"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick/Screen 2.0",
            "QtQuick/Screen 2.3",
            "QtQuick/Screen 2.10",
            "QtQuick/Screen 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 515, 522, 1536]
        attachedType: "QQuickScreenAttached"
    }
    Component {
        file: "private/qquickscreen_p.h"
        name: "QQuickScreenAttached"
        accessSemantics: "reference"
        prototype: "QQuickScreenInfo"
        Method {
            name: "screenChanged"
            Parameter { type: "QScreen"; isPointer: true }
        }
        Method {
            name: "angleBetween"
            type: "int"
            Parameter { name: "a"; type: "int" }
            Parameter { name: "b"; type: "int" }
        }
    }
    Component {
        file: "private/qquickscreen_p.h"
        name: "QQuickScreenInfo"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick/ScreenInfo 2.3",
            "QtQuick/ScreenInfo 2.10",
            "QtQuick/ScreenInfo 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [515, 522, 1536]
        Property {
            name: "name"
            type: "QString"
            read: "name"
            notify: "nameChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "manufacturer"
            revision: 522
            type: "QString"
            read: "manufacturer"
            notify: "manufacturerChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "model"
            revision: 522
            type: "QString"
            read: "model"
            notify: "modelChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "serialNumber"
            revision: 522
            type: "QString"
            read: "serialNumber"
            notify: "serialNumberChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "width"
            type: "int"
            read: "width"
            notify: "widthChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            notify: "heightChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "desktopAvailableWidth"
            type: "int"
            read: "desktopAvailableWidth"
            notify: "desktopGeometryChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "desktopAvailableHeight"
            type: "int"
            read: "desktopAvailableHeight"
            notify: "desktopGeometryChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "logicalPixelDensity"
            type: "double"
            read: "logicalPixelDensity"
            notify: "logicalPixelDensityChanged"
            index: 8
            isReadonly: true
        }
        Property {
            name: "pixelDensity"
            type: "double"
            read: "pixelDensity"
            notify: "pixelDensityChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "devicePixelRatio"
            type: "double"
            read: "devicePixelRatio"
            notify: "devicePixelRatioChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "primaryOrientation"
            type: "Qt::ScreenOrientation"
            read: "primaryOrientation"
            notify: "primaryOrientationChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "orientation"
            type: "Qt::ScreenOrientation"
            read: "orientation"
            notify: "orientationChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "virtualX"
            revision: 515
            type: "int"
            read: "virtualX"
            notify: "virtualXChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "virtualY"
            revision: 515
            type: "int"
            read: "virtualY"
            notify: "virtualYChanged"
            index: 14
            isReadonly: true
        }
        Signal { name: "nameChanged" }
        Signal { name: "manufacturerChanged"; revision: 522 }
        Signal { name: "modelChanged"; revision: 522 }
        Signal { name: "serialNumberChanged"; revision: 522 }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
        Signal { name: "desktopGeometryChanged" }
        Signal { name: "logicalPixelDensityChanged" }
        Signal { name: "pixelDensityChanged" }
        Signal { name: "devicePixelRatioChanged" }
        Signal { name: "primaryOrientationChanged" }
        Signal { name: "orientationChanged" }
        Signal { name: "virtualXChanged"; revision: 515 }
        Signal { name: "virtualYChanged"; revision: 515 }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickScriptAction"
        accessSemantics: "reference"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/ScriptAction 2.0",
            "QtQuick/ScriptAction 2.12",
            "QtQuick/ScriptAction 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property { name: "script"; type: "QQmlScriptString"; read: "script"; write: "setScript"; index: 0 }
        Property {
            name: "scriptName"
            type: "QString"
            read: "stateChangeScriptName"
            write: "setStateChangeScriptName"
            index: 1
        }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickSequentialAnimation"
        accessSemantics: "reference"
        defaultProperty: "animations"
        prototype: "QQuickAnimationGroup"
        exports: [
            "QtQuick/SequentialAnimation 2.0",
            "QtQuick/SequentialAnimation 2.12",
            "QtQuick/SequentialAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
    }
    Component {
        file: "private/qquickshadereffect_p.h"
        name: "QQuickShaderEffect"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/ShaderEffect 2.0",
            "QtQuick/ShaderEffect 2.1",
            "QtQuick/ShaderEffect 2.4",
            "QtQuick/ShaderEffect 2.7",
            "QtQuick/ShaderEffect 2.11",
            "QtQuick/ShaderEffect 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Enum {
            name: "CullMode"
            values: ["NoCulling", "BackFaceCulling", "FrontFaceCulling"]
        }
        Enum {
            name: "Status"
            values: ["Compiled", "Uncompiled", "Error"]
        }
        Property {
            name: "fragmentShader"
            type: "QUrl"
            read: "fragmentShader"
            write: "setFragmentShader"
            notify: "fragmentShaderChanged"
            index: 0
        }
        Property {
            name: "vertexShader"
            type: "QUrl"
            read: "vertexShader"
            write: "setVertexShader"
            notify: "vertexShaderChanged"
            index: 1
        }
        Property {
            name: "blending"
            type: "bool"
            read: "blending"
            write: "setBlending"
            notify: "blendingChanged"
            index: 2
        }
        Property {
            name: "mesh"
            type: "QVariant"
            read: "mesh"
            write: "setMesh"
            notify: "meshChanged"
            index: 3
        }
        Property {
            name: "cullMode"
            type: "CullMode"
            read: "cullMode"
            write: "setCullMode"
            notify: "cullModeChanged"
            index: 4
        }
        Property {
            name: "log"
            type: "QString"
            read: "log"
            notify: "logChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "supportsAtlasTextures"
            revision: 516
            type: "bool"
            read: "supportsAtlasTextures"
            write: "setSupportsAtlasTextures"
            notify: "supportsAtlasTexturesChanged"
            index: 7
        }
        Signal { name: "fragmentShaderChanged" }
        Signal { name: "vertexShaderChanged" }
        Signal { name: "blendingChanged" }
        Signal { name: "meshChanged" }
        Signal { name: "cullModeChanged" }
        Signal { name: "logChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "supportsAtlasTexturesChanged" }
    }
    Component {
        file: "private/qquickshadereffectmesh_p.h"
        name: "QQuickShaderEffectMesh"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick/ShaderEffectMesh 2.0",
            "QtQuick/ShaderEffectMesh 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        Signal { name: "geometryChanged" }
    }
    Component {
        file: "private/qquickshadereffectsource_p.h"
        name: "QQuickShaderEffectSource"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/ShaderEffectSource 2.0",
            "QtQuick/ShaderEffectSource 2.1",
            "QtQuick/ShaderEffectSource 2.4",
            "QtQuick/ShaderEffectSource 2.6",
            "QtQuick/ShaderEffectSource 2.7",
            "QtQuick/ShaderEffectSource 2.9",
            "QtQuick/ShaderEffectSource 2.11",
            "QtQuick/ShaderEffectSource 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 518, 519, 521, 523, 1536]
        Enum {
            name: "WrapMode"
            values: [
                "ClampToEdge",
                "RepeatHorizontally",
                "RepeatVertically",
                "Repeat"
            ]
        }
        Enum {
            name: "Format"
            values: ["Alpha", "RGB", "RGBA"]
        }
        Enum {
            name: "TextureMirroring"
            values: ["NoMirroring", "MirrorHorizontally", "MirrorVertically"]
        }
        Property {
            name: "wrapMode"
            type: "WrapMode"
            read: "wrapMode"
            write: "setWrapMode"
            notify: "wrapModeChanged"
            index: 0
        }
        Property {
            name: "sourceItem"
            type: "QQuickItem"
            isPointer: true
            read: "sourceItem"
            write: "setSourceItem"
            notify: "sourceItemChanged"
            index: 1
        }
        Property {
            name: "sourceRect"
            type: "QRectF"
            read: "sourceRect"
            write: "setSourceRect"
            notify: "sourceRectChanged"
            index: 2
        }
        Property {
            name: "textureSize"
            type: "QSize"
            read: "textureSize"
            write: "setTextureSize"
            notify: "textureSizeChanged"
            index: 3
        }
        Property {
            name: "format"
            type: "Format"
            read: "format"
            write: "setFormat"
            notify: "formatChanged"
            index: 4
        }
        Property {
            name: "live"
            type: "bool"
            read: "live"
            write: "setLive"
            notify: "liveChanged"
            index: 5
        }
        Property {
            name: "hideSource"
            type: "bool"
            read: "hideSource"
            write: "setHideSource"
            notify: "hideSourceChanged"
            index: 6
        }
        Property {
            name: "mipmap"
            type: "bool"
            read: "mipmap"
            write: "setMipmap"
            notify: "mipmapChanged"
            index: 7
        }
        Property {
            name: "recursive"
            type: "bool"
            read: "recursive"
            write: "setRecursive"
            notify: "recursiveChanged"
            index: 8
        }
        Property {
            name: "textureMirroring"
            revision: 518
            type: "TextureMirroring"
            read: "textureMirroring"
            write: "setTextureMirroring"
            notify: "textureMirroringChanged"
            index: 9
        }
        Property {
            name: "samples"
            revision: 521
            type: "int"
            read: "samples"
            write: "setSamples"
            notify: "samplesChanged"
            index: 10
        }
        Signal { name: "wrapModeChanged" }
        Signal { name: "sourceItemChanged" }
        Signal { name: "sourceRectChanged" }
        Signal { name: "textureSizeChanged" }
        Signal { name: "formatChanged" }
        Signal { name: "liveChanged" }
        Signal { name: "hideSourceChanged" }
        Signal { name: "mipmapChanged" }
        Signal { name: "recursiveChanged" }
        Signal { name: "textureMirroringChanged" }
        Signal { name: "samplesChanged" }
        Signal { name: "scheduledUpdateCompleted" }
        Method {
            name: "sourceItemDestroyed"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method { name: "invalidateSceneGraph" }
        Method { name: "scheduleUpdate" }
    }
    Component {
        file: "private/qquickshortcut_p.h"
        name: "QQuickShortcut"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick/Shortcut 2.5",
            "QtQuick/Shortcut 2.6",
            "QtQuick/Shortcut 2.9",
            "QtQuick/Shortcut 6.0"
        ]
        exportMetaObjectRevisions: [517, 518, 521, 1536]
        Property {
            name: "sequence"
            type: "QVariant"
            read: "sequence"
            write: "setSequence"
            notify: "sequenceChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "sequences"
            revision: 521
            type: "QVariantList"
            read: "sequences"
            write: "setSequences"
            notify: "sequencesChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "nativeText"
            revision: 518
            type: "QString"
            read: "nativeText"
            notify: "sequenceChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "portableText"
            revision: 518
            type: "QString"
            read: "portableText"
            notify: "sequenceChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "autoRepeat"
            type: "bool"
            read: "autoRepeat"
            write: "setAutoRepeat"
            notify: "autoRepeatChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "context"
            type: "Qt::ShortcutContext"
            read: "context"
            write: "setContext"
            notify: "contextChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "sequenceChanged" }
        Signal { name: "sequencesChanged"; revision: 521 }
        Signal { name: "enabledChanged" }
        Signal { name: "autoRepeatChanged" }
        Signal { name: "contextChanged" }
        Signal { name: "activated" }
        Signal { name: "activatedAmbiguously" }
    }
    Component {
        file: "private/qquicksinglepointhandler_p.h"
        name: "QQuickSinglePointHandler"
        accessSemantics: "reference"
        prototype: "QQuickPointerDeviceHandler"
        Property {
            name: "point"
            type: "QQuickHandlerPoint"
            read: "point"
            notify: "pointChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "pointChanged" }
    }
    Component {
        file: "private/qquicksmoothedanimation_p.h"
        name: "QQuickSmoothedAnimation"
        accessSemantics: "reference"
        prototype: "QQuickNumberAnimation"
        exports: [
            "QtQuick/SmoothedAnimation 2.0",
            "QtQuick/SmoothedAnimation 2.12",
            "QtQuick/SmoothedAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Enum {
            name: "ReversingMode"
            values: ["Eased", "Immediate", "Sync"]
        }
        Property {
            name: "velocity"
            type: "double"
            read: "velocity"
            write: "setVelocity"
            notify: "velocityChanged"
            index: 0
        }
        Property {
            name: "reversingMode"
            type: "ReversingMode"
            read: "reversingMode"
            write: "setReversingMode"
            notify: "reversingModeChanged"
            index: 1
        }
        Property {
            name: "maximumEasingTime"
            type: "double"
            read: "maximumEasingTime"
            write: "setMaximumEasingTime"
            notify: "maximumEasingTimeChanged"
            index: 2
        }
        Signal { name: "velocityChanged" }
        Signal { name: "reversingModeChanged" }
        Signal { name: "maximumEasingTimeChanged" }
    }
    Component {
        file: "private/qquickspringanimation_p.h"
        name: "QQuickSpringAnimation"
        accessSemantics: "reference"
        prototype: "QQuickNumberAnimation"
        interfaces: ["QQmlPropertyValueSource"]
        exports: [
            "QtQuick/SpringAnimation 2.0",
            "QtQuick/SpringAnimation 2.12",
            "QtQuick/SpringAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property { name: "velocity"; type: "double"; read: "velocity"; write: "setVelocity"; index: 0 }
        Property { name: "spring"; type: "double"; read: "spring"; write: "setSpring"; index: 1 }
        Property { name: "damping"; type: "double"; read: "damping"; write: "setDamping"; index: 2 }
        Property { name: "epsilon"; type: "double"; read: "epsilon"; write: "setEpsilon"; index: 3 }
        Property {
            name: "modulus"
            type: "double"
            read: "modulus"
            write: "setModulus"
            notify: "modulusChanged"
            index: 4
        }
        Property {
            name: "mass"
            type: "double"
            read: "mass"
            write: "setMass"
            notify: "massChanged"
            index: 5
        }
        Signal { name: "modulusChanged" }
        Signal { name: "massChanged" }
        Signal { name: "syncChanged" }
    }
    Component {
        file: "private/qquicksprite_p.h"
        name: "QQuickSprite"
        accessSemantics: "reference"
        prototype: "QQuickStochasticState"
        exports: ["QtQuick/Sprite 2.0", "QtQuick/Sprite 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "reverse"
            type: "bool"
            read: "reverse"
            write: "setReverse"
            notify: "reverseChanged"
            index: 1
        }
        Property {
            name: "frameSync"
            type: "bool"
            read: "frameSync"
            write: "setFrameSync"
            notify: "frameSyncChanged"
            index: 2
        }
        Property {
            name: "frames"
            type: "int"
            read: "frames"
            write: "setFrames"
            notify: "frameCountChanged"
            index: 3
        }
        Property {
            name: "frameCount"
            type: "int"
            read: "frameCount"
            write: "setFrameCount"
            notify: "frameCountChanged"
            index: 4
        }
        Property {
            name: "frameHeight"
            type: "int"
            read: "frameHeight"
            write: "setFrameHeight"
            notify: "frameHeightChanged"
            index: 5
        }
        Property {
            name: "frameWidth"
            type: "int"
            read: "frameWidth"
            write: "setFrameWidth"
            notify: "frameWidthChanged"
            index: 6
        }
        Property {
            name: "frameX"
            type: "int"
            read: "frameX"
            write: "setFrameX"
            notify: "frameXChanged"
            index: 7
        }
        Property {
            name: "frameY"
            type: "int"
            read: "frameY"
            write: "setFrameY"
            notify: "frameYChanged"
            index: 8
        }
        Property {
            name: "frameRate"
            type: "double"
            read: "frameRate"
            write: "setFrameRate"
            notify: "frameRateChanged"
            index: 9
        }
        Property {
            name: "frameRateVariation"
            type: "double"
            read: "frameRateVariation"
            write: "setFrameRateVariation"
            notify: "frameRateVariationChanged"
            index: 10
        }
        Property {
            name: "frameDuration"
            type: "int"
            read: "frameDuration"
            write: "setFrameDuration"
            notify: "frameDurationChanged"
            index: 11
        }
        Property {
            name: "frameDurationVariation"
            type: "int"
            read: "frameDurationVariation"
            write: "setFrameDurationVariation"
            notify: "frameDurationVariationChanged"
            index: 12
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Signal {
            name: "frameHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "reverseChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "frameCountChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameXChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameYChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameRateChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "frameRateVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "frameDurationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameDurationVariationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameSyncChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setFrameHeight"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setReverse"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFrames"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameCount"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameRate"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFrameRateVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFrameDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameDurationVariation"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameSync"
            Parameter { name: "arg"; type: "bool" }
        }
        Method { name: "startImageLoading" }
    }
    Component {
        file: "private/qquickspritesequence_p.h"
        name: "QQuickSpriteSequence"
        accessSemantics: "reference"
        defaultProperty: "sprites"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/SpriteSequence 2.0",
            "QtQuick/SpriteSequence 2.1",
            "QtQuick/SpriteSequence 2.4",
            "QtQuick/SpriteSequence 2.7",
            "QtQuick/SpriteSequence 2.11",
            "QtQuick/SpriteSequence 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536]
        Property {
            name: "running"
            type: "bool"
            read: "running"
            write: "setRunning"
            notify: "runningChanged"
            index: 0
        }
        Property {
            name: "interpolate"
            type: "bool"
            read: "interpolate"
            write: "setInterpolate"
            notify: "interpolateChanged"
            index: 1
        }
        Property {
            name: "goalSprite"
            type: "QString"
            read: "goalSprite"
            write: "setGoalSprite"
            notify: "goalSpriteChanged"
            index: 2
        }
        Property {
            name: "currentSprite"
            type: "QString"
            read: "currentSprite"
            notify: "currentSpriteChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "sprites"
            type: "QQuickSprite"
            isList: true
            read: "sprites"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "runningChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "interpolateChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "goalSpriteChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Signal {
            name: "currentSpriteChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "jumpTo"
            Parameter { name: "sprite"; type: "QString" }
        }
        Method {
            name: "setGoalSprite"
            Parameter { name: "sprite"; type: "QString" }
        }
        Method {
            name: "setRunning"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setInterpolate"
            Parameter { name: "arg"; type: "bool" }
        }
        Method { name: "createEngine" }
    }
    Component {
        file: "private/qquickstate_p.h"
        name: "QQuickState"
        accessSemantics: "reference"
        defaultProperty: "changes"
        prototype: "QObject"
        exports: ["QtQuick/State 2.0", "QtQuick/State 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 0 }
        Property { name: "when"; type: "bool"; read: "when"; write: "setWhen"; index: 1 }
        Property { name: "extend"; type: "QString"; read: "extends"; write: "setExtends"; index: 2 }
        Property {
            name: "changes"
            type: "QQuickStateOperation"
            isList: true
            read: "changes"
            index: 3
            isReadonly: true
        }
        Signal { name: "completed" }
    }
    Component {
        file: "private/qquickstatechangescript_p.h"
        name: "QQuickStateChangeScript"
        accessSemantics: "reference"
        prototype: "QQuickStateOperation"
        exports: [
            "QtQuick/StateChangeScript 2.0",
            "QtQuick/StateChangeScript 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property { name: "script"; type: "QQmlScriptString"; read: "script"; write: "setScript"; index: 0 }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 1 }
    }
    Component {
        file: "private/qquickstategroup_p.h"
        name: "QQuickStateGroup"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick/StateGroup 2.0", "QtQuick/StateGroup 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "state"
            type: "QString"
            read: "state"
            write: "setState"
            notify: "stateChanged"
            index: 0
        }
        Property {
            name: "states"
            type: "QQuickState"
            isList: true
            read: "statesProperty"
            index: 1
            isReadonly: true
        }
        Property {
            name: "transitions"
            type: "QQuickTransition"
            isList: true
            read: "transitionsProperty"
            index: 2
            isReadonly: true
        }
        Signal {
            name: "stateChanged"
            Parameter { type: "QString" }
        }
    }
    Component {
        file: "private/qquickstate_p.h"
        name: "QQuickStateOperation"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/qquickspriteengine_p.h"
        name: "QQuickStochasticState"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "duration"
            type: "int"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 0
        }
        Property {
            name: "durationVariation"
            type: "int"
            read: "durationVariation"
            write: "setDurationVariation"
            notify: "durationVariationChanged"
            index: 1
        }
        Property {
            name: "randomStart"
            type: "bool"
            read: "randomStart"
            write: "setRandomStart"
            notify: "randomStartChanged"
            index: 2
        }
        Property {
            name: "to"
            type: "QVariantMap"
            read: "to"
            write: "setTo"
            notify: "toChanged"
            index: 3
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 4
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "arg"; type: "QString" }
        }
        Signal {
            name: "toChanged"
            Parameter { name: "arg"; type: "QVariantMap" }
        }
        Signal {
            name: "durationVariationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal { name: "entered" }
        Signal {
            name: "randomStartChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setName"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setTo"
            Parameter { name: "arg"; type: "QVariantMap" }
        }
        Method {
            name: "setDurationVariation"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setRandomStart"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquicksystempalette_p.h"
        name: "QQuickSystemPalette"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick/SystemPalette 2.0",
            "QtQuick/SystemPalette 6.0",
            "QtQuick/SystemPalette 6.2"
        ]
        exportMetaObjectRevisions: [512, 1536, 1538]
        Enum {
            name: "ColorGroup"
            values: ["Active", "Inactive", "Disabled"]
        }
        Property {
            name: "colorGroup"
            type: "QQuickSystemPalette::ColorGroup"
            read: "colorGroup"
            write: "setColorGroup"
            notify: "paletteChanged"
            index: 0
        }
        Property {
            name: "window"
            type: "QColor"
            read: "window"
            notify: "paletteChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "windowText"
            type: "QColor"
            read: "windowText"
            notify: "paletteChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "base"
            type: "QColor"
            read: "base"
            notify: "paletteChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "text"
            type: "QColor"
            read: "text"
            notify: "paletteChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "alternateBase"
            type: "QColor"
            read: "alternateBase"
            notify: "paletteChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "button"
            type: "QColor"
            read: "button"
            notify: "paletteChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "buttonText"
            type: "QColor"
            read: "buttonText"
            notify: "paletteChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "light"
            type: "QColor"
            read: "light"
            notify: "paletteChanged"
            index: 8
            isReadonly: true
        }
        Property {
            name: "midlight"
            type: "QColor"
            read: "midlight"
            notify: "paletteChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "dark"
            type: "QColor"
            read: "dark"
            notify: "paletteChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "mid"
            type: "QColor"
            read: "mid"
            notify: "paletteChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "shadow"
            type: "QColor"
            read: "shadow"
            notify: "paletteChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "highlight"
            type: "QColor"
            read: "highlight"
            notify: "paletteChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "highlightedText"
            type: "QColor"
            read: "highlightedText"
            notify: "paletteChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "placeholderText"
            revision: 1538
            type: "QColor"
            read: "placeholderText"
            notify: "paletteChanged"
            index: 15
            isReadonly: true
        }
        Signal { name: "paletteChanged" }
    }
    Component {
        file: "private/qquicktableview_p.h"
        name: "QQuickTableView"
        accessSemantics: "reference"
        defaultProperty: "flickableData"
        prototype: "QQuickFlickable"
        exports: [
            "QtQuick/TableView 2.12",
            "QtQuick/TableView 2.14",
            "QtQuick/TableView 6.0",
            "QtQuick/TableView 6.2"
        ]
        exportMetaObjectRevisions: [524, 526, 1536, 1538]
        attachedType: "QQuickTableViewAttached"
        Property {
            name: "rows"
            type: "int"
            read: "rows"
            notify: "rowsChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "columns"
            type: "int"
            read: "columns"
            notify: "columnsChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "rowSpacing"
            type: "double"
            read: "rowSpacing"
            write: "setRowSpacing"
            notify: "rowSpacingChanged"
            index: 2
        }
        Property {
            name: "columnSpacing"
            type: "double"
            read: "columnSpacing"
            write: "setColumnSpacing"
            notify: "columnSpacingChanged"
            index: 3
        }
        Property {
            name: "rowHeightProvider"
            type: "QJSValue"
            read: "rowHeightProvider"
            write: "setRowHeightProvider"
            notify: "rowHeightProviderChanged"
            index: 4
        }
        Property {
            name: "columnWidthProvider"
            type: "QJSValue"
            read: "columnWidthProvider"
            write: "setColumnWidthProvider"
            notify: "columnWidthProviderChanged"
            index: 5
        }
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 6
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 7
        }
        Property {
            name: "reuseItems"
            type: "bool"
            read: "reuseItems"
            write: "setReuseItems"
            notify: "reuseItemsChanged"
            index: 8
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            write: "setContentWidth"
            notify: "contentWidthChanged"
            index: 9
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            write: "setContentHeight"
            notify: "contentHeightChanged"
            index: 10
        }
        Property {
            name: "syncView"
            revision: 526
            type: "QQuickTableView"
            isPointer: true
            read: "syncView"
            write: "setSyncView"
            notify: "syncViewChanged"
            index: 11
        }
        Property {
            name: "syncDirection"
            revision: 526
            type: "Qt::Orientations"
            read: "syncDirection"
            write: "setSyncDirection"
            notify: "syncDirectionChanged"
            index: 12
        }
        Property {
            name: "leftColumn"
            revision: 1536
            type: "int"
            read: "leftColumn"
            notify: "leftColumnChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "rightColumn"
            revision: 1536
            type: "int"
            read: "rightColumn"
            notify: "rightColumnChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "topRow"
            revision: 1536
            type: "int"
            read: "topRow"
            notify: "topRowChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "bottomRow"
            revision: 1536
            type: "int"
            read: "bottomRow"
            notify: "bottomRowChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "selectionModel"
            revision: 1538
            type: "QItemSelectionModel"
            isPointer: true
            read: "selectionModel"
            write: "setSelectionModel"
            notify: "selectionModelChanged"
            index: 17
        }
        Signal { name: "rowsChanged" }
        Signal { name: "columnsChanged" }
        Signal { name: "rowSpacingChanged" }
        Signal { name: "columnSpacingChanged" }
        Signal { name: "rowHeightProviderChanged" }
        Signal { name: "columnWidthProviderChanged" }
        Signal { name: "modelChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "reuseItemsChanged" }
        Signal { name: "syncViewChanged"; revision: 526 }
        Signal { name: "syncDirectionChanged"; revision: 526 }
        Signal { name: "leftColumnChanged"; revision: 1536 }
        Signal { name: "rightColumnChanged"; revision: 1536 }
        Signal { name: "topRowChanged"; revision: 1536 }
        Signal { name: "bottomRowChanged"; revision: 1536 }
        Signal { name: "selectionModelChanged"; revision: 1538 }
        Method { name: "_q_componentFinalized" }
        Method { name: "forceLayout" }
        Method {
            name: "positionViewAtCell"
            Parameter { name: "cell"; type: "QPoint" }
            Parameter { name: "alignment"; type: "Qt::Alignment" }
            Parameter { name: "offset"; type: "QPointF" }
        }
        Method {
            name: "positionViewAtCell"
            Parameter { name: "cell"; type: "QPoint" }
            Parameter { name: "alignment"; type: "Qt::Alignment" }
        }
        Method {
            name: "positionViewAtCell"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "row"; type: "int" }
            Parameter { name: "alignment"; type: "Qt::Alignment" }
            Parameter { name: "offset"; type: "QPointF" }
        }
        Method {
            name: "positionViewAtCell"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "row"; type: "int" }
            Parameter { name: "alignment"; type: "Qt::Alignment" }
        }
        Method {
            name: "positionViewAtRow"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "alignment"; type: "Qt::Alignment" }
            Parameter { name: "offset"; type: "double" }
        }
        Method {
            name: "positionViewAtRow"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "alignment"; type: "Qt::Alignment" }
        }
        Method {
            name: "positionViewAtColumn"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "alignment"; type: "Qt::Alignment" }
            Parameter { name: "offset"; type: "double" }
        }
        Method {
            name: "positionViewAtColumn"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "alignment"; type: "Qt::Alignment" }
        }
        Method {
            name: "itemAtCell"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "cell"; type: "QPoint" }
        }
        Method {
            name: "itemAtCell"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "column"; type: "int" }
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "cellAtPos"
            type: "QPoint"
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "includeSpacing"; type: "bool" }
        }
        Method {
            name: "cellAtPos"
            type: "QPoint"
            Parameter { name: "position"; type: "QPointF" }
        }
        Method {
            name: "cellAtPos"
            type: "QPoint"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "includeSpacing"; type: "bool" }
        }
        Method {
            name: "cellAtPos"
            type: "QPoint"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "isColumnLoaded"
            revision: 1538
            type: "bool"
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "isRowLoaded"
            revision: 1538
            type: "bool"
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "columnWidth"
            revision: 1538
            type: "double"
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "rowHeight"
            revision: 1538
            type: "double"
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "implicitColumnWidth"
            revision: 1538
            type: "double"
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "implicitRowHeight"
            revision: 1538
            type: "double"
            Parameter { name: "row"; type: "int" }
        }
    }
    Component {
        file: "private/qquicktableview_p.h"
        name: "QQuickTableViewAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "view"
            type: "QQuickTableView"
            isPointer: true
            read: "view"
            notify: "viewChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "viewChanged" }
        Signal { name: "pooled" }
        Signal { name: "reused" }
    }
    Component {
        file: "private/qquicktaphandler_p.h"
        name: "QQuickTapHandler"
        accessSemantics: "reference"
        prototype: "QQuickSinglePointHandler"
        exports: [
            "QtQuick/TapHandler 2.12",
            "QtQuick/TapHandler 2.15",
            "QtQuick/TapHandler 6.0"
        ]
        exportMetaObjectRevisions: [524, 527, 1536]
        Enum {
            name: "GesturePolicy"
            values: ["DragThreshold", "WithinBounds", "ReleaseWithinBounds"]
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "tapCount"
            type: "int"
            read: "tapCount"
            notify: "tapCountChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "timeHeld"
            type: "double"
            read: "timeHeld"
            notify: "timeHeldChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "longPressThreshold"
            type: "double"
            read: "longPressThreshold"
            write: "setLongPressThreshold"
            notify: "longPressThresholdChanged"
            index: 3
        }
        Property {
            name: "gesturePolicy"
            type: "GesturePolicy"
            read: "gesturePolicy"
            write: "setGesturePolicy"
            notify: "gesturePolicyChanged"
            index: 4
        }
        Signal { name: "pressedChanged" }
        Signal { name: "tapCountChanged" }
        Signal { name: "timeHeldChanged" }
        Signal { name: "longPressThresholdChanged" }
        Signal { name: "gesturePolicyChanged" }
        Signal {
            name: "tapped"
            Parameter { name: "eventPoint"; type: "QEventPoint" }
            Parameter { type: "Qt::MouseButton" }
        }
        Signal {
            name: "singleTapped"
            Parameter { name: "eventPoint"; type: "QEventPoint" }
            Parameter { type: "Qt::MouseButton" }
        }
        Signal {
            name: "doubleTapped"
            Parameter { name: "eventPoint"; type: "QEventPoint" }
            Parameter { type: "Qt::MouseButton" }
        }
        Signal { name: "longPressed" }
    }
    Component {
        file: "private/qquicktext_p.h"
        name: "QQuickText"
        accessSemantics: "reference"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/Text 2.0",
            "QtQuick/Text 2.1",
            "QtQuick/Text 2.2",
            "QtQuick/Text 2.3",
            "QtQuick/Text 2.4",
            "QtQuick/Text 2.6",
            "QtQuick/Text 2.7",
            "QtQuick/Text 2.9",
            "QtQuick/Text 2.10",
            "QtQuick/Text 2.11",
            "QtQuick/Text 6.0",
            "QtQuick/Text 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            518,
            519,
            521,
            522,
            523,
            1536,
            1538
        ]
        Enum {
            name: "HAlignment"
            values: [
                "AlignLeft",
                "AlignRight",
                "AlignHCenter",
                "AlignJustify"
            ]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "TextStyle"
            values: ["Normal", "Outline", "Raised", "Sunken"]
        }
        Enum {
            name: "TextFormat"
            values: [
                "PlainText",
                "RichText",
                "MarkdownText",
                "AutoText",
                "StyledText"
            ]
        }
        Enum {
            name: "TextElideMode"
            values: ["ElideLeft", "ElideRight", "ElideMiddle", "ElideNone"]
        }
        Enum {
            name: "WrapMode"
            values: [
                "NoWrap",
                "WordWrap",
                "WrapAnywhere",
                "WrapAtWordBoundaryOrAnywhere",
                "Wrap"
            ]
        }
        Enum {
            name: "RenderType"
            values: ["QtRendering", "NativeRendering"]
        }
        Enum {
            name: "RenderTypeQuality"
            values: [
                "DefaultRenderTypeQuality",
                "LowRenderTypeQuality",
                "NormalRenderTypeQuality",
                "HighRenderTypeQuality",
                "VeryHighRenderTypeQuality"
            ]
        }
        Enum {
            name: "LineHeightMode"
            values: ["ProportionalHeight", "FixedHeight"]
        }
        Enum {
            name: "FontSizeMode"
            values: ["FixedSize", "HorizontalFit", "VerticalFit", "Fit"]
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 1
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "linkColor"
            type: "QColor"
            read: "linkColor"
            write: "setLinkColor"
            notify: "linkColorChanged"
            index: 3
        }
        Property {
            name: "style"
            type: "TextStyle"
            read: "style"
            write: "setStyle"
            notify: "styleChanged"
            index: 4
        }
        Property {
            name: "styleColor"
            type: "QColor"
            read: "styleColor"
            write: "setStyleColor"
            notify: "styleColorChanged"
            index: 5
        }
        Property {
            name: "horizontalAlignment"
            type: "HAlignment"
            read: "hAlign"
            write: "setHAlign"
            notify: "horizontalAlignmentChanged"
            index: 6
        }
        Property {
            name: "effectiveHorizontalAlignment"
            type: "HAlignment"
            read: "effectiveHAlign"
            notify: "effectiveHorizontalAlignmentChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "verticalAlignment"
            type: "VAlignment"
            read: "vAlign"
            write: "setVAlign"
            notify: "verticalAlignmentChanged"
            index: 8
        }
        Property {
            name: "wrapMode"
            type: "WrapMode"
            read: "wrapMode"
            write: "setWrapMode"
            notify: "wrapModeChanged"
            index: 9
        }
        Property {
            name: "lineCount"
            type: "int"
            read: "lineCount"
            notify: "lineCountChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "truncated"
            type: "bool"
            read: "truncated"
            notify: "truncatedChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "maximumLineCount"
            type: "int"
            read: "maximumLineCount"
            write: "setMaximumLineCount"
            notify: "maximumLineCountChanged"
            index: 12
        }
        Property {
            name: "textFormat"
            type: "TextFormat"
            read: "textFormat"
            write: "setTextFormat"
            notify: "textFormatChanged"
            index: 13
        }
        Property {
            name: "elide"
            type: "TextElideMode"
            read: "elideMode"
            write: "setElideMode"
            notify: "elideModeChanged"
            index: 14
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            notify: "contentWidthChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            notify: "contentHeightChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "paintedWidth"
            type: "double"
            read: "contentWidth"
            notify: "contentWidthChanged"
            index: 17
            isReadonly: true
        }
        Property {
            name: "paintedHeight"
            type: "double"
            read: "contentHeight"
            notify: "contentHeightChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "lineHeight"
            type: "double"
            read: "lineHeight"
            write: "setLineHeight"
            notify: "lineHeightChanged"
            index: 19
        }
        Property {
            name: "lineHeightMode"
            type: "LineHeightMode"
            read: "lineHeightMode"
            write: "setLineHeightMode"
            notify: "lineHeightModeChanged"
            index: 20
        }
        Property {
            name: "baseUrl"
            type: "QUrl"
            read: "baseUrl"
            write: "setBaseUrl"
            notify: "baseUrlChanged"
            index: 21
        }
        Property {
            name: "minimumPixelSize"
            type: "int"
            read: "minimumPixelSize"
            write: "setMinimumPixelSize"
            notify: "minimumPixelSizeChanged"
            index: 22
        }
        Property {
            name: "minimumPointSize"
            type: "int"
            read: "minimumPointSize"
            write: "setMinimumPointSize"
            notify: "minimumPointSizeChanged"
            index: 23
        }
        Property {
            name: "fontSizeMode"
            type: "FontSizeMode"
            read: "fontSizeMode"
            write: "setFontSizeMode"
            notify: "fontSizeModeChanged"
            index: 24
        }
        Property {
            name: "renderType"
            type: "RenderType"
            read: "renderType"
            write: "setRenderType"
            notify: "renderTypeChanged"
            index: 25
        }
        Property {
            name: "hoveredLink"
            revision: 514
            type: "QString"
            read: "hoveredLink"
            notify: "linkHovered"
            index: 26
            isReadonly: true
        }
        Property {
            name: "renderTypeQuality"
            revision: 1536
            type: "int"
            read: "renderTypeQuality"
            write: "setRenderTypeQuality"
            notify: "renderTypeQualityChanged"
            index: 27
        }
        Property {
            name: "padding"
            revision: 518
            type: "double"
            read: "padding"
            write: "setPadding"
            notify: "paddingChanged"
            index: 28
        }
        Property {
            name: "topPadding"
            revision: 518
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            notify: "topPaddingChanged"
            index: 29
        }
        Property {
            name: "leftPadding"
            revision: 518
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            notify: "leftPaddingChanged"
            index: 30
        }
        Property {
            name: "rightPadding"
            revision: 518
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            notify: "rightPaddingChanged"
            index: 31
        }
        Property {
            name: "bottomPadding"
            revision: 518
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            notify: "bottomPaddingChanged"
            index: 32
        }
        Property {
            name: "fontInfo"
            revision: 521
            type: "QJSValue"
            read: "fontInfo"
            notify: "fontInfoChanged"
            index: 33
            isReadonly: true
        }
        Property {
            name: "advance"
            revision: 522
            type: "QSizeF"
            read: "advance"
            notify: "contentSizeChanged"
            index: 34
            isReadonly: true
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "QString" }
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "QString" }
        }
        Signal {
            name: "linkHovered"
            revision: 514
            Parameter { name: "link"; type: "QString" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal { name: "colorChanged" }
        Signal { name: "linkColorChanged" }
        Signal {
            name: "styleChanged"
            Parameter { name: "style"; type: "QQuickText::TextStyle" }
        }
        Signal { name: "styleColorChanged" }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::VAlignment" }
        }
        Signal { name: "wrapModeChanged" }
        Signal { name: "lineCountChanged" }
        Signal { name: "truncatedChanged" }
        Signal { name: "maximumLineCountChanged" }
        Signal {
            name: "textFormatChanged"
            Parameter { name: "textFormat"; type: "QQuickText::TextFormat" }
        }
        Signal {
            name: "elideModeChanged"
            Parameter { name: "mode"; type: "QQuickText::TextElideMode" }
        }
        Signal { name: "contentSizeChanged" }
        Signal {
            name: "contentWidthChanged"
            Parameter { name: "contentWidth"; type: "double" }
        }
        Signal {
            name: "contentHeightChanged"
            Parameter { name: "contentHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightChanged"
            Parameter { name: "lineHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightModeChanged"
            Parameter { name: "mode"; type: "LineHeightMode" }
        }
        Signal { name: "fontSizeModeChanged" }
        Signal { name: "minimumPixelSizeChanged" }
        Signal { name: "minimumPointSizeChanged" }
        Signal { name: "effectiveHorizontalAlignmentChanged" }
        Signal {
            name: "lineLaidOut"
            Parameter { name: "line"; type: "QQuickTextLine"; isPointer: true }
        }
        Signal { name: "baseUrlChanged" }
        Signal { name: "renderTypeChanged" }
        Signal { name: "paddingChanged"; revision: 518 }
        Signal { name: "topPaddingChanged"; revision: 518 }
        Signal { name: "leftPaddingChanged"; revision: 518 }
        Signal { name: "rightPaddingChanged"; revision: 518 }
        Signal { name: "bottomPaddingChanged"; revision: 518 }
        Signal { name: "fontInfoChanged"; revision: 521 }
        Signal { name: "renderTypeQualityChanged"; revision: 1536 }
        Method { name: "q_updateLayout" }
        Method { name: "triggerPreprocess" }
        Method { name: "imageDownloadFinished" }
        Method { name: "forceLayout"; revision: 521 }
        Method {
            name: "linkAt"
            revision: 515
            type: "QString"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        file: "qquicktextdocument.h"
        name: "QQuickTextDocument"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/qquicktextedit_p.h"
        name: "QQuickTextEdit"
        accessSemantics: "reference"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/TextEdit 2.0",
            "QtQuick/TextEdit 2.1",
            "QtQuick/TextEdit 2.2",
            "QtQuick/TextEdit 2.3",
            "QtQuick/TextEdit 2.4",
            "QtQuick/TextEdit 2.6",
            "QtQuick/TextEdit 2.7",
            "QtQuick/TextEdit 2.10",
            "QtQuick/TextEdit 2.11",
            "QtQuick/TextEdit 6.0",
            "QtQuick/TextEdit 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            518,
            519,
            522,
            523,
            1536,
            1538
        ]
        Enum {
            name: "HAlignment"
            values: [
                "AlignLeft",
                "AlignRight",
                "AlignHCenter",
                "AlignJustify"
            ]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "TextFormat"
            values: ["PlainText", "RichText", "AutoText", "MarkdownText"]
        }
        Enum {
            name: "WrapMode"
            values: [
                "NoWrap",
                "WordWrap",
                "WrapAnywhere",
                "WrapAtWordBoundaryOrAnywhere",
                "Wrap"
            ]
        }
        Enum {
            name: "SelectionMode"
            values: ["SelectCharacters", "SelectWords"]
        }
        Enum {
            name: "RenderType"
            values: ["QtRendering", "NativeRendering"]
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "selectionColor"
            type: "QColor"
            read: "selectionColor"
            write: "setSelectionColor"
            notify: "selectionColorChanged"
            index: 2
        }
        Property {
            name: "selectedTextColor"
            type: "QColor"
            read: "selectedTextColor"
            write: "setSelectedTextColor"
            notify: "selectedTextColorChanged"
            index: 3
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 4
        }
        Property {
            name: "horizontalAlignment"
            type: "HAlignment"
            read: "hAlign"
            write: "setHAlign"
            notify: "horizontalAlignmentChanged"
            index: 5
        }
        Property {
            name: "effectiveHorizontalAlignment"
            type: "HAlignment"
            read: "effectiveHAlign"
            notify: "effectiveHorizontalAlignmentChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "verticalAlignment"
            type: "VAlignment"
            read: "vAlign"
            write: "setVAlign"
            notify: "verticalAlignmentChanged"
            index: 7
        }
        Property {
            name: "wrapMode"
            type: "WrapMode"
            read: "wrapMode"
            write: "setWrapMode"
            notify: "wrapModeChanged"
            index: 8
        }
        Property {
            name: "lineCount"
            type: "int"
            read: "lineCount"
            notify: "lineCountChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "length"
            type: "int"
            read: "length"
            notify: "textChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            notify: "contentSizeChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            notify: "contentSizeChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "paintedWidth"
            type: "double"
            read: "contentWidth"
            notify: "contentSizeChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "paintedHeight"
            type: "double"
            read: "contentHeight"
            notify: "contentSizeChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "textFormat"
            type: "TextFormat"
            read: "textFormat"
            write: "setTextFormat"
            notify: "textFormatChanged"
            index: 15
        }
        Property {
            name: "readOnly"
            type: "bool"
            read: "isReadOnly"
            write: "setReadOnly"
            notify: "readOnlyChanged"
            index: 16
        }
        Property {
            name: "cursorVisible"
            type: "bool"
            read: "isCursorVisible"
            write: "setCursorVisible"
            notify: "cursorVisibleChanged"
            index: 17
        }
        Property {
            name: "cursorPosition"
            type: "int"
            read: "cursorPosition"
            write: "setCursorPosition"
            notify: "cursorPositionChanged"
            index: 18
        }
        Property {
            name: "cursorRectangle"
            type: "QRectF"
            read: "cursorRectangle"
            notify: "cursorRectangleChanged"
            index: 19
            isReadonly: true
        }
        Property {
            name: "cursorDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "cursorDelegate"
            write: "setCursorDelegate"
            notify: "cursorDelegateChanged"
            index: 20
        }
        Property {
            name: "overwriteMode"
            type: "bool"
            read: "overwriteMode"
            write: "setOverwriteMode"
            notify: "overwriteModeChanged"
            index: 21
        }
        Property {
            name: "selectionStart"
            type: "int"
            read: "selectionStart"
            notify: "selectionStartChanged"
            index: 22
            isReadonly: true
        }
        Property {
            name: "selectionEnd"
            type: "int"
            read: "selectionEnd"
            notify: "selectionEndChanged"
            index: 23
            isReadonly: true
        }
        Property {
            name: "selectedText"
            type: "QString"
            read: "selectedText"
            notify: "selectedTextChanged"
            index: 24
            isReadonly: true
        }
        Property {
            name: "activeFocusOnPress"
            type: "bool"
            read: "focusOnPress"
            write: "setFocusOnPress"
            notify: "activeFocusOnPressChanged"
            index: 25
        }
        Property {
            name: "persistentSelection"
            type: "bool"
            read: "persistentSelection"
            write: "setPersistentSelection"
            notify: "persistentSelectionChanged"
            index: 26
        }
        Property {
            name: "textMargin"
            type: "double"
            read: "textMargin"
            write: "setTextMargin"
            notify: "textMarginChanged"
            index: 27
        }
        Property {
            name: "inputMethodHints"
            type: "Qt::InputMethodHints"
            read: "inputMethodHints"
            write: "setInputMethodHints"
            notify: "inputMethodHintsChanged"
            index: 28
        }
        Property {
            name: "selectByKeyboard"
            revision: 513
            type: "bool"
            read: "selectByKeyboard"
            write: "setSelectByKeyboard"
            notify: "selectByKeyboardChanged"
            index: 29
        }
        Property {
            name: "selectByMouse"
            type: "bool"
            read: "selectByMouse"
            write: "setSelectByMouse"
            notify: "selectByMouseChanged"
            index: 30
        }
        Property {
            name: "mouseSelectionMode"
            type: "SelectionMode"
            read: "mouseSelectionMode"
            write: "setMouseSelectionMode"
            notify: "mouseSelectionModeChanged"
            index: 31
        }
        Property {
            name: "canPaste"
            type: "bool"
            read: "canPaste"
            notify: "canPasteChanged"
            index: 32
            isReadonly: true
        }
        Property {
            name: "canUndo"
            type: "bool"
            read: "canUndo"
            notify: "canUndoChanged"
            index: 33
            isReadonly: true
        }
        Property {
            name: "canRedo"
            type: "bool"
            read: "canRedo"
            notify: "canRedoChanged"
            index: 34
            isReadonly: true
        }
        Property {
            name: "inputMethodComposing"
            type: "bool"
            read: "isInputMethodComposing"
            notify: "inputMethodComposingChanged"
            index: 35
            isReadonly: true
        }
        Property {
            name: "baseUrl"
            type: "QUrl"
            read: "baseUrl"
            write: "setBaseUrl"
            notify: "baseUrlChanged"
            index: 36
        }
        Property {
            name: "renderType"
            type: "RenderType"
            read: "renderType"
            write: "setRenderType"
            notify: "renderTypeChanged"
            index: 37
        }
        Property {
            name: "textDocument"
            revision: 513
            type: "QQuickTextDocument"
            isPointer: true
            read: "textDocument"
            index: 38
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hoveredLink"
            revision: 514
            type: "QString"
            read: "hoveredLink"
            notify: "linkHovered"
            index: 39
            isReadonly: true
        }
        Property {
            name: "padding"
            revision: 518
            type: "double"
            read: "padding"
            write: "setPadding"
            notify: "paddingChanged"
            index: 40
        }
        Property {
            name: "topPadding"
            revision: 518
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            notify: "topPaddingChanged"
            index: 41
        }
        Property {
            name: "leftPadding"
            revision: 518
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            notify: "leftPaddingChanged"
            index: 42
        }
        Property {
            name: "rightPadding"
            revision: 518
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            notify: "rightPaddingChanged"
            index: 43
        }
        Property {
            name: "bottomPadding"
            revision: 518
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            notify: "bottomPaddingChanged"
            index: 44
        }
        Property {
            name: "preeditText"
            revision: 519
            type: "QString"
            read: "preeditText"
            notify: "preeditTextChanged"
            index: 45
            isReadonly: true
        }
        Property {
            name: "tabStopDistance"
            revision: 522
            type: "double"
            read: "tabStopDistance"
            write: "setTabStopDistance"
            notify: "tabStopDistanceChanged"
            index: 46
        }
        Signal { name: "textChanged" }
        Signal { name: "preeditTextChanged"; revision: 519 }
        Signal { name: "contentSizeChanged" }
        Signal { name: "cursorPositionChanged" }
        Signal { name: "cursorRectangleChanged" }
        Signal { name: "selectionStartChanged" }
        Signal { name: "selectionEndChanged" }
        Signal { name: "selectedTextChanged" }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectionColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectedTextColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextEdit::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextEdit::VAlignment" }
        }
        Signal { name: "wrapModeChanged" }
        Signal { name: "lineCountChanged" }
        Signal {
            name: "textFormatChanged"
            Parameter { name: "textFormat"; type: "QQuickTextEdit::TextFormat" }
        }
        Signal {
            name: "readOnlyChanged"
            Parameter { name: "isReadOnly"; type: "bool" }
        }
        Signal {
            name: "cursorVisibleChanged"
            Parameter { name: "isCursorVisible"; type: "bool" }
        }
        Signal { name: "cursorDelegateChanged" }
        Signal {
            name: "overwriteModeChanged"
            Parameter { name: "overwriteMode"; type: "bool" }
        }
        Signal {
            name: "activeFocusOnPressChanged"
            Parameter { name: "activeFocusOnPressed"; type: "bool" }
        }
        Signal {
            name: "persistentSelectionChanged"
            Parameter { name: "isPersistentSelection"; type: "bool" }
        }
        Signal {
            name: "textMarginChanged"
            Parameter { name: "textMargin"; type: "double" }
        }
        Signal {
            name: "selectByKeyboardChanged"
            revision: 513
            Parameter { name: "selectByKeyboard"; type: "bool" }
        }
        Signal {
            name: "selectByMouseChanged"
            Parameter { name: "selectByMouse"; type: "bool" }
        }
        Signal {
            name: "mouseSelectionModeChanged"
            Parameter { name: "mode"; type: "QQuickTextEdit::SelectionMode" }
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "QString" }
        }
        Signal {
            name: "linkHovered"
            revision: 514
            Parameter { name: "link"; type: "QString" }
        }
        Signal { name: "canPasteChanged" }
        Signal { name: "canUndoChanged" }
        Signal { name: "canRedoChanged" }
        Signal { name: "inputMethodComposingChanged" }
        Signal { name: "effectiveHorizontalAlignmentChanged" }
        Signal { name: "baseUrlChanged" }
        Signal { name: "inputMethodHintsChanged" }
        Signal { name: "renderTypeChanged" }
        Signal { name: "editingFinished"; revision: 518 }
        Signal { name: "paddingChanged"; revision: 518 }
        Signal { name: "topPaddingChanged"; revision: 518 }
        Signal { name: "leftPaddingChanged"; revision: 518 }
        Signal { name: "rightPaddingChanged"; revision: 518 }
        Signal { name: "bottomPaddingChanged"; revision: 518 }
        Signal {
            name: "tabStopDistanceChanged"
            revision: 522
            Parameter { name: "distance"; type: "double" }
        }
        Method { name: "selectAll" }
        Method { name: "selectWord" }
        Method {
            name: "select"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "deselect" }
        Method {
            name: "isRightToLeft"
            type: "bool"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "cut" }
        Method { name: "copy" }
        Method { name: "paste" }
        Method { name: "undo" }
        Method { name: "redo" }
        Method {
            name: "insert"
            Parameter { name: "position"; type: "int" }
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "remove"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "append"
            revision: 514
            Parameter { name: "text"; type: "QString" }
        }
        Method { name: "clear"; revision: 519 }
        Method { name: "q_textChanged" }
        Method {
            name: "q_contentsChange"
            Parameter { type: "int" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method { name: "updateSelection" }
        Method { name: "moveCursorDelegate" }
        Method { name: "createCursor" }
        Method { name: "q_canPasteChanged" }
        Method { name: "updateWholeDocument" }
        Method {
            name: "invalidateBlock"
            Parameter { name: "block"; type: "QTextBlock" }
        }
        Method { name: "updateCursor" }
        Method {
            name: "q_linkHovered"
            Parameter { name: "link"; type: "QString" }
        }
        Method {
            name: "q_markerHovered"
            Parameter { name: "hovered"; type: "bool" }
        }
        Method { name: "q_updateAlignment" }
        Method { name: "updateSize" }
        Method { name: "triggerPreprocess" }
        Method {
            name: "inputMethodQuery"
            revision: 516
            type: "QVariant"
            Parameter { name: "query"; type: "Qt::InputMethodQuery" }
            Parameter { name: "argument"; type: "QVariant" }
        }
        Method {
            name: "positionToRectangle"
            type: "QRectF"
            Parameter { type: "int" }
        }
        Method {
            name: "positionAt"
            type: "int"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
            Parameter { name: "mode"; type: "SelectionMode" }
        }
        Method {
            name: "getText"
            type: "QString"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "getFormattedText"
            type: "QString"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "linkAt"
            revision: 515
            type: "QString"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        file: "private/qquicktextinput_p.h"
        name: "QQuickTextInput"
        accessSemantics: "reference"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/TextInput 2.0",
            "QtQuick/TextInput 2.1",
            "QtQuick/TextInput 2.2",
            "QtQuick/TextInput 2.4",
            "QtQuick/TextInput 2.6",
            "QtQuick/TextInput 2.7",
            "QtQuick/TextInput 2.9",
            "QtQuick/TextInput 2.11",
            "QtQuick/TextInput 6.0",
            "QtQuick/TextInput 6.2"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            516,
            518,
            519,
            521,
            523,
            1536,
            1538
        ]
        Enum {
            name: "EchoMode"
            values: ["Normal", "NoEcho", "Password", "PasswordEchoOnEdit"]
        }
        Enum {
            name: "HAlignment"
            values: ["AlignLeft", "AlignRight", "AlignHCenter"]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "WrapMode"
            values: [
                "NoWrap",
                "WordWrap",
                "WrapAnywhere",
                "WrapAtWordBoundaryOrAnywhere",
                "Wrap"
            ]
        }
        Enum {
            name: "SelectionMode"
            values: ["SelectCharacters", "SelectWords"]
        }
        Enum {
            name: "CursorPosition"
            values: ["CursorBetweenCharacters", "CursorOnCharacter"]
        }
        Enum {
            name: "RenderType"
            values: ["QtRendering", "NativeRendering"]
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
        }
        Property {
            name: "length"
            type: "int"
            read: "length"
            notify: "textChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "selectionColor"
            type: "QColor"
            read: "selectionColor"
            write: "setSelectionColor"
            notify: "selectionColorChanged"
            index: 3
        }
        Property {
            name: "selectedTextColor"
            type: "QColor"
            read: "selectedTextColor"
            write: "setSelectedTextColor"
            notify: "selectedTextColorChanged"
            index: 4
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 5
        }
        Property {
            name: "horizontalAlignment"
            type: "HAlignment"
            read: "hAlign"
            write: "setHAlign"
            notify: "horizontalAlignmentChanged"
            index: 6
        }
        Property {
            name: "effectiveHorizontalAlignment"
            type: "HAlignment"
            read: "effectiveHAlign"
            notify: "effectiveHorizontalAlignmentChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "verticalAlignment"
            type: "VAlignment"
            read: "vAlign"
            write: "setVAlign"
            notify: "verticalAlignmentChanged"
            index: 8
        }
        Property {
            name: "wrapMode"
            type: "WrapMode"
            read: "wrapMode"
            write: "setWrapMode"
            notify: "wrapModeChanged"
            index: 9
        }
        Property {
            name: "readOnly"
            type: "bool"
            read: "isReadOnly"
            write: "setReadOnly"
            notify: "readOnlyChanged"
            index: 10
        }
        Property {
            name: "cursorVisible"
            type: "bool"
            read: "isCursorVisible"
            write: "setCursorVisible"
            notify: "cursorVisibleChanged"
            index: 11
        }
        Property {
            name: "cursorPosition"
            type: "int"
            read: "cursorPosition"
            write: "setCursorPosition"
            notify: "cursorPositionChanged"
            index: 12
        }
        Property {
            name: "cursorRectangle"
            type: "QRectF"
            read: "cursorRectangle"
            notify: "cursorRectangleChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "cursorDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "cursorDelegate"
            write: "setCursorDelegate"
            notify: "cursorDelegateChanged"
            index: 14
        }
        Property {
            name: "overwriteMode"
            type: "bool"
            read: "overwriteMode"
            write: "setOverwriteMode"
            notify: "overwriteModeChanged"
            index: 15
        }
        Property {
            name: "selectionStart"
            type: "int"
            read: "selectionStart"
            notify: "selectionStartChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "selectionEnd"
            type: "int"
            read: "selectionEnd"
            notify: "selectionEndChanged"
            index: 17
            isReadonly: true
        }
        Property {
            name: "selectedText"
            type: "QString"
            read: "selectedText"
            notify: "selectedTextChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "maximumLength"
            type: "int"
            read: "maxLength"
            write: "setMaxLength"
            notify: "maximumLengthChanged"
            index: 19
        }
        Property {
            name: "validator"
            type: "QValidator"
            isPointer: true
            read: "validator"
            write: "setValidator"
            notify: "validatorChanged"
            index: 20
        }
        Property {
            name: "inputMask"
            type: "QString"
            read: "inputMask"
            write: "setInputMask"
            notify: "inputMaskChanged"
            index: 21
        }
        Property {
            name: "inputMethodHints"
            type: "Qt::InputMethodHints"
            read: "inputMethodHints"
            write: "setInputMethodHints"
            notify: "inputMethodHintsChanged"
            index: 22
        }
        Property {
            name: "acceptableInput"
            type: "bool"
            read: "hasAcceptableInput"
            notify: "acceptableInputChanged"
            index: 23
            isReadonly: true
        }
        Property {
            name: "echoMode"
            type: "EchoMode"
            read: "echoMode"
            write: "setEchoMode"
            notify: "echoModeChanged"
            index: 24
        }
        Property {
            name: "activeFocusOnPress"
            type: "bool"
            read: "focusOnPress"
            write: "setFocusOnPress"
            notify: "activeFocusOnPressChanged"
            index: 25
        }
        Property {
            name: "passwordCharacter"
            type: "QString"
            read: "passwordCharacter"
            write: "setPasswordCharacter"
            notify: "passwordCharacterChanged"
            index: 26
        }
        Property {
            name: "passwordMaskDelay"
            revision: 516
            type: "int"
            read: "passwordMaskDelay"
            write: "setPasswordMaskDelay"
            notify: "passwordMaskDelayChanged"
            index: 27
        }
        Property {
            name: "displayText"
            type: "QString"
            read: "displayText"
            notify: "displayTextChanged"
            index: 28
            isReadonly: true
        }
        Property {
            name: "preeditText"
            revision: 519
            type: "QString"
            read: "preeditText"
            notify: "preeditTextChanged"
            index: 29
            isReadonly: true
        }
        Property {
            name: "autoScroll"
            type: "bool"
            read: "autoScroll"
            write: "setAutoScroll"
            notify: "autoScrollChanged"
            index: 30
        }
        Property {
            name: "selectByMouse"
            type: "bool"
            read: "selectByMouse"
            write: "setSelectByMouse"
            notify: "selectByMouseChanged"
            index: 31
        }
        Property {
            name: "mouseSelectionMode"
            type: "SelectionMode"
            read: "mouseSelectionMode"
            write: "setMouseSelectionMode"
            notify: "mouseSelectionModeChanged"
            index: 32
        }
        Property {
            name: "persistentSelection"
            type: "bool"
            read: "persistentSelection"
            write: "setPersistentSelection"
            notify: "persistentSelectionChanged"
            index: 33
        }
        Property {
            name: "canPaste"
            type: "bool"
            read: "canPaste"
            notify: "canPasteChanged"
            index: 34
            isReadonly: true
        }
        Property {
            name: "canUndo"
            type: "bool"
            read: "canUndo"
            notify: "canUndoChanged"
            index: 35
            isReadonly: true
        }
        Property {
            name: "canRedo"
            type: "bool"
            read: "canRedo"
            notify: "canRedoChanged"
            index: 36
            isReadonly: true
        }
        Property {
            name: "inputMethodComposing"
            type: "bool"
            read: "isInputMethodComposing"
            notify: "inputMethodComposingChanged"
            index: 37
            isReadonly: true
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            notify: "contentSizeChanged"
            index: 38
            isReadonly: true
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            notify: "contentSizeChanged"
            index: 39
            isReadonly: true
        }
        Property {
            name: "renderType"
            type: "RenderType"
            read: "renderType"
            write: "setRenderType"
            notify: "renderTypeChanged"
            index: 40
        }
        Property {
            name: "padding"
            revision: 518
            type: "double"
            read: "padding"
            write: "setPadding"
            notify: "paddingChanged"
            index: 41
        }
        Property {
            name: "topPadding"
            revision: 518
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            notify: "topPaddingChanged"
            index: 42
        }
        Property {
            name: "leftPadding"
            revision: 518
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            notify: "leftPaddingChanged"
            index: 43
        }
        Property {
            name: "rightPadding"
            revision: 518
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            notify: "rightPaddingChanged"
            index: 44
        }
        Property {
            name: "bottomPadding"
            revision: 518
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            notify: "bottomPaddingChanged"
            index: 45
        }
        Signal { name: "textChanged" }
        Signal { name: "cursorPositionChanged" }
        Signal { name: "cursorRectangleChanged" }
        Signal { name: "selectionStartChanged" }
        Signal { name: "selectionEndChanged" }
        Signal { name: "selectedTextChanged" }
        Signal { name: "accepted" }
        Signal { name: "acceptableInputChanged" }
        Signal { name: "editingFinished"; revision: 514 }
        Signal { name: "textEdited"; revision: 521 }
        Signal { name: "colorChanged" }
        Signal { name: "selectionColorChanged" }
        Signal { name: "selectedTextColorChanged" }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextInput::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextInput::VAlignment" }
        }
        Signal { name: "wrapModeChanged" }
        Signal {
            name: "readOnlyChanged"
            Parameter { name: "isReadOnly"; type: "bool" }
        }
        Signal {
            name: "cursorVisibleChanged"
            Parameter { name: "isCursorVisible"; type: "bool" }
        }
        Signal { name: "cursorDelegateChanged" }
        Signal {
            name: "overwriteModeChanged"
            Parameter { name: "overwriteMode"; type: "bool" }
        }
        Signal {
            name: "maximumLengthChanged"
            Parameter { name: "maximumLength"; type: "int" }
        }
        Signal { name: "validatorChanged" }
        Signal {
            name: "inputMaskChanged"
            Parameter { name: "inputMask"; type: "QString" }
        }
        Signal {
            name: "echoModeChanged"
            Parameter { name: "echoMode"; type: "QQuickTextInput::EchoMode" }
        }
        Signal { name: "passwordCharacterChanged" }
        Signal {
            name: "passwordMaskDelayChanged"
            revision: 516
            Parameter { name: "delay"; type: "int" }
        }
        Signal { name: "displayTextChanged" }
        Signal { name: "preeditTextChanged"; revision: 519 }
        Signal {
            name: "activeFocusOnPressChanged"
            Parameter { name: "activeFocusOnPress"; type: "bool" }
        }
        Signal {
            name: "autoScrollChanged"
            Parameter { name: "autoScroll"; type: "bool" }
        }
        Signal {
            name: "selectByMouseChanged"
            Parameter { name: "selectByMouse"; type: "bool" }
        }
        Signal {
            name: "mouseSelectionModeChanged"
            Parameter { name: "mode"; type: "QQuickTextInput::SelectionMode" }
        }
        Signal { name: "persistentSelectionChanged" }
        Signal { name: "canPasteChanged" }
        Signal { name: "canUndoChanged" }
        Signal { name: "canRedoChanged" }
        Signal { name: "inputMethodComposingChanged" }
        Signal { name: "effectiveHorizontalAlignmentChanged" }
        Signal { name: "contentSizeChanged" }
        Signal { name: "inputMethodHintsChanged" }
        Signal { name: "renderTypeChanged" }
        Signal { name: "paddingChanged"; revision: 518 }
        Signal { name: "topPaddingChanged"; revision: 518 }
        Signal { name: "leftPaddingChanged"; revision: 518 }
        Signal { name: "rightPaddingChanged"; revision: 518 }
        Signal { name: "bottomPaddingChanged"; revision: 518 }
        Method { name: "selectAll" }
        Method { name: "selectWord" }
        Method {
            name: "select"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "deselect" }
        Method {
            name: "isRightToLeft"
            type: "bool"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "cut" }
        Method { name: "copy" }
        Method { name: "paste" }
        Method { name: "undo" }
        Method { name: "redo" }
        Method {
            name: "insert"
            Parameter { name: "position"; type: "int" }
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "remove"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "ensureVisible"
            revision: 516
            Parameter { name: "position"; type: "int" }
        }
        Method { name: "clear"; revision: 519 }
        Method { name: "selectionChanged" }
        Method { name: "createCursor" }
        Method {
            name: "updateCursorRectangle"
            Parameter { name: "scroll"; type: "bool" }
        }
        Method { name: "updateCursorRectangle" }
        Method { name: "q_canPasteChanged" }
        Method { name: "q_updateAlignment" }
        Method { name: "triggerPreprocess" }
        Method { name: "q_validatorChanged" }
        Method {
            name: "positionAt"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "positionToRectangle"
            type: "QRectF"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
            Parameter { name: "mode"; type: "SelectionMode" }
        }
        Method {
            name: "inputMethodQuery"
            revision: 516
            type: "QVariant"
            Parameter { name: "query"; type: "Qt::InputMethodQuery" }
            Parameter { name: "argument"; type: "QVariant" }
        }
        Method {
            name: "getText"
            type: "QString"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
    }
    Component {
        file: "private/qquicktext_p.h"
        name: "QQuickTextLine"
        accessSemantics: "reference"
        prototype: "QObject"
        Property { name: "number"; type: "int"; read: "number"; index: 0; isReadonly: true }
        Property { name: "width"; type: "double"; read: "width"; write: "setWidth"; index: 1 }
        Property { name: "height"; type: "double"; read: "height"; write: "setHeight"; index: 2 }
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 3 }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 4 }
        Property {
            name: "implicitWidth"
            revision: 527
            type: "double"
            read: "implicitWidth"
            index: 5
            isReadonly: true
        }
        Property { name: "isLast"; revision: 527; type: "bool"; read: "isLast"; index: 6; isReadonly: true }
    }
    Component {
        file: "private/qquicktextmetrics_p.h"
        name: "QQuickTextMetrics"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/TextMetrics 2.4", "QtQuick/TextMetrics 6.0"]
        exportMetaObjectRevisions: [516, 1536]
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "advanceWidth"
            type: "double"
            read: "advanceWidth"
            notify: "metricsChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "boundingRect"
            type: "QRectF"
            read: "boundingRect"
            notify: "metricsChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "width"
            type: "double"
            read: "width"
            notify: "metricsChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "height"
            type: "double"
            read: "height"
            notify: "metricsChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "tightBoundingRect"
            type: "QRectF"
            read: "tightBoundingRect"
            notify: "metricsChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "elidedText"
            type: "QString"
            read: "elidedText"
            notify: "metricsChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "elide"
            type: "Qt::TextElideMode"
            read: "elide"
            write: "setElide"
            notify: "elideChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "elideWidth"
            type: "double"
            read: "elideWidth"
            write: "setElideWidth"
            notify: "elideWidthChanged"
            index: 9
            isFinal: true
        }
        Signal { name: "fontChanged" }
        Signal { name: "textChanged" }
        Signal { name: "elideChanged" }
        Signal { name: "elideWidthChanged" }
        Signal { name: "metricsChanged" }
    }
    Component {
        file: "private/qquickmultipointtoucharea_p.h"
        name: "QQuickTouchPoint"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick/TouchPoint 2.0",
            "QtQuick/TouchPoint 2.9",
            "QtQuick/TouchPoint 6.0"
        ]
        exportMetaObjectRevisions: [512, 521, 1536]
        Property {
            name: "pointId"
            type: "int"
            read: "pointId"
            notify: "pointIdChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "uniqueId"
            revision: 521
            type: "QPointingDeviceUniqueId"
            read: "uniqueId"
            notify: "uniqueIdChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "pressed"
            notify: "pressedChanged"
            index: 2
            isReadonly: true
        }
        Property { name: "x"; type: "double"; read: "x"; notify: "xChanged"; index: 3; isReadonly: true }
        Property { name: "y"; type: "double"; read: "y"; notify: "yChanged"; index: 4; isReadonly: true }
        Property {
            name: "ellipseDiameters"
            revision: 521
            type: "QSizeF"
            read: "ellipseDiameters"
            notify: "ellipseDiametersChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "pressure"
            type: "double"
            read: "pressure"
            notify: "pressureChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "rotation"
            revision: 521
            type: "double"
            read: "rotation"
            notify: "rotationChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "velocity"
            type: "QVector2D"
            read: "velocity"
            notify: "velocityChanged"
            index: 8
            isReadonly: true
        }
        Property {
            name: "area"
            type: "QRectF"
            read: "area"
            notify: "areaChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "startX"
            type: "double"
            read: "startX"
            notify: "startXChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "startY"
            type: "double"
            read: "startY"
            notify: "startYChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "previousX"
            type: "double"
            read: "previousX"
            notify: "previousXChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "previousY"
            type: "double"
            read: "previousY"
            notify: "previousYChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "sceneX"
            type: "double"
            read: "sceneX"
            notify: "sceneXChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "sceneY"
            type: "double"
            read: "sceneY"
            notify: "sceneYChanged"
            index: 15
            isReadonly: true
        }
        Signal { name: "pressedChanged" }
        Signal { name: "pointIdChanged" }
        Signal { name: "uniqueIdChanged"; revision: 521 }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "ellipseDiametersChanged"; revision: 521 }
        Signal { name: "pressureChanged" }
        Signal { name: "rotationChanged"; revision: 521 }
        Signal { name: "velocityChanged" }
        Signal { name: "areaChanged" }
        Signal { name: "startXChanged" }
        Signal { name: "startYChanged" }
        Signal { name: "previousXChanged" }
        Signal { name: "previousYChanged" }
        Signal { name: "sceneXChanged" }
        Signal { name: "sceneYChanged" }
    }
    Component {
        file: "qquickitem.h"
        name: "QQuickTransform"
        accessSemantics: "reference"
        prototype: "QObject"
        Method { name: "update" }
    }
    Component {
        file: "private/qquicktransition_p.h"
        name: "QQuickTransition"
        accessSemantics: "reference"
        defaultProperty: "animations"
        prototype: "QObject"
        exports: ["QtQuick/Transition 2.0", "QtQuick/Transition 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "from"
            type: "QString"
            read: "fromState"
            write: "setFromState"
            notify: "fromChanged"
            index: 0
        }
        Property {
            name: "to"
            type: "QString"
            read: "toState"
            write: "setToState"
            notify: "toChanged"
            index: 1
        }
        Property {
            name: "reversible"
            type: "bool"
            read: "reversible"
            write: "setReversible"
            notify: "reversibleChanged"
            index: 2
        }
        Property {
            name: "running"
            type: "bool"
            read: "running"
            notify: "runningChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "animations"
            type: "QQuickAbstractAnimation"
            isList: true
            read: "animations"
            index: 4
            isReadonly: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 5
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "reversibleChanged" }
        Signal { name: "enabledChanged" }
        Signal { name: "runningChanged" }
    }
    Component {
        file: "private/qquicktranslate_p.h"
        name: "QQuickTranslate"
        accessSemantics: "reference"
        prototype: "QQuickTransform"
        exports: ["QtQuick/Translate 2.0", "QtQuick/Translate 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; notify: "xChanged"; index: 0 }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; notify: "yChanged"; index: 1 }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickUniformAnimator"
        accessSemantics: "reference"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/UniformAnimator 2.2",
            "QtQuick/UniformAnimator 2.12",
            "QtQuick/UniformAnimator 6.0"
        ]
        exportMetaObjectRevisions: [514, 524, 1536]
        Property {
            name: "uniform"
            type: "QString"
            read: "uniform"
            write: "setUniform"
            notify: "uniformChanged"
            index: 0
        }
        Signal {
            name: "uniformChanged"
            Parameter { type: "QString" }
        }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QVector2D"
        accessSemantics: "value"
        extension: "QQuickVector2DValueType"
        exports: ["QtQuick/vector2d 2.0", "QtQuick/vector2d 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickVector2DValueType"
        accessSemantics: "value"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Method { name: "toString"; type: "QString" }
        Method {
            name: "dotProduct"
            type: "double"
            Parameter { name: "vec"; type: "QVector2D" }
        }
        Method {
            name: "times"
            type: "QVector2D"
            Parameter { name: "vec"; type: "QVector2D" }
        }
        Method {
            name: "times"
            type: "QVector2D"
            Parameter { name: "scalar"; type: "double" }
        }
        Method {
            name: "plus"
            type: "QVector2D"
            Parameter { name: "vec"; type: "QVector2D" }
        }
        Method {
            name: "minus"
            type: "QVector2D"
            Parameter { name: "vec"; type: "QVector2D" }
        }
        Method { name: "normalized"; type: "QVector2D" }
        Method { name: "length"; type: "double" }
        Method { name: "toVector3d"; type: "QVector3D" }
        Method { name: "toVector4d"; type: "QVector4D" }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector2D" }
            Parameter { name: "epsilon"; type: "double" }
        }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector2D" }
        }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QVector3D"
        accessSemantics: "value"
        extension: "QQuickVector3DValueType"
        exports: ["QtQuick/vector3d 2.0", "QtQuick/vector3d 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickVector3DValueType"
        accessSemantics: "value"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Property { name: "z"; type: "double"; read: "z"; write: "setZ"; index: 2; isFinal: true }
        Method { name: "toString"; type: "QString" }
        Method {
            name: "crossProduct"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "dotProduct"
            type: "double"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "times"
            type: "QVector3D"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "times"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "times"
            type: "QVector3D"
            Parameter { name: "scalar"; type: "double" }
        }
        Method {
            name: "plus"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method {
            name: "minus"
            type: "QVector3D"
            Parameter { name: "vec"; type: "QVector3D" }
        }
        Method { name: "normalized"; type: "QVector3D" }
        Method { name: "length"; type: "double" }
        Method { name: "toVector2d"; type: "QVector2D" }
        Method { name: "toVector4d"; type: "QVector4D" }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector3D" }
            Parameter { name: "epsilon"; type: "double" }
        }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickVector3dAnimation"
        accessSemantics: "reference"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "QtQuick/Vector3dAnimation 2.0",
            "QtQuick/Vector3dAnimation 2.12",
            "QtQuick/Vector3dAnimation 6.0"
        ]
        exportMetaObjectRevisions: [512, 524, 1536]
        Property {
            name: "from"
            type: "QVector3D"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
        }
        Property { name: "to"; type: "QVector3D"; read: "to"; write: "setTo"; notify: "toChanged"; index: 1 }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QVector4D"
        accessSemantics: "value"
        extension: "QQuickVector4DValueType"
        exports: ["QtQuick/vector4d 2.0", "QtQuick/vector4d 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickVector4DValueType"
        accessSemantics: "value"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Property { name: "z"; type: "double"; read: "z"; write: "setZ"; index: 2; isFinal: true }
        Property { name: "w"; type: "double"; read: "w"; write: "setW"; index: 3; isFinal: true }
        Method { name: "toString"; type: "QString" }
        Method {
            name: "dotProduct"
            type: "double"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method {
            name: "times"
            type: "QVector4D"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method {
            name: "times"
            type: "QVector4D"
            Parameter { name: "m"; type: "QMatrix4x4" }
        }
        Method {
            name: "times"
            type: "QVector4D"
            Parameter { name: "scalar"; type: "double" }
        }
        Method {
            name: "plus"
            type: "QVector4D"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method {
            name: "minus"
            type: "QVector4D"
            Parameter { name: "vec"; type: "QVector4D" }
        }
        Method { name: "normalized"; type: "QVector4D" }
        Method { name: "length"; type: "double" }
        Method { name: "toVector2d"; type: "QVector2D" }
        Method { name: "toVector3d"; type: "QVector3D" }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector4D" }
            Parameter { name: "epsilon"; type: "double" }
        }
        Method {
            name: "fuzzyEquals"
            type: "bool"
            Parameter { name: "vec"; type: "QVector4D" }
        }
    }
    Component {
        file: "private/qquicklistview_p.h"
        name: "QQuickViewSection"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/ViewSection 2.0", "QtQuick/ViewSection 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Enum {
            name: "SectionCriteria"
            values: ["FullString", "FirstCharacter"]
        }
        Enum {
            name: "LabelPositioning"
            values: ["InlineLabels", "CurrentLabelAtStart", "NextLabelAtEnd"]
        }
        Property {
            name: "property"
            type: "QString"
            read: "property"
            write: "setProperty"
            notify: "propertyChanged"
            index: 0
        }
        Property {
            name: "criteria"
            type: "SectionCriteria"
            read: "criteria"
            write: "setCriteria"
            notify: "criteriaChanged"
            index: 1
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 2
        }
        Property {
            name: "labelPositioning"
            type: "int"
            read: "labelPositioning"
            write: "setLabelPositioning"
            notify: "labelPositioningChanged"
            index: 3
        }
        Signal { name: "sectionsChanged" }
        Signal { name: "propertyChanged" }
        Signal { name: "criteriaChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "labelPositioningChanged" }
    }
    Component {
        file: "private/qquickitemviewtransition_p.h"
        name: "QQuickViewTransitionAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick/ViewTransition 2.0", "QtQuick/ViewTransition 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        attachedType: "QQuickViewTransitionAttached"
        Property {
            name: "index"
            type: "int"
            read: "index"
            notify: "indexChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "item"
            type: "QQuickItem"
            isPointer: true
            read: "item"
            notify: "itemChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "destination"
            type: "QPointF"
            read: "destination"
            notify: "destinationChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "targetIndexes"
            type: "QList<int>"
            read: "targetIndexes"
            notify: "targetIndexesChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "targetItems"
            type: "QObject"
            isList: true
            read: "targetItems"
            notify: "targetItemsChanged"
            index: 4
            isReadonly: true
        }
        Signal { name: "indexChanged" }
        Signal { name: "itemChanged" }
        Signal { name: "destinationChanged" }
        Signal { name: "targetIndexesChanged" }
        Signal { name: "targetItemsChanged" }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickWheelEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "device"
            type: "const QPointingDevice"
            isPointer: true
            read: "pointingDevice"
            index: 0
            isReadonly: true
        }
        Property { name: "x"; type: "double"; read: "x"; index: 1; isReadonly: true }
        Property { name: "y"; type: "double"; read: "y"; index: 2; isReadonly: true }
        Property { name: "angleDelta"; type: "QPoint"; read: "angleDelta"; index: 3; isReadonly: true }
        Property { name: "pixelDelta"; type: "QPoint"; read: "pixelDelta"; index: 4; isReadonly: true }
        Property { name: "phase"; type: "Qt::ScrollPhase"; read: "phase"; index: 5; isReadonly: true }
        Property { name: "buttons"; type: "int"; read: "buttons"; index: 6; isReadonly: true }
        Property { name: "modifiers"; type: "int"; read: "modifiers"; index: 7; isReadonly: true }
        Property { name: "inverted"; type: "bool"; read: "inverted"; index: 8; isReadonly: true }
        Property { name: "accepted"; type: "bool"; read: "isAccepted"; write: "setAccepted"; index: 9 }
    }
    Component {
        file: "private/qquickwheelhandler_p.h"
        name: "QQuickWheelHandler"
        accessSemantics: "reference"
        prototype: "QQuickSinglePointHandler"
        exports: [
            "QtQuick/WheelHandler 2.14",
            "QtQuick/WheelHandler 2.15",
            "QtQuick/WheelHandler 6.0"
        ]
        exportMetaObjectRevisions: [526, 527, 1536]
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 0
        }
        Property {
            name: "invertible"
            type: "bool"
            read: "isInvertible"
            write: "setInvertible"
            notify: "invertibleChanged"
            index: 1
        }
        Property {
            name: "activeTimeout"
            type: "double"
            read: "activeTimeout"
            write: "setActiveTimeout"
            notify: "activeTimeoutChanged"
            index: 2
        }
        Property {
            name: "rotation"
            type: "double"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 3
        }
        Property {
            name: "rotationScale"
            type: "double"
            read: "rotationScale"
            write: "setRotationScale"
            notify: "rotationScaleChanged"
            index: 4
        }
        Property {
            name: "property"
            type: "QString"
            read: "property"
            write: "setProperty"
            notify: "propertyChanged"
            index: 5
        }
        Property {
            name: "targetScaleMultiplier"
            type: "double"
            read: "targetScaleMultiplier"
            write: "setTargetScaleMultiplier"
            notify: "targetScaleMultiplierChanged"
            index: 6
        }
        Property {
            name: "targetTransformAroundCursor"
            type: "bool"
            read: "isTargetTransformAroundCursor"
            write: "setTargetTransformAroundCursor"
            notify: "targetTransformAroundCursorChanged"
            index: 7
        }
        Signal {
            name: "wheel"
            Parameter { name: "event"; type: "QQuickWheelEvent"; isPointer: true }
        }
        Signal { name: "orientationChanged" }
        Signal { name: "invertibleChanged" }
        Signal { name: "activeTimeoutChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "rotationScaleChanged" }
        Signal { name: "propertyChanged" }
        Signal { name: "targetScaleMultiplierChanged" }
        Signal { name: "targetTransformAroundCursorChanged" }
    }
    Component {
        file: "qquickwindow.h"
        name: "QQuickWindow"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QWindow"
        exports: ["QtQuick/Window 2.0"]
        exportMetaObjectRevisions: [512]
        Enum {
            name: "CreateTextureOptions"
            alias: "CreateTextureOption"
            isFlag: true
            values: [
                "TextureHasAlphaChannel",
                "TextureHasMipmaps",
                "TextureOwnsGLTexture",
                "TextureCanUseAtlas",
                "TextureIsOpaque"
            ]
        }
        Enum {
            name: "SceneGraphError"
            values: ["ContextNotAvailable"]
        }
        Enum {
            name: "TextRenderType"
            values: ["QtTextRendering", "NativeTextRendering"]
        }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            index: 2
            isReadonly: true
        }
        Property {
            name: "activeFocusItem"
            revision: 513
            type: "QQuickItem"
            isPointer: true
            read: "activeFocusItem"
            notify: "activeFocusItemChanged"
            index: 3
            isReadonly: true
        }
        Property { name: "palette"; revision: 1538; type: "QQuickPalette"; isPointer: true }
        Signal { name: "frameSwapped" }
        Signal { name: "sceneGraphInitialized" }
        Signal { name: "sceneGraphInvalidated" }
        Signal { name: "beforeSynchronizing" }
        Signal { name: "afterSynchronizing"; revision: 514 }
        Signal { name: "beforeRendering" }
        Signal { name: "afterRendering" }
        Signal { name: "afterAnimating"; revision: 514 }
        Signal { name: "sceneGraphAboutToStop"; revision: 514 }
        Signal {
            name: "closing"
            revision: 513
            Parameter { name: "close"; type: "QQuickCloseEvent"; isPointer: true }
        }
        Signal {
            name: "colorChanged"
            Parameter { type: "QColor" }
        }
        Signal { name: "activeFocusItemChanged"; revision: 513 }
        Signal {
            name: "sceneGraphError"
            revision: 514
            Parameter { name: "error"; type: "QQuickWindow::SceneGraphError" }
            Parameter { name: "message"; type: "QString" }
        }
        Signal { name: "beforeRenderPassRecording"; revision: 526 }
        Signal { name: "afterRenderPassRecording"; revision: 526 }
        Signal { name: "paletteChanged"; revision: 1536 }
        Signal { name: "paletteCreated"; revision: 1536 }
        Signal { name: "beforeFrameBegin"; revision: 1536 }
        Signal { name: "afterFrameEnd"; revision: 1536 }
        Method { name: "update" }
        Method { name: "releaseResources" }
        Method { name: "maybeUpdate" }
        Method { name: "cleanupSceneGraph" }
        Method { name: "physicalDpiChanged" }
        Method {
            name: "handleScreenChanged"
            Parameter { name: "screen"; type: "QScreen"; isPointer: true }
        }
        Method {
            name: "setTransientParent_helper"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Method { name: "runJobsAfterSwap" }
        Method {
            name: "handleApplicationStateChanged"
            Parameter { name: "state"; type: "Qt::ApplicationState" }
        }
    }
    Component {
        file: "private/qquickwindowattached_p.h"
        name: "QQuickWindowAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "visibility"
            type: "QWindow::Visibility"
            read: "visibility"
            notify: "visibilityChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            notify: "activeChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "activeFocusItem"
            type: "QQuickItem"
            isPointer: true
            read: "activeFocusItem"
            notify: "activeFocusItemChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            notify: "contentItemChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "width"
            type: "int"
            read: "width"
            notify: "widthChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            notify: "heightChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "window"
            type: "QQuickWindow"
            isPointer: true
            read: "window"
            notify: "windowChanged"
            index: 6
            isReadonly: true
        }
        Signal { name: "visibilityChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "activeFocusItemChanged" }
        Signal { name: "contentItemChanged" }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
        Signal { name: "windowChanged" }
        Method {
            name: "windowChange"
            Parameter { type: "QQuickWindow"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickwindowmodule_p.h"
        name: "QQuickWindowQmlImpl"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickWindow"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick/Window 2.1",
            "QtQuick/Window 2.2",
            "QtQuick/Window 2.3",
            "QtQuick/Window 2.13",
            "QtQuick/Window 2.14",
            "QtQuick/Window 6.0",
            "QtQuick/Window 6.2"
        ]
        exportMetaObjectRevisions: [513, 514, 515, 525, 526, 1536, 1538]
        attachedType: "QQuickWindowAttached"
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 0
        }
        Property {
            name: "visibility"
            type: "Visibility"
            read: "visibility"
            write: "setVisibility"
            notify: "visibilityChanged"
            index: 1
        }
        Property {
            name: "screen"
            revision: 515
            type: "QObject"
            isPointer: true
            read: "screen"
            write: "setScreen"
            notify: "screenChanged"
            index: 2
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "visibilityChanged"
            Parameter { name: "visibility"; type: "QWindow::Visibility" }
        }
        Signal { name: "screenChanged"; revision: 515 }
        Method { name: "setWindowVisibility" }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickXAnimator"
        accessSemantics: "reference"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/XAnimator 2.2",
            "QtQuick/XAnimator 2.12",
            "QtQuick/XAnimator 6.0"
        ]
        exportMetaObjectRevisions: [514, 524, 1536]
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickYAnimator"
        accessSemantics: "reference"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/YAnimator 2.2",
            "QtQuick/YAnimator 2.12",
            "QtQuick/YAnimator 6.0"
        ]
        exportMetaObjectRevisions: [514, 524, 1536]
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QRegularExpressionValidator"
        accessSemantics: "reference"
        prototype: "QValidator"
        exports: [
            "QtQuick/RegularExpressionValidator 2.14",
            "QtQuick/RegularExpressionValidator 6.0"
        ]
        exportMetaObjectRevisions: [526, 1536]
        Property {
            name: "regularExpression"
            type: "QRegularExpression"
            read: "regularExpression"
            write: "setRegularExpression"
            notify: "regularExpressionChanged"
            index: 0
        }
        Signal {
            name: "regularExpressionChanged"
            Parameter { name: "re"; type: "QRegularExpression" }
        }
        Method {
            name: "setRegularExpression"
            Parameter { name: "re"; type: "QRegularExpression" }
        }
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QValidator"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "State"
            values: ["Invalid", "Intermediate", "Acceptable"]
        }
        Signal { name: "changed" }
    }
    Component {
        file: "private/qquickwindowmodule_p.h"
        name: "QWindow"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "Visibility"
            values: [
                "Hidden",
                "AutomaticVisibility",
                "Windowed",
                "Minimized",
                "Maximized",
                "FullScreen"
            ]
        }
        Enum {
            name: "AncestorMode"
            values: ["ExcludeTransients", "IncludeTransients"]
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "windowTitleChanged"
            index: 0
        }
        Property {
            name: "modality"
            type: "Qt::WindowModality"
            read: "modality"
            write: "setModality"
            notify: "modalityChanged"
            index: 1
        }
        Property { name: "flags"; type: "Qt::WindowFlags"; read: "flags"; write: "setFlags"; index: 2 }
        Property { name: "x"; type: "int"; read: "x"; write: "setX"; notify: "xChanged"; index: 3 }
        Property { name: "y"; type: "int"; read: "y"; write: "setY"; notify: "yChanged"; index: 4 }
        Property {
            name: "width"
            type: "int"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 5
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 6
        }
        Property {
            name: "minimumWidth"
            type: "int"
            read: "minimumWidth"
            write: "setMinimumWidth"
            notify: "minimumWidthChanged"
            index: 7
        }
        Property {
            name: "minimumHeight"
            type: "int"
            read: "minimumHeight"
            write: "setMinimumHeight"
            notify: "minimumHeightChanged"
            index: 8
        }
        Property {
            name: "maximumWidth"
            type: "int"
            read: "maximumWidth"
            write: "setMaximumWidth"
            notify: "maximumWidthChanged"
            index: 9
        }
        Property {
            name: "maximumHeight"
            type: "int"
            read: "maximumHeight"
            write: "setMaximumHeight"
            notify: "maximumHeightChanged"
            index: 10
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 11
        }
        Property {
            name: "active"
            revision: 513
            type: "bool"
            read: "isActive"
            notify: "activeChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "visibility"
            revision: 513
            type: "Visibility"
            read: "visibility"
            write: "setVisibility"
            notify: "visibilityChanged"
            index: 13
        }
        Property {
            name: "contentOrientation"
            type: "Qt::ScreenOrientation"
            read: "contentOrientation"
            write: "reportContentOrientationChange"
            notify: "contentOrientationChanged"
            index: 14
        }
        Property {
            name: "opacity"
            revision: 513
            type: "double"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 15
        }
        Property { name: "transientParent"; revision: 525; type: "QWindow"; isPointer: true }
        Signal {
            name: "screenChanged"
            Parameter { name: "screen"; type: "QScreen"; isPointer: true }
        }
        Signal {
            name: "modalityChanged"
            Parameter { name: "modality"; type: "Qt::WindowModality" }
        }
        Signal {
            name: "windowStateChanged"
            Parameter { name: "windowState"; type: "Qt::WindowState" }
        }
        Signal {
            name: "windowTitleChanged"
            revision: 514
            Parameter { name: "title"; type: "QString" }
        }
        Signal {
            name: "xChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "minimumWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "minimumHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "maximumWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "maximumHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "visibilityChanged"
            revision: 513
            Parameter { name: "visibility"; type: "QWindow::Visibility" }
        }
        Signal { name: "activeChanged"; revision: 513 }
        Signal {
            name: "contentOrientationChanged"
            Parameter { name: "orientation"; type: "Qt::ScreenOrientation" }
        }
        Signal {
            name: "focusObjectChanged"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "opacityChanged"
            revision: 513
            Parameter { name: "opacity"; type: "double" }
        }
        Signal {
            name: "transientParentChanged"
            revision: 525
            Parameter { name: "transientParent"; type: "QWindow"; isPointer: true }
        }
        Method { name: "requestActivate"; revision: 513 }
        Method {
            name: "setVisible"
            Parameter { name: "visible"; type: "bool" }
        }
        Method { name: "show" }
        Method { name: "hide" }
        Method { name: "showMinimized" }
        Method { name: "showMaximized" }
        Method { name: "showFullScreen" }
        Method { name: "showNormal" }
        Method { name: "close"; type: "bool" }
        Method { name: "raise" }
        Method { name: "lower" }
        Method {
            name: "startSystemResize"
            type: "bool"
            Parameter { name: "edges"; type: "Qt::Edges" }
        }
        Method { name: "startSystemMove"; type: "bool" }
        Method {
            name: "setTitle"
            Parameter { type: "QString" }
        }
        Method {
            name: "setX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "posx"; type: "int" }
            Parameter { name: "posy"; type: "int" }
            Parameter { name: "w"; type: "int" }
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "rect"; type: "QRect" }
        }
        Method {
            name: "setMinimumWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setMinimumHeight"
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "setMaximumWidth"
            Parameter { name: "w"; type: "int" }
        }
        Method {
            name: "setMaximumHeight"
            Parameter { name: "h"; type: "int" }
        }
        Method {
            name: "alert"
            revision: 513
            Parameter { name: "msec"; type: "int" }
        }
        Method { name: "requestUpdate"; revision: 515 }
        Method { name: "_q_clearAlert" }
    }
}
