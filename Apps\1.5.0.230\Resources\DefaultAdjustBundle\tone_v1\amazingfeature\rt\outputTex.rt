%YAML 1.1
--- !SceneOutputRT &1
name: ""
guid: {a: 9891201354162805589, b: 8655160536004912055}
width: 720
height: 1280
depth: 1
internalFormat:
  __class: InternalFormat
  value: RGBA8
dataType:
  __class: DataType
  value: U8norm
builtinType:
  __class: BuiltInTextureType
  value: OUTPUT
filterMin:
  __class: FilterMode
  value: LINEAR
filterMag:
  __class: FilterMode
  value: LINEAR
filterMipmap:
  __class: FilterMipmapMode
  value: NONE
wrapModeS:
  __class: WrapMode
  value: CLAMP
wrapModeT:
  __class: WrapMode
  value: CLAMP
wrapModeR:
  __class: WrapMode
  value: CLAMP
attachment:
  __class: RenderTextureAttachment
  value: DEPTH24
massMode:
  __class: MSAAMode
  value: NONE
shared: false
colorFormat:
  __class: PixelFormat
  value: RGBA8Unorm
