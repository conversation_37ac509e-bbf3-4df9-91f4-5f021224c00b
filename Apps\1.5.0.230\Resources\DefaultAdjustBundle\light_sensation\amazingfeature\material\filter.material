%YAML 1.1
--- !Material &1
name: filter_material
guid: {a: 6146132980174692755, b: 17648109470343058609}
xshader: {localId: 1, path: xshader/filter.xshader}
properties:
  __class: PropertySheet
  name: filter_propertySheet
  guid: {a: 7008951588592087062, b: 12444881149689327011}
  floatmap:
    __class: Map
    intensity: 1
  vec4map:
    __class: Map
  vec3map:
    __class: Map
  vec2map:
    __class: Map
  mat4map:
    __class: Map
  texmap:
    __class: Map
    inputImageTexture: {localId: 1, path: share://input.texture}
    inputImageTexture2: {localId: 1, path: image/lightSensation_min.png}
    inputImageTexture3: {localId: 1, path: image/lightSensation_max.png}
  intmap:
    __class: Map
renderQueue: 1
enabledMacros:
  __class: Map
mshaderPath: ""
