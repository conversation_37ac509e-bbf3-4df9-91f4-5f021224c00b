import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qquickattachedobject_p.h"
        name: "QQuickAttachedObject"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "qquickmaterialstyle_p.h"
        name: "QQuickMaterialStyle"
        accessSemantics: "reference"
        prototype: "QQuickAttachedObject"
        exports: [
            "QtQuick.Controls.Material/Material 2.0",
            "QtQuick.Controls.Material/Material 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        attachedType: "QQuickMaterialStyle"
        Enum {
            name: "Theme"
            values: ["Light", "Dark", "System"]
        }
        Enum {
            name: "Variant"
            values: ["Normal", "Dense"]
        }
        Enum {
            name: "Color"
            values: [
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "DeepP<PERSON><PERSON>",
                "Indigo",
                "<PERSON>",
                "<PERSON><PERSON>lue",
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "<PERSON>",
                "LightGreen",
                "Lime",
                "Yellow",
                "Amber",
                "Orange",
                "DeepOrange",
                "Brown",
                "Grey",
                "BlueGrey"
            ]
        }
        Enum {
            name: "Shade"
            values: [
                "Shade50",
                "Shade100",
                "Shade200",
                "Shade300",
                "Shade400",
                "Shade500",
                "Shade600",
                "Shade700",
                "Shade800",
                "Shade900",
                "ShadeA100",
                "ShadeA200",
                "ShadeA400",
                "ShadeA700"
            ]
        }
        Property {
            name: "theme"
            type: "Theme"
            read: "theme"
            write: "setTheme"
            notify: "themeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "primary"
            type: "QVariant"
            read: "primary"
            write: "setPrimary"
            notify: "primaryChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "accent"
            type: "QVariant"
            read: "accent"
            write: "setAccent"
            notify: "accentChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "foreground"
            type: "QVariant"
            read: "foreground"
            write: "setForeground"
            notify: "foregroundChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "background"
            type: "QVariant"
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "elevation"
            type: "int"
            read: "elevation"
            write: "setElevation"
            notify: "elevationChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "primaryColor"
            type: "QColor"
            read: "primaryColor"
            notify: "primaryChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "accentColor"
            type: "QColor"
            read: "accentColor"
            notify: "accentChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "backgroundColor"
            type: "QColor"
            read: "backgroundColor"
            notify: "backgroundChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "primaryTextColor"
            type: "QColor"
            read: "primaryTextColor"
            notify: "themeChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "primaryHighlightedTextColor"
            type: "QColor"
            read: "primaryHighlightedTextColor"
            notify: "primaryHighlightedTextColorChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "secondaryTextColor"
            type: "QColor"
            read: "secondaryTextColor"
            notify: "themeChanged"
            index: 11
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hintTextColor"
            type: "QColor"
            read: "hintTextColor"
            notify: "themeChanged"
            index: 12
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "textSelectionColor"
            type: "QColor"
            read: "textSelectionColor"
            notify: "themeOrAccentChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "dropShadowColor"
            type: "QColor"
            read: "dropShadowColor"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "dividerColor"
            type: "QColor"
            read: "dividerColor"
            notify: "themeChanged"
            index: 15
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "iconColor"
            type: "QColor"
            read: "iconColor"
            notify: "themeChanged"
            index: 16
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "iconDisabledColor"
            type: "QColor"
            read: "iconDisabledColor"
            notify: "themeChanged"
            index: 17
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "buttonColor"
            type: "QColor"
            read: "buttonColor"
            notify: "buttonColorChanged"
            index: 18
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "buttonDisabledColor"
            type: "QColor"
            read: "buttonDisabledColor"
            notify: "buttonDisabledColorChanged"
            index: 19
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "highlightedButtonColor"
            type: "QColor"
            read: "highlightedButtonColor"
            notify: "buttonColorChanged"
            index: 20
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "frameColor"
            type: "QColor"
            read: "frameColor"
            notify: "themeChanged"
            index: 21
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "rippleColor"
            type: "QColor"
            read: "rippleColor"
            notify: "themeChanged"
            index: 22
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "highlightedRippleColor"
            type: "QColor"
            read: "highlightedRippleColor"
            notify: "themeOrAccentChanged"
            index: 23
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchUncheckedTrackColor"
            type: "QColor"
            read: "switchUncheckedTrackColor"
            notify: "themeChanged"
            index: 24
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchCheckedTrackColor"
            type: "QColor"
            read: "switchCheckedTrackColor"
            notify: "themeOrAccentChanged"
            index: 25
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchUncheckedHandleColor"
            type: "QColor"
            read: "switchUncheckedHandleColor"
            notify: "themeChanged"
            index: 26
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchCheckedHandleColor"
            type: "QColor"
            read: "switchCheckedHandleColor"
            notify: "themeOrAccentChanged"
            index: 27
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledTrackColor"
            type: "QColor"
            read: "switchDisabledTrackColor"
            notify: "themeChanged"
            index: 28
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDisabledHandleColor"
            type: "QColor"
            read: "switchDisabledHandleColor"
            notify: "themeChanged"
            index: 29
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "scrollBarColor"
            type: "QColor"
            read: "scrollBarColor"
            notify: "themeChanged"
            index: 30
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "scrollBarHoveredColor"
            type: "QColor"
            read: "scrollBarHoveredColor"
            notify: "themeChanged"
            index: 31
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "scrollBarPressedColor"
            type: "QColor"
            read: "scrollBarPressedColor"
            notify: "themeChanged"
            index: 32
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "dialogColor"
            type: "QColor"
            read: "dialogColor"
            notify: "dialogColorChanged"
            index: 33
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "backgroundDimColor"
            type: "QColor"
            read: "backgroundDimColor"
            notify: "themeChanged"
            index: 34
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "listHighlightColor"
            type: "QColor"
            read: "listHighlightColor"
            notify: "themeChanged"
            index: 35
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "tooltipColor"
            type: "QColor"
            read: "tooltipColor"
            notify: "tooltipColorChanged"
            index: 36
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "toolBarColor"
            type: "QColor"
            read: "toolBarColor"
            notify: "toolBarColorChanged"
            index: 37
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "toolTextColor"
            type: "QColor"
            read: "toolTextColor"
            notify: "toolTextColorChanged"
            index: 38
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "spinBoxDisabledIconColor"
            type: "QColor"
            read: "spinBoxDisabledIconColor"
            notify: "themeChanged"
            index: 39
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "sliderDisabledColor"
            revision: 65295
            type: "QColor"
            read: "sliderDisabledColor"
            notify: "themeChanged"
            index: 40
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "touchTarget"
            type: "int"
            read: "touchTarget"
            index: 41
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "buttonHeight"
            type: "int"
            read: "buttonHeight"
            index: 42
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "delegateHeight"
            type: "int"
            read: "delegateHeight"
            index: 43
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "dialogButtonBoxHeight"
            type: "int"
            read: "dialogButtonBoxHeight"
            index: 44
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "frameVerticalPadding"
            type: "int"
            read: "frameVerticalPadding"
            index: 45
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "menuItemHeight"
            type: "int"
            read: "menuItemHeight"
            index: 46
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "menuItemVerticalPadding"
            type: "int"
            read: "menuItemVerticalPadding"
            index: 47
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "switchDelegateVerticalPadding"
            type: "int"
            read: "switchDelegateVerticalPadding"
            index: 48
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "tooltipHeight"
            type: "int"
            read: "tooltipHeight"
            index: 49
            isReadonly: true
            isFinal: true
        }
        Signal { name: "themeChanged" }
        Signal { name: "primaryChanged" }
        Signal { name: "accentChanged" }
        Signal { name: "foregroundChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "elevationChanged" }
        Signal { name: "themeOrAccentChanged" }
        Signal { name: "primaryHighlightedTextColorChanged" }
        Signal { name: "buttonColorChanged" }
        Signal { name: "buttonDisabledColorChanged" }
        Signal { name: "dialogColorChanged" }
        Signal { name: "tooltipColorChanged" }
        Signal { name: "toolBarColorChanged" }
        Signal { name: "toolTextColorChanged" }
        Method {
            name: "color"
            type: "QColor"
            Parameter { name: "color"; type: "Color" }
            Parameter { name: "shade"; type: "Shade" }
        }
        Method {
            name: "color"
            type: "QColor"
            Parameter { name: "color"; type: "Color" }
        }
        Method {
            name: "shade"
            type: "QColor"
            Parameter { name: "color"; type: "QColor" }
            Parameter { name: "shade"; type: "Shade" }
        }
    }
}
