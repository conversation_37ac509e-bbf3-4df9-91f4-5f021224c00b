"use strict";Object.defineProperty(exports,"__esModule",{value:true});class TimeRange{constructor(startTime=0,duration=0){this.startTime=startTime;this.duration=duration}get endTime(){return this.startTime+this.duration}set endTime(endTime){if(this.endTime!=endTime){this.duration=endTime-this.startTime}}}var Amaz$g=effect.Amaz;var Color=Amaz$g.Color;var AmazUtils;(function(AmazUtils){(function(AMGABTestName){AMGABTestName[AMGABTestName["ENABLE_TEXT_TEMPLATE_OPTIMIZE_V0"]=79]="ENABLE_TEXT_TEMPLATE_OPTIMIZE_V0";AMGABTestName[AMGABTestName["EFFECTAB_ENABLE_ECS_SYSTEM_OPTIMIZE"]=84]="EFFECTAB_ENABLE_ECS_SYSTEM_OPTIMIZE"})(AmazUtils.AMGABTestName||(AmazUtils.AMGABTestName={}));AmazUtils.swingTemplateUtils=new Amaz$g.SwingTemplateUtils;AmazUtils.initAmazEntity=function(entity){Object.defineProperty(entity,"transform",{get(){let trans=this.getComponent("Transform");if(!trans){trans=this.addComponent("Transform")}return trans},set(value){AmazUtils.setEntityTransform(this,value)}});Object.defineProperty(entity,"camera",{get(){let trans=this.getComponent("Camera");if(!trans){trans=this.addComponent("Camera")}return trans}})};AmazUtils.setEntityTransform=function(entity,options){let trans=entity.getComponent("Transform");if(!trans){trans=entity.addComponent("Transform")}trans.localPosition=options.position;trans.localEulerAngle=options.rotation;trans.localScale=options.scale};AmazUtils.createEntity=function(name,scene){const ent=scene.createEntity(name);AmazUtils.initAmazEntity(ent);return ent};AmazUtils.addChildEntity=function(parent,child){child.transform.parent=parent.transform;parent.transform.addTransform(child.transform)};AmazUtils.removeChildEntity=function(parent,child){parent.transform.removeTransform(child.transform)};AmazUtils.getRenderers=function(rootEntity){const renderers=new Amaz$g.Vector;if(null!==rootEntity){const renderersTmp=rootEntity.getComponentsRecursive("Renderer");if(!renderersTmp.empty()){const size=renderersTmp.size();for(let i=0;i<size;i++){renderers.pushBack(renderersTmp.get(i))}}}return renderers};AmazUtils.CastJsonArray4fToAmazVector4f=function(jsonArray){let result=null;if(jsonArray instanceof Array&&jsonArray.length===4){result=new Amaz$g.Vector4f(jsonArray[0],jsonArray[1],jsonArray[2],jsonArray[3])}return result};AmazUtils.CastJsonArray3fToAmazVector3f=function(jsonArray){let result=null;if(jsonArray instanceof Array&&jsonArray.length===3){result=new Amaz$g.Vector3f(jsonArray[0],jsonArray[1],jsonArray[2])}return result};AmazUtils.CastJsonArray2fToAmazVector2f=function(jsonArray){let result=null;if(jsonArray instanceof Array&&jsonArray.length===2){result=new Amaz$g.Vector2f(jsonArray[0],jsonArray[1])}return result};AmazUtils.CastJsonArrayToAmazVector=function(jsonArray){const result=new Amaz$g.Vector;if(jsonArray instanceof Array){for(let i=0;i<jsonArray.length;i++){const jsonValue=jsonArray[i];result.pushBack(jsonValue)}}return result};AmazUtils.CastJsonArray4fToColor=function(jsonArray){let result=null;if(jsonArray instanceof Array&&jsonArray.length===4){result=new Color(jsonArray[0],jsonArray[1],jsonArray[2],jsonArray[3])}return result};AmazUtils.CreateQuadMesh=function(){const fv=new Amaz$g.FloatVector;const ary=new Amaz$g.UInt16Vector;fv.pushBack(-1);fv.pushBack(-1);fv.pushBack(0);fv.pushBack(0);fv.pushBack(0);fv.pushBack(1);fv.pushBack(-1);fv.pushBack(0);fv.pushBack(1);fv.pushBack(0);fv.pushBack(1);fv.pushBack(1);fv.pushBack(0);fv.pushBack(1);fv.pushBack(1);fv.pushBack(-1);fv.pushBack(1);fv.pushBack(0);fv.pushBack(0);fv.pushBack(1);const quadMesh=new Amaz$g.Mesh;quadMesh.clearAfterUpload=true;quadMesh.vertices=fv;const posDesc=new Amaz$g.VertexAttribDesc;posDesc.semantic=Amaz$g.VertexAttribType.POSITION;const uvDesc=new Amaz$g.VertexAttribDesc;uvDesc.semantic=Amaz$g.VertexAttribType.TEXCOORD0;const vads=new Amaz$g.Vector;vads.pushBack(posDesc);vads.pushBack(uvDesc);quadMesh.vertexAttribs=vads;const aabb=new Amaz$g.AABB;aabb.min_x=-1;aabb.min_y=-1;aabb.min_z=0;aabb.max_x=1;aabb.max_y=1;aabb.max_z=0;quadMesh.boundingBox=aabb;const subMesh=new Amaz$g.SubMesh;ary.pushBack(0);ary.pushBack(1);ary.pushBack(2);ary.pushBack(3);subMesh.indices16=ary;subMesh.primitive=Amaz$g.Primitive.TRIANGLE_FAN;subMesh.boundingBox=aabb;quadMesh.addSubMesh(subMesh);return quadMesh};AmazUtils.TextWordMatchUtils=new Amaz$g.TextWordMatchUtils})(AmazUtils||(AmazUtils={}));var AmazUtils$1=AmazUtils;var TemplateEventType;(function(TemplateEventType){TemplateEventType[TemplateEventType["layerOperation"]=10100]="layerOperation"})(TemplateEventType||(TemplateEventType={}));const TIME_EPS=1e-5;const CUBIC_BEZIER_EPSILON=.001;const KEYFRAME_TIME_FACTOR=1e-6;const DEFAULT_FONT_SIZE=12;const TEMPLATE_TAG="AMAZINGTEMPLATE";function isJson(str){return str.length>=2&&str.charAt(0)=="{"}function deepCopy(obj){return JSON.parse(JSON.stringify(obj))}class AmazFileUtils{static utf8ArrayToStr(array){let out="";const len=array.length;let i=0;let char2=null;let char3=null;while(i<len){const c=array[i++];switch(c>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:out+=String.fromCharCode(c);break;case 12:case 13:char2=array[i++];out+=String.fromCharCode((c&31)<<6|char2&63);break;case 14:char2=array[i++];char3=array[i++];out+=String.fromCharCode((c&15)<<12|(char2&63)<<6|(char3&63)<<0);break}}return out}static ab2str(buf){const array=new Uint8Array(buf);const resultStr=AmazFileUtils.utf8ArrayToStr(array);return resultStr}static readFileContent(filePath){try{const stateFile=fs.statSync(filePath);if(stateFile&&stateFile.isFile()){const content=fs.readFileSync(filePath);if(content){return AmazFileUtils.ab2str(content)}}}catch(e){console.error(TEMPLATE_TAG,"read file content error:",e)}return undefined}}var SelectorUnit;(function(SelectorUnit){SelectorUnit["CHAR"]="char";SelectorUnit["SYLLABLE"]="syllable";SelectorUnit["WORD"]="word";SelectorUnit["LINE"]="line";SelectorUnit["PAGE"]="page";SelectorUnit["WORD_UNREAD"]="word_unread";SelectorUnit["LINE_UNREAD"]="line_unread";SelectorUnit["KEYWORD"]="keyword"})(SelectorUnit||(SelectorUnit={}));var SelectorScope;(function(SelectorScope){SelectorScope["WORD"]="word";SelectorScope["LINE"]="line";SelectorScope["PAGE"]="page"})(SelectorScope||(SelectorScope={}));var SelectorType;(function(SelectorType){SelectorType[SelectorType["CAPTION"]=0]="CAPTION";SelectorType[SelectorType["TEXT"]=1]="TEXT"})(SelectorType||(SelectorType={}));var SelectorMode;(function(SelectorMode){SelectorMode[SelectorMode["EVERY"]=0]="EVERY";SelectorMode[SelectorMode["CUSTOM"]=1]="CUSTOM"})(SelectorMode||(SelectorMode={}));var SelectorBaseOn;(function(SelectorBaseOn){SelectorBaseOn[SelectorBaseOn["PERCENT"]=0]="PERCENT";SelectorBaseOn[SelectorBaseOn["INDEX"]=1]="INDEX"})(SelectorBaseOn||(SelectorBaseOn={}));const CaptionExpressionFunctions={["current_word_start"]:(timeInPage,captionPage)=>{var _a;const postWords=captionPage===null||captionPage===void 0?void 0:captionPage.getPostWords(timeInPage);const word=postWords===null||postWords===void 0?void 0:postWords[postWords.length-1];return(_a=word===null||word===void 0?void 0:word.startIndex)!==null&&_a!==void 0?_a:0},["current_word_end"]:(timeInPage,captionPage)=>{var _a;const postWords=captionPage===null||captionPage===void 0?void 0:captionPage.getPostWords(timeInPage);const word=postWords===null||postWords===void 0?void 0:postWords[postWords.length-1];return(_a=word===null||word===void 0?void 0:word.endIndex)!==null&&_a!==void 0?_a:0},["current_line_start"]:(timeInPage,captionPage)=>{var _a,_b;const postLines=captionPage===null||captionPage===void 0?void 0:captionPage.getPostLines(timeInPage);const line=postLines===null||postLines===void 0?void 0:postLines[postLines.length-1];return(_b=(_a=line===null||line===void 0?void 0:line.startWord)===null||_a===void 0?void 0:_a.startIndex)!==null&&_b!==void 0?_b:0},["current_line_end"]:(timeInPage,captionPage)=>{var _a,_b;const postLines=captionPage===null||captionPage===void 0?void 0:captionPage.getPostLines(timeInPage);const line=postLines===null||postLines===void 0?void 0:postLines[postLines.length-1];return(_b=(_a=line===null||line===void 0?void 0:line.endWord)===null||_a===void 0?void 0:_a.endIndex)!==null&&_b!==void 0?_b:0},["current_page_start"]:(_,captionPage)=>{var _a;const word=captionPage===null||captionPage===void 0?void 0:captionPage.getFirstNormalWord();return(_a=word===null||word===void 0?void 0:word.startIndex)!==null&&_a!==void 0?_a:0},["current_page_end"]:(_,captionPage)=>{var _a;const word=captionPage===null||captionPage===void 0?void 0:captionPage.getLastNormalWord();return(_a=word===null||word===void 0?void 0:word.endIndex)!==null&&_a!==void 0?_a:0}};class SelectorRange{constructor(start,end){this.m_startIndex=0;this.m_endIndex=0;this.m_converted=false;this.m_startIndex=start;this.m_endIndex=end}empty(){return this.m_startIndex==0&&this.m_endIndex==0}}var CAPITAL;(function(CAPITAL){CAPITAL[CAPITAL["NONE"]=1]="NONE";CAPITAL[CAPITAL["UPPER"]=2]="UPPER";CAPITAL[CAPITAL["LOWER"]=3]="LOWER";CAPITAL[CAPITAL["MIXED"]=4]="MIXED"})(CAPITAL||(CAPITAL={}));class CaptionBaseInfo{constructor(str,startTimeInWidget,endTimeInWidget){this.m_string=str;this.m_startTimeInWidget=startTimeInWidget;this.m_endTimeInWidget=endTimeInWidget}}class CaptionWord extends CaptionBaseInfo{constructor(str,startTimeInSegment,endTimeInSegment,isKey,lineBreak,pageBreak){super(str,startTimeInSegment,endTimeInSegment);this.m_globalIndex=-1;this.legalIndex=-1;this.legalIndexSkipKeyword=-1;this.legalIndexInLine=-1;this.legalIndexInLineSkipKeyword=-1;this.legalIndexInPage=-1;this.legalIndexInPageSkipKeyword=-1;this.legalKeywordIndex=-1;this.legalKeywordIndexInLine=-1;this.legalKeywordIndexInPage=-1;this.startLetterIndex=-1;this.endLetterIndex=-1;this.animStartLetterIndex=-1;this.animEndLetterIndex=-1;this.m_charIndexInPage=0;this.m_isKey=isKey;this.m_lineBreak=lineBreak;this.m_pageBreak=pageBreak}get isKey(){return this.m_isKey}isLineBreak(){return this.m_lineBreak}isPageBreak(){return this.m_pageBreak}isSpace(){return this.m_string==" "}isNormal(){return this.m_string!=null&&!this.isSpace()}toJson(){const json={start_time:this.m_startTimeInWidget*1e3,text:this.m_string,end_time:this.m_endTimeInWidget*1e3};if(this.m_pageBreak)Object.assign(json,{new_page:true});if(this.m_isKey)Object.assign(json,{is_key:true});return json}get startIndex(){return this.m_charIndexInPage}get endIndex(){return this.m_charIndexInPage+this.m_string.length}}class CaptionLine extends CaptionBaseInfo{constructor(words){super("",words[0].m_startTimeInWidget,words[words.length-1].m_endTimeInWidget);this.m_globalIndex=-1;this.legalIndex=-1;this.legalIndexInPage=-1;this.m_legalWords=[];this.m_words=words;this.m_string="";this.m_legalWords=[];for(let i=0;i<this.m_words.length;i++){this.m_string+=this.m_words[i].m_string;if(this.m_words[i].isNormal()){this.m_legalWords.push(this.m_words[i])}}if(words.length>0){this.m_startTimeInWidget=words[0].m_startTimeInWidget;this.m_endTimeInWidget=words[words.length-1].m_endTimeInWidget}else{this.m_startTimeInWidget=0;this.m_endTimeInWidget=0}}static _replaceChar(str,index,ch){return str.slice(0,index)+ch+str.slice(index+1)}get startWord(){if(this.m_words.length>0)return this.m_words[0];return null}get endWord(){if(this.m_words.length>0)return this.m_words[this.m_words.length-1];return null}get endCommonWord(){for(let i=this.m_words.length-1;i>=0;i--){const word=this.m_words[i];if(!word.isLineBreak()){return word}}return null}isPageBreak(){return this.endWord!=null&&this.endWord.isPageBreak()}get legalWords(){return this.m_legalWords}}class CaptionPage extends CaptionBaseInfo{constructor(lines){super("",lines[0].m_startTimeInWidget,lines[lines.length-1].m_endTimeInWidget);this.m_words=[];this.m_lines=[];this.m_utf16StartLetterIndex=-1;this.m_utf16EndLetterIndex=-1;this.m_globalIndex=-1;this.legalIndex=-1;this.m_legalWords=[];this.m_lines=lines;this.m_string="";for(let i=0;i<lines.length;i++){this.m_string+=lines[i].m_string;this.m_words=this.m_words.concat(lines[i].m_words);this.m_legalWords=this.m_legalWords.concat(lines[i].m_words.filter((word=>word.isNormal())))}if(lines.length>0){this.m_startTimeInWidget=lines[0].m_startTimeInWidget;this.m_endTimeInWidget=lines[lines.length-1].m_endTimeInWidget}else{this.m_startTimeInWidget=0;this.m_endTimeInWidget=0}let index=0;this.m_words.forEach((word=>{word.m_charIndexInPage=index;index+=word.m_string.length}))}get legalWords(){return this.m_legalWords}refreshAnimLetterIndex(){let currentIndex=0;for(let i=0;i<this.m_words.length;i++){this.m_words[i].animStartLetterIndex=currentIndex;currentIndex+=this.m_words[i].m_string.length;this.m_words[i].animEndLetterIndex=currentIndex}}get startLetterIndex(){if(this.m_words.length>0){return this.m_words[0].startLetterIndex}return 0}get endLetterIndex(){if(this.m_words.length>0){return this.m_words[this.m_words.length-1].endLetterIndex}return 0}get stringTrimEnd(){return this.m_string.trimEnd()}get startWord(){if(this.m_words.length>0)return this.m_words[0];return null}get endWord(){if(this.m_words.length>0)return this.m_words[this.m_words.length-1];return null}get endCommonWord(){for(let i=this.m_words.length-1;i>=0;i--){const word=this.m_words[i];if(!word.isLineBreak()){return word}}return null}getUnreadWords(timeInPage){const words=[];for(let i=0;i<this.m_words.length;i++){const word=this.m_words[i];if(word.isNormal()&&timeInPage+this.m_startTimeInWidget<word.m_startTimeInWidget){words.push(word)}}return words}getUnreadLines(timeInPage){const lines=[];for(let i=0;i<this.m_lines.length;i++){const line=this.m_lines[i];if(timeInPage+this.m_startTimeInWidget<line.m_startTimeInWidget){lines.push(line)}}return lines}getCurrentWord(timeInPage){const timeInWidget=timeInPage+this.m_startTimeInWidget;let maxStartTimeBeforeTimeStamp=-1;let index=-1;for(let i=0;i<this.m_words.length;i++){if(this.m_words[i].isNormal()&&timeInWidget>=this.m_words[i].m_startTimeInWidget){if(timeInWidget<this.m_words[i].m_endTimeInWidget)return this.m_words[i];else if(this.m_words[i].m_startTimeInWidget>maxStartTimeBeforeTimeStamp){maxStartTimeBeforeTimeStamp=this.m_words[i].m_startTimeInWidget;index=i}}}if(index>=0)return this.m_words[index];return null}getPostWords(timeInPage){const timeInWidget=timeInPage+this.m_startTimeInWidget;const postWords=[];if(timeInWidget<this.m_startTimeInWidget||timeInWidget>=this.m_endTimeInWidget)return null;for(let i=0;i<this.m_words.length;i++){if(this.m_words[i].isNormal()&&timeInWidget>=this.m_words[i].m_startTimeInWidget){postWords.push(this.m_words[i])}}return postWords}getCurrentLine(timeInPage){const timeInWidget=timeInPage+this.m_startTimeInWidget;let maxStartTimeBeforeTimeStamp=-1;let index=-1;for(let i=0;i<this.m_lines.length;i++){if(timeInWidget>=this.m_lines[i].m_startTimeInWidget){if(timeInWidget<this.m_lines[i].m_endTimeInWidget)return this.m_lines[i];else{if(this.m_lines[i].m_startTimeInWidget>maxStartTimeBeforeTimeStamp){maxStartTimeBeforeTimeStamp=this.m_lines[i].m_startTimeInWidget;index=i}}}}if(index>=0)return this.m_lines[index];return null}getPostLines(timeInPage){const timeInWidget=timeInPage+this.m_startTimeInWidget;const postLines=[];if(timeInWidget<this.m_startTimeInWidget||timeInWidget>=this.m_endTimeInWidget)return null;for(let i=0;i<this.m_lines.length;i++){if(timeInWidget>=this.m_lines[i].m_startTimeInWidget){postLines.push(this.m_lines[i])}}return postLines}getFirstNormalWord(){for(let i=0;i<this.m_words.length;i++){const word=this.m_words[i];if(word.isNormal()){return word}}return null}getLastNormalWord(){for(let i=this.m_words.length-1;i>=0;i--){const word=this.m_words[i];if(word.isNormal()){return word}}return null}getCurrentCharIndex(timeInPage){const currentWord=this.getCurrentWord(timeInPage);if(currentWord!=null){const timeInWidget=timeInPage+this.m_startTimeInWidget;const progress=(timeInWidget-currentWord.m_startTimeInWidget)/(currentWord.m_endTimeInWidget-currentWord.m_startTimeInWidget);let index=Math.floor(currentWord.startIndex+(currentWord.endIndex-currentWord.startIndex)*progress);index=Math.min(Math.max(index,currentWord.startIndex),currentWord.endIndex-1);return index}console.error(TEMPLATE_TAG,"getCurrentCharIndex error",timeInPage);return null}getTimeInPage(timeInWidget){return Math.max(0,timeInWidget-this.m_startTimeInWidget)}}class CaptionInfo{constructor(){this.m_caption_duration_info_raw=null;this.m_caption_duration_info_capital=null;this.m_caption_duration_info_original=null;this.m_caption_duration_info=null;this.m_caption_params={};this.m_keyword_font_size=-1;this.m_startTimeInWidget=0;this.m_endTimeInWidget=0;this.m_pages=[];this.m_lines=[];this.m_words=[];this.m_capital=CAPITAL.NONE;this.m_keywordCapitalDirty=false;this.m_onlyForKeyword=false;this.reset()}hasValidCaptionParams(){return Object.keys(this.caption_params).length>0}get pages(){return this.m_pages}set pages(value){this.m_pages=value}get lines(){return this.m_lines}get words(){return this.m_words}get caption_duration_info(){return this.m_caption_duration_info==null?this.m_caption_duration_info_original:this.m_caption_duration_info}get caption_duration_info_raw(){return this.m_caption_duration_info_raw}get caption_duration_info_capital(){return this.m_caption_duration_info_capital}get splitting_result(){return CaptionInfo.convertToCaptionDurationInfo(this.m_words,this.caption_duration_info.text)}get capitalStr(){const dic={[CAPITAL.NONE]:"none",[CAPITAL.UPPER]:"upper",[CAPITAL.LOWER]:"lower",[CAPITAL.MIXED]:"mixed"};return dic[this.m_capital]}get caption_params(){return this.m_caption_params}get keyword_font_size(){return this.m_keyword_font_size}set keyword_font_size(size){this.m_keyword_font_size=size}getKeywordRichTextWithSize(){let keyword_rich_text_str=this.m_caption_params.keyword_rich_text;if(keyword_rich_text_str!=null&&isJson(keyword_rich_text_str)){const keyword_rich_text=JSON.parse(keyword_rich_text_str);const fontSize=this.keyword_font_size;if(keyword_rich_text!=null&&keyword_rich_text.styles!=null&&keyword_rich_text.styles[0]!=null&&fontSize!=null&&fontSize>0){keyword_rich_text.styles[0].size=fontSize;keyword_rich_text_str=JSON.stringify(keyword_rich_text)}}return keyword_rich_text_str}isRelative(){return this.m_caption_params.keyword_supersize!=null&&this.m_caption_params.keyword_supersize>0}getKeywordRichText(){return this.m_caption_params.keyword_rich_text}get keywordCapitalDirty(){return this.m_keywordCapitalDirty}set keywordCapitalDirty(dirty){this.m_keywordCapitalDirty=dirty}get onlyForKeyword(){return this.m_onlyForKeyword}getPageIndex(timeInWidget){if(timeInWidget<this.m_startTimeInWidget||timeInWidget>=this.m_endTimeInWidget)return-1;if(this.m_pages.length==0)return-1;for(let i=0;i<this.m_pages.length;i++){if(timeInWidget>=this.m_pages[i].m_startTimeInWidget&&timeInWidget<this.m_pages[i].m_endTimeInWidget){return i}else if(timeInWidget<this.m_pages[i].m_startTimeInWidget&&i>0){return i-1}}return-1}getPage(timeInWidget){const index=this.getPageIndex(timeInWidget);return index<0?null:this.m_pages[index]}reset(){this.m_caption_duration_info=null;this.m_caption_duration_info_original=null;this.m_startTimeInWidget=0;this.m_endTimeInWidget=0;this.m_words=[];this.m_lines=[];this.m_pages=[];this.m_onlyForKeyword=false}callSplitLinePage(caption_duration_info,caption_params){if(caption_params&&caption_duration_info){const child={caption_params:caption_params,text_params:{caption_duration_info:caption_duration_info}};const outJsonStr=AmazUtils$1.swingTemplateUtils.splitLinePage(JSON.stringify(child),true);if(outJsonStr!=""){return JSON.parse(outJsonStr).caption_duration_info}}return caption_duration_info}updateCaptionDurationInfos(){this.m_caption_duration_info_capital=CaptionInfo.applyCapital(this.m_caption_duration_info_raw,this.m_capital);this.m_caption_duration_info_original=this.callSplitLinePage(this.m_caption_duration_info_capital,this.m_caption_params);this.m_caption_duration_info=deepCopy(this.m_caption_duration_info_original)}setCaptionDurationInfo(caption_duration_info){if(caption_duration_info==null||Object.keys(caption_duration_info).length==0){this.reset();return}if(caption_duration_info==this.m_caption_duration_info_original){return}this.m_caption_duration_info_raw=caption_duration_info;this.updateCaptionDurationInfos();this.m_startTimeInWidget=this.m_caption_duration_info_original.start_time/1e3;this.m_endTimeInWidget=this.m_caption_duration_info_original.end_time/1e3;this.m_keywordCapitalDirty=true;if("only_for_keyword"in this.m_caption_duration_info_raw){this.m_onlyForKeyword=this.m_caption_duration_info_raw.only_for_keyword}else{this.m_onlyForKeyword=false}}resetCaptionParams(){this.m_caption_params={}}setCaptionParams(captionParams){const oldKeywordCapital=this.m_caption_params.keyword_capital;const oldSplitInfo={max_lines_per_page:this.m_caption_params.max_lines_per_page,max_units_per_line:this.m_caption_params.max_units_per_line,unit_type:this.m_caption_params.unit_type,ignore_AI_punctuation:this.m_caption_params.ignore_AI_punctuation,keyword_isolation:this.m_caption_params.keyword_isolation};Object.assign(this.m_caption_params,captionParams);if(this.m_caption_params.max_lines_per_page!=oldSplitInfo.max_lines_per_page||this.m_caption_params.max_units_per_line!=oldSplitInfo.max_units_per_line||this.m_caption_params.unit_type!=oldSplitInfo.unit_type||this.m_caption_params.ignore_AI_punctuation!=oldSplitInfo.ignore_AI_punctuation||this.m_caption_params.keyword_isolation!=oldSplitInfo.keyword_isolation){this.updateCaptionDurationInfos()}if(oldKeywordCapital!=this.m_caption_params.keyword_capital)this.m_keywordCapitalDirty=true;return}setCapital(capital){const old_capital=this.m_capital;switch(capital){case"none":this.m_capital=CAPITAL.NONE;break;case"upper":this.m_capital=CAPITAL.UPPER;break;case"lower":this.m_capital=CAPITAL.LOWER;break;case"mixed":this.m_capital=CAPITAL.MIXED;break;default:console.error(TEMPLATE_TAG,"invalid capital:"+capital);return false}if(old_capital!=this.m_capital){this.m_keywordCapitalDirty=true;this.updateCaptionDurationInfos();return true}return false}static applyCapital(caption_duration_info,capital){const caption_duration_info_capital=deepCopy(caption_duration_info);if(!caption_duration_info)return caption_duration_info_capital;if(![CAPITAL.NONE,CAPITAL.UPPER,CAPITAL.LOWER,CAPITAL.MIXED].includes(capital))return caption_duration_info_capital;if(CAPITAL.NONE==capital){for(let i=0;i<caption_duration_info.words.length;i++){caption_duration_info_capital.words[i].text=caption_duration_info.words[i].text}caption_duration_info_capital.text=caption_duration_info.text;return caption_duration_info_capital}let str="";for(let i=0;i<caption_duration_info.words.length;i++){str+=caption_duration_info.words[i].text}const dic={[CAPITAL.UPPER]:0,[CAPITAL.LOWER]:1,[CAPITAL.MIXED]:2};const out=AmazUtils$1.swingTemplateUtils.convertTextCase(str,dic[capital]);if(out.length!=str.length){console.error(TEMPLATE_TAG,"case of no text is converted!");return}let p=0;for(let i=0;i<caption_duration_info_capital.words.length;i++){const word=caption_duration_info_capital.words[i];word.text=out.slice(p,p+word.text.length);p+=word.text.length}caption_duration_info_capital.text=out;return caption_duration_info_capital}applyKeywordCapital(){if(!this.m_keywordCapitalDirty||!this.m_caption_duration_info_original||!this.m_caption_params.enable_keyword)return;const keyword_capital=this.m_caption_params.keyword_capital;if(!["upper","lower","mixed","none"].includes(keyword_capital))return;if("none"==keyword_capital){let text="";for(let i=0;i<this.m_caption_duration_info.words.length;i++){const word=this.m_caption_duration_info.words[i];if(word.is_key){word.text=this.m_caption_duration_info_original.words[i].text}text+=word.text}this.m_caption_duration_info.text=text;return}let str="";for(let i=0;i<this.m_caption_duration_info_original.words.length;i++){if(this.m_caption_duration_info_original.words[i].is_key)str+=this.m_caption_duration_info_original.words[i].text+" "}const dic={["upper"]:0,["lower"]:1,["mixed"]:2};const out=AmazUtils$1.swingTemplateUtils.convertTextCase(str,dic[keyword_capital]);if(out.length!=str.length){console.error(TEMPLATE_TAG,"case of no text is converted in applyKeywordCapital!");return}let p=0;let text="";for(let i=0;i<this.m_caption_duration_info.words.length;i++){const word=this.m_caption_duration_info.words[i];if(word.is_key){word.text=out.slice(p,p+word.text.length);p+=word.text.length+1}text+=word.text}this.m_caption_duration_info.text=text}static _generateWords(caption_params,caption_duration_info){const INFINITY=1e5;const maxLinesPerPage=caption_params.max_lines_per_page==0?INFINITY:caption_params.max_lines_per_page;const maxUnitsPerLine=caption_params.max_units_per_line==0?INFINITY:caption_params.max_units_per_line;const ignoreAIPunctuation=caption_params.ignore_AI_punctuation==true;const keywordIsolation=caption_params.keyword_isolation;let words=[];let curUnitNum=0;let curLineNum=0;function increaseLineNum(word){if(!word.m_lineBreak){curLineNum++;word.m_lineBreak=true;if(curLineNum>=maxLinesPerPage){word.m_pageBreak=true;curLineNum=0}}}for(let i=0;i<caption_duration_info.words.length;i++){const wordInfo=caption_duration_info.words[i];if(ignoreAIPunctuation){if(wordInfo.text=="\n"){wordInfo.text=" "}}const word=new CaptionWord(wordInfo.text,wordInfo.start_time/1e3,wordInfo.end_time/1e3,wordInfo.is_key==true,wordInfo.text=="\n",false);if(maxUnitsPerLine==0){words.push(word);continue}if(wordInfo.text==" ");else if(wordInfo.text=="\n"){if(words.length>0){const lastWord=words[words.length-1];increaseLineNum(lastWord)}curUnitNum=0;continue}else{curUnitNum++;if(word.m_isKey&&keywordIsolation=="page"){if(words.length>0){words[words.length-1].m_pageBreak=true;words[words.length-1].m_lineBreak=true}word.m_pageBreak=true;word.m_lineBreak=true;curUnitNum=0;curLineNum=0}else if(word.m_isKey&&keywordIsolation=="line"){if(words.length>0){const lastWord=words[words.length-1];increaseLineNum(lastWord)}curUnitNum=0;increaseLineNum(word)}else if(curUnitNum>=maxUnitsPerLine){curUnitNum=0;increaseLineNum(word)}}words.push(word)}const ws=words;words=[];for(let i=0;i<ws.length;i++){const w=ws[i];if(w.isLineBreak()&&!w.isPageBreak()&&w.m_string!="\n"){w.m_lineBreak=false;w.m_pageBreak=false;words.push(w);const word=new CaptionWord("\n",w.m_endTimeInWidget,w.m_endTimeInWidget,false,true,false);words.push(word)}else words.push(w)}return words}static convertToCaptionDurationInfo(words,text){const ret={};ret.text=text;ret.words=[];for(let i=0;i<words.length;i++){ret.words.push(words[i].toJson())}return ret}static splitLinePage(caption_params,caption_duration_info){const words=CaptionInfo._generateWords(caption_params,caption_duration_info);return CaptionInfo.convertToCaptionDurationInfo(words,caption_duration_info.text)}_generateLines(){const words=this.m_words;const lines=[];function createLine(index){if(index<lineStart)return;const lineWords=words.slice(lineStart,index+1);const line=new CaptionLine(lineWords);line.m_globalIndex=lineGlobalIndex;lineGlobalIndex++;lines.push(line);lineStart=index+1}let lineStart=0;let lineGlobalIndex=0;for(let i=0;i<words.length;i++){if(words[i].isLineBreak()||words[i].isPageBreak()||i==words.length-1){createLine(i)}}this.m_lines=lines}_generatePages(){const lines=this.m_lines;const pages=[];function createPage(index){const pageLines=lines.slice(pageStart,index+1);const page=new CaptionPage(pageLines);page.m_globalIndex=pageGlobalIndex;pageGlobalIndex++;pages.push(page);pageStart=index+1}let pageStart=0;let pageGlobalIndex=0;for(let i=0;i<lines.length;i++){if(lines[i].isPageBreak()||i==lines.length-1){createPage(i)}}this.m_pages=pages}updatePagesJS(){const caption_duration_info=this.caption_duration_info;if(caption_duration_info==null||caption_duration_info.words==null||this.caption_params.max_lines_per_page==null||this.caption_params.max_units_per_line==null){this.m_pages=[];return}this.m_words=CaptionInfo._generateWords(this.m_caption_params,this.m_caption_duration_info_original);this._generateLines();this._generatePages()}static generateOnePage(caption_duration_info_raw){const words=[];let wordGlobalIndex=0;for(let i=0;i<caption_duration_info_raw.words.length;i++){const wordParam=caption_duration_info_raw.words[i];let isKey=false;if("is_key"in wordParam){isKey=wordParam.is_key}const word=new CaptionWord(wordParam.text,wordParam.start_time/1e3,wordParam.end_time/1e3,isKey,false,false);if(word.isNormal()&&word.m_startTimeInWidget!=word.m_endTimeInWidget){word.m_globalIndex=wordGlobalIndex;wordGlobalIndex++}words.push(word)}const lines=new CaptionLine(words);const page=new CaptionPage([lines]);return page}updatePages(){const caption_duration_info=this.caption_duration_info;if(caption_duration_info==null||caption_duration_info.words==null){this.m_pages=[];return}const words=[];let wordGlobalIndex=0;for(let i=0;i<caption_duration_info.words.length;i++){const wordParam=caption_duration_info.words[i];let isKey=false;if("is_key"in wordParam){isKey=wordParam.is_key}const word=new CaptionWord(wordParam.text,wordParam.start_time/1e3,wordParam.end_time/1e3,isKey,wordParam.isLineBreak||wordParam.text=="\n",wordParam.new_page);if(word.isNormal()&&word.m_startTimeInWidget!=word.m_endTimeInWidget){word.m_globalIndex=wordGlobalIndex;wordGlobalIndex++}words.push(word)}const lines=[];let lineStart=0;let lineGlobalIndex=0;for(let i=0;i<words.length;i++){if(words[i].isLineBreak()||words[i].isPageBreak()||i==words.length-1){const lineWords=words.slice(lineStart,i+1);const line=new CaptionLine(lineWords);line.m_globalIndex=lineGlobalIndex;lineGlobalIndex++;lines.push(line);lineStart=i+1}}this.m_lines=lines;this.m_pages=[];let pageStart=0;let pageGlobalIndex=0;for(let i=0;i<lines.length;i++){if(lines[i].isPageBreak()||i==lines.length-1){const pageLines=lines.slice(pageStart,i+1);const page=new CaptionPage(pageLines);page.m_globalIndex=pageGlobalIndex;pageGlobalIndex++;this.m_pages.push(page);pageStart=i+1}}}static getWordUnreadRange(time,captionPage){const wordUnreadArray=captionPage.getUnreadWords(time);if(wordUnreadArray.length>0){return new SelectorRange(wordUnreadArray[0].startIndex,wordUnreadArray[wordUnreadArray.length-1].endIndex)}return new SelectorRange(0,0)}static getLineUnreadRange(timeInPage,captionPage){var _a,_b;const lineUnreadArray=captionPage.getUnreadLines(timeInPage);const lineFirstWord=(_a=lineUnreadArray[0])===null||_a===void 0?void 0:_a.startWord;const lineLastWord=(_b=lineUnreadArray[lineUnreadArray.length-1])===null||_b===void 0?void 0:_b.endWord;if(lineUnreadArray.length>0&&lineFirstWord!==null&&lineLastWord!==null){return new SelectorRange(lineFirstWord.startIndex,lineLastWord.endIndex)}console.error(TEMPLATE_TAG,"getLineUnreadRange error",timeInPage);return new SelectorRange(0,0)}static getPageRange(captionPage){if(captionPage.startWord!=null&&captionPage.endCommonWord!=null){return new SelectorRange(captionPage.startWord.startIndex,captionPage.endCommonWord.endIndex)}else{console.warn(TEMPLATE_TAG,"getPageRange warning");return new SelectorRange(0,0)}}static getWordRange(timeInPage,captionPage){const currentWord=captionPage.getCurrentWord(timeInPage);return this.getRangeFromWord(currentWord)}static getRangeFromWord(currentWord){if(currentWord!=null){return new SelectorRange(currentWord.startIndex,currentWord.endIndex)}else{return new SelectorRange(0,0)}}static getLineRange(timeInPage,captionPage){const currentLine=captionPage.getCurrentLine(timeInPage);return this.getRangeFromLine(currentLine)}static getRangeFromLine(currentLine){if(currentLine!=null&&currentLine.startWord!=null&&currentLine.endCommonWord!=null){return new SelectorRange(currentLine.startWord.startIndex,currentLine.endCommonWord.endIndex)}else{console.error(TEMPLATE_TAG,"getLineRange error");return new SelectorRange(0,0)}}static getLetterRange(timeInPage,captionPage){const index=captionPage.getCurrentCharIndex(timeInPage);if(index!=null){return new SelectorRange(index,index+1)}else{console.error(TEMPLATE_TAG,"getLetterRange error",timeInPage);return new SelectorRange(0,0)}}}class Selector{constructor(selectorInfo){this.id="";this.basedOn=SelectorBaseOn.INDEX;this.scope=SelectorScope.PAGE;this.start=0;this.end=1;this.range=new SelectorRange(0,0);this.mode=SelectorMode.EVERY;this.captionPage=null;this.unit=SelectorUnit.CHAR;this.selectorType=SelectorType.TEXT;this.id=selectorInfo.id;this.selectorType=this.getType(selectorInfo.type);this.basedOn=this.getBasedOn(selectorInfo.base_on);this.mode=this.getMode(selectorInfo.selector_mode);this.start=selectorInfo.selector_start;this.end=selectorInfo.selector_end;this.scope=this.getScope(selectorInfo.selector_scope);this.unit=this.getUnit(selectorInfo.selector_unit)}getUnit(unitStr){switch(unitStr){case"char":return SelectorUnit.CHAR;case"word":return SelectorUnit.WORD;case"line":return SelectorUnit.LINE;case"page":return SelectorUnit.PAGE;case"word_unread":this.start="${current_word_end}";this.end="${current_page_end}";this.mode=SelectorMode.CUSTOM;this.scope=SelectorScope.PAGE;return SelectorUnit.WORD_UNREAD;case"line_unread":this.start="${current_line_end}";this.end="${current_page_end}";this.scope=SelectorScope.PAGE;this.mode=SelectorMode.CUSTOM;return SelectorUnit.LINE_UNREAD;case"keyword":return SelectorUnit.KEYWORD;default:return SelectorUnit.CHAR}}getType(typeStr){switch(typeStr){case"caption":return SelectorType.CAPTION;case"text":return SelectorType.TEXT;default:return SelectorType.CAPTION}}getBasedOn(baseOnStr){switch(baseOnStr){case"percent":return SelectorBaseOn.PERCENT;case"index":return SelectorBaseOn.INDEX;default:return SelectorBaseOn.INDEX}}getScope(scopeStr){switch(scopeStr){case"word":return SelectorScope.WORD;case"line":return SelectorScope.LINE;case"page":return SelectorScope.PAGE;default:return SelectorScope.PAGE}}getMode(modeStr){switch(modeStr){case"every":return SelectorMode.EVERY;case"custom":return SelectorMode.CUSTOM;default:return SelectorMode.EVERY}}update(timeInPage){if(!this.captionPage){console.error(TEMPLATE_TAG,"Selector update captionPage is null!");return timeInPage}if(this.selectorType==SelectorType.TEXT){return timeInPage}if(this.selectorType==SelectorType.CAPTION){if(this.mode==SelectorMode.EVERY){this.range=this.getSelectedRange(timeInPage);return timeInPage}if(this.mode==SelectorMode.CUSTOM){if(this.scope==SelectorScope.PAGE){const startIndex=this._parse(this.start,timeInPage);const endIndex=this._parse(this.end,timeInPage);if(typeof startIndex==="number"&&typeof endIndex==="number"||startIndex>=endIndex){this.range=new SelectorRange(startIndex,endIndex);return timeInPage}else{console.error(TEMPLATE_TAG,`illegal range in selector: [${this.id}], please check out!`)}}return timeInPage}}return timeInPage}_parse(value,time){if(typeof value==="number"){return value}const captionPage=this.captionPage;if(!captionPage){console.error(TEMPLATE_TAG,"Selector parse captionPage is null!");return 0}function evaluateCaptionExpression(expression){const replacedExpression=expression.replace(/\$\{([^}]+)\}/g,((_,match)=>{var _a;if(match in CaptionExpressionFunctions&&captionPage){return((_a=CaptionExpressionFunctions[match](time,captionPage))!==null&&_a!==void 0?_a:0).toString()}console.error(TEMPLATE_TAG,"Selector parse expression not support: [%s]",match);return"0"}));return Function('"use strict"; return ('+replacedExpression+");")()}return evaluateCaptionExpression(value)}getSelectedRange(timeInPage){const captionPage=this.captionPage;if(!captionPage){console.error(TEMPLATE_TAG,"Selector getSelectedRange captionPage is null!");return new SelectorRange(0,0)}let range=new SelectorRange(0,0);const unit=this.unit;switch(unit){case SelectorUnit.CHAR:{range=CaptionInfo.getLetterRange(timeInPage,captionPage);break}case SelectorUnit.WORD:{range=CaptionInfo.getWordRange(timeInPage,captionPage);break}case SelectorUnit.LINE:{range=CaptionInfo.getLineRange(timeInPage,captionPage);break}case SelectorUnit.PAGE:{range=CaptionInfo.getPageRange(captionPage);break}case SelectorUnit.WORD_UNREAD:{range=CaptionInfo.getWordUnreadRange(timeInPage,captionPage);break}case SelectorUnit.LINE_UNREAD:{range=CaptionInfo.getLineUnreadRange(timeInPage,captionPage);break}}return range}}const Interpolation={Linear:function(v,k){const m=v.length-1;const f=m*k;const i=Math.floor(f);const fn=Interpolation.Utils.Linear;if(k<0){return fn(v[0],v[1],f)}if(k>1){return fn(v[m],v[m-1],m-f)}return fn(v[i],v[i+1>m?m:i+1],f-i)},Bezier:function(v,k){let b=0;const n=v.length-1;const pw=Math.pow;const bn=Interpolation.Utils.Bernstein;for(let i=0;i<=n;i++){b+=pw(1-k,n-i)*pw(k,i)*v[i]*bn(n,i)}return b},CatmullRom:function(v,k){const m=v.length-1;let f=m*k;let i=Math.floor(f);const fn=Interpolation.Utils.CatmullRom;if(v[0]===v[m]){if(k<0){i=Math.floor(f=m*(1+k))}return fn(v[(i-1+m)%m],v[i],v[(i+1)%m],v[(i+2)%m],f-i)}else{if(k<0){return v[0]-(fn(v[0],v[0],v[1],v[1],-f)-v[0])}if(k>1){return v[m]-(fn(v[m],v[m],v[m-1],v[m-1],f-m)-v[m])}return fn(v[i?i-1:0],v[i],v[m<i+1?m:i+1],v[m<i+2?m:i+2],f-i)}},Utils:{Linear:function(p0,p1,t){return(p1-p0)*t+p0},Bernstein:function(n,i){const fc=Interpolation.Utils.Factorial;return fc(n)/fc(i)/fc(n-i)},Factorial:function(){const a=[1];return function(n){let s=1;if(a[n]){return a[n]}for(let i=n;i>1;i--){s*=i}a[n]=s;return s}}(),CatmullRom:function(p0,p1,p2,p3,t){const v0=(p2-p0)*.5;const v1=(p3-p1)*.5;const t2=t*t;const t3=t*t2;return(2*p1-2*p2+v0+v1)*t3+(-3*p1+3*p2-2*v0-v1)*t2+v0*t+p1}}};const Easing=Object.freeze({Linear:Object.freeze({None(amount){return amount},In(amount){return this.None(amount)},Out(amount){return this.None(amount)},InOut(amount){return this.None(amount)}}),Quadratic:Object.freeze({In(amount){return amount*amount},Out(amount){return amount*(2-amount)},InOut(amount){if((amount*=2)<1){return.5*amount*amount}return-.5*(--amount*(amount-2)-1)}}),Cubic:Object.freeze({In(amount){return amount*amount*amount},Out(amount){return--amount*amount*amount+1},InOut(amount){if((amount*=2)<1){return.5*amount*amount*amount}return.5*((amount-=2)*amount*amount+2)}}),Quartic:Object.freeze({In(amount){return amount*amount*amount*amount},Out(amount){return 1- --amount*amount*amount*amount},InOut(amount){if((amount*=2)<1){return.5*amount*amount*amount*amount}return-.5*((amount-=2)*amount*amount*amount-2)}}),Quintic:Object.freeze({In(amount){return amount*amount*amount*amount*amount},Out(amount){return--amount*amount*amount*amount*amount+1},InOut(amount){if((amount*=2)<1){return.5*amount*amount*amount*amount*amount}return.5*((amount-=2)*amount*amount*amount*amount+2)}}),Sinusoidal:Object.freeze({In(amount){return 1-Math.sin((1-amount)*Math.PI/2)},Out(amount){return Math.sin(amount*Math.PI/2)},InOut(amount){return.5*(1-Math.sin(Math.PI*(.5-amount)))}}),Exponential:Object.freeze({In(amount){return amount===0?0:Math.pow(1024,amount-1)},Out(amount){return amount===1?1:1-Math.pow(2,-10*amount)},InOut(amount){if(amount===0){return 0}if(amount===1){return 1}if((amount*=2)<1){return.5*Math.pow(1024,amount-1)}return.5*(-Math.pow(2,-10*(amount-1))+2)}}),Circular:Object.freeze({In(amount){return 1-Math.sqrt(1-amount*amount)},Out(amount){return Math.sqrt(1- --amount*amount)},InOut(amount){if((amount*=2)<1){return-.5*(Math.sqrt(1-amount*amount)-1)}return.5*(Math.sqrt(1-(amount-=2)*amount)+1)}}),Elastic:Object.freeze({In(amount){if(amount===0){return 0}if(amount===1){return 1}return-Math.pow(2,10*(amount-1))*Math.sin((amount-1.1)*5*Math.PI)},Out(amount){if(amount===0){return 0}if(amount===1){return 1}return Math.pow(2,-10*amount)*Math.sin((amount-.1)*5*Math.PI)+1},InOut(amount){if(amount===0){return 0}if(amount===1){return 1}amount*=2;if(amount<1){return-.5*Math.pow(2,10*(amount-1))*Math.sin((amount-1.1)*5*Math.PI)}return.5*Math.pow(2,-10*(amount-1))*Math.sin((amount-1.1)*5*Math.PI)+1}}),Back:Object.freeze({In(amount){const s=1.70158;return amount===1?1:amount*amount*((s+1)*amount-s)},Out(amount){const s=1.70158;return amount===0?0:--amount*amount*((s+1)*amount+s)+1},InOut(amount){const s=1.70158*1.525;if((amount*=2)<1){return.5*(amount*amount*((s+1)*amount-s))}return.5*((amount-=2)*amount*((s+1)*amount+s)+2)}}),Bounce:Object.freeze({In(amount){return 1-Easing.Bounce.Out(1-amount)},Out(amount){if(amount<1/2.75){return 7.5625*amount*amount}else if(amount<2/2.75){return 7.5625*(amount-=1.5/2.75)*amount+.75}else if(amount<2.5/2.75){return 7.5625*(amount-=2.25/2.75)*amount+.9375}else{return 7.5625*(amount-=2.625/2.75)*amount+.984375}},InOut(amount){if(amount<.5){return Easing.Bounce.In(amount*2)*.5}return Easing.Bounce.Out(amount*2-1)*.5+.5}}),generatePow(power=4){power=power<Number.EPSILON?Number.EPSILON:power;power=power>1e4?1e4:power;return{In(amount){return Math.pow(amount,power)},Out(amount){return 1-Math.pow(1-amount,power)},InOut(amount){if(amount<.5){return Math.pow(amount*2,power)/2}return(1-Math.pow(2-amount*2,power))/2+.5}}}});function getRelativeTime(start,end,anim_start,anim_end,time){const percent=(time-anim_start)/(anim_end-anim_start);return percent*(end-start)+start}class Group{constructor(){this._tweens={};this._tweensAddedDuringUpdate={}}getAll(){return Object.keys(this._tweens).map((tweenId=>this._tweens[tweenId]))}removeAll(){this._tweens={}}add(tween){this._tweens[tween.getId()]=tween;this._tweensAddedDuringUpdate[tween.getId()]=tween}remove(tween){delete this._tweens[tween.getId()];delete this._tweensAddedDuringUpdate[tween.getId()]}update(time=0,preserve=false,updateContext=null){let tweenIds=Object.keys(this._tweens);if(tweenIds.length===0){return false}let TIME_MATCHING_MODE;(function(TIME_MATCHING_MODE){TIME_MATCHING_MODE[TIME_MATCHING_MODE["ONCE"]=1]="ONCE";TIME_MATCHING_MODE[TIME_MATCHING_MODE["IN"]=2]="IN";TIME_MATCHING_MODE[TIME_MATCHING_MODE["OUT"]=3]="OUT";TIME_MATCHING_MODE[TIME_MATCHING_MODE["LOOP"]=4]="LOOP"})(TIME_MATCHING_MODE||(TIME_MATCHING_MODE={}));const WORD_COUNT_THRESHOLD=2;while(tweenIds.length>0){this._tweensAddedDuringUpdate={};for(let i=0;i<tweenIds.length;i++){const tween=this._tweens[tweenIds[i]];const autoStart=!preserve;let convertedTime=time;if(updateContext!=null){const timeMatchingMode=updateContext.wordCount>WORD_COUNT_THRESHOLD?TIME_MATCHING_MODE.IN:TIME_MATCHING_MODE.ONCE;if(timeMatchingMode==TIME_MATCHING_MODE.ONCE)convertedTime=getRelativeTime(tween.getStartTime(),tween.getEndTime(),updateContext.targetStart,updateContext.targetEnd,time);else if(timeMatchingMode==TIME_MATCHING_MODE.IN)convertedTime=time-updateContext.targetStart}if(tween&&tween.update(convertedTime,autoStart)===false&&!preserve){delete this._tweens[tweenIds[i]]}}tweenIds=Object.keys(this._tweensAddedDuringUpdate)}return true}}const mainGroup=new Group;class Sequence{static nextId(){return Sequence._nextId++}}Sequence._nextId=0;var Amaz$f=effect.Amaz;function bezier_3(t,p0,p1,p2,p3){const it=1-t;return p0*it*it*it+3*p1*t*it*it+3*p2*t*t*it+p3*t*t*t}class ColorRGBA{constructor(r,g,b,a){this.r=0;this.g=0;this.b=0;this.a=0;this.r=r;this.g=g;this.b=b;this.a=a}static normalizedToByte(f){f=Math.max(f,0);f=Math.min(f,1);return Math.floor(f*255+.5)}static RGB2HSV(c){const r=ColorRGBA.normalizedToByte(c.r);const g=ColorRGBA.normalizedToByte(c.g);const b=ColorRGBA.normalizedToByte(c.b);const a=ColorRGBA.normalizedToByte(c.a);const max=Math.max(Math.max(r,g),b);const min=Math.min(Math.min(r,g),b);const v=max/255;const s=max==0?0:(max-min)/max;let h=0;if(max==min){h=0}else if(max==r&&g>=b){h=(g-b)*60/(max-min)+0}else if(max==r&&g<b){h=(g-b)*60/(max-min)+360}else if(max==g){h=(b-r)*60/(max-min)+120}else if(max==b){h=(r-g)*60/(max-min)+240}return new Amaz$f.Vector4f(h,s,v,a/255)}static HSV2RGB(hsv){const h=hsv.x;const s=hsv.y;const v=hsv.z;let r=0,g=0,b=0;const i=Math.floor(h/60%6);const f=h/60-i;const p=v*(1-s);const q=v*(1-f*s);const t=v*(1-(1-f)*s);switch(i){case 0:r=v;g=t;b=p;break;case 1:r=q;g=v;b=p;break;case 2:r=p;g=v;b=t;break;case 3:r=p;g=q;b=v;break;case 4:r=t;g=p;b=v;break;case 5:r=v;g=p;b=q;break}return new ColorRGBA(Math.floor(r*255)/255,Math.floor(g*255)/255,Math.floor(b*255)/255,Math.floor(hsv.w*255)/255)}static castJsonArray4fToColorRGBA(jsonArray){let result=null;if(jsonArray instanceof Array&&jsonArray.length===4){result=new ColorRGBA(jsonArray[0],jsonArray[1],jsonArray[2],jsonArray[3])}return result}add(otherColor){return new ColorRGBA(this.r+otherColor.r,this.g+otherColor.g,this.b+otherColor.b,this.a+otherColor.a)}}var Amaz$e=effect.Amaz;class BezierInfo{constructor(isCubic,p0,p1,p2,p3){this.isCubic=false;this.isCubic=isCubic;this.p0=p0;this.p1=p1;this.p2=p2;this.p3=p3}}class Motion{constructor(_object,_isCubic=false,_group=mainGroup){this._object=_object;this._isCubic=_isCubic;this._group=_group;this._isPaused=false;this._pauseStart=0;this._valuesStart={};this._valuesEnd=[];this._valuesStartRepeat={};this._duration=[];this._isDynamic=false;this._initialRepeat=0;this._repeat=0;this._yoyo=false;this._isPlaying=false;this._reversed=false;this._delayTime=0;this._startTime=0;this._easingFunction=Easing.Linear.None;this._interpolationFunction=Interpolation.Linear;this._chainedMotions=[];this._onStartCallbackFired=false;this._onEveryStartCallbackFired=false;this._id=Sequence.nextId();this._isChainStopped=false;this._propertiesAreSetUp=false;this._goToEnd=false}setBezierInfo(p0,p1,p2,p3){this._bezierInfo=[new BezierInfo(this._isCubic,p0,p1,p2,p3)];return this}getObject(){return this._object}getId(){return this._id}setId(id){this._id=id}isPlaying(){return this._isPlaying}isPaused(){return this._isPaused}to(target,duration=1e3){if(this._isPlaying)throw new Error("Can not call Motion.to() while Motion is already started or paused. Stop the Motion first.");this._valuesEnd=[target];this._propertiesAreSetUp=false;this._duration=[duration];return this}getKeyFrameCount(){return this._valuesEnd.length}getStartTime(){return this._startTime}getEndTime(){let t=this._startTime;for(let i=0;i<this._duration.length;i++)t+=this._duration[i];return t}seekTo(target,duration,bezierInfo,easingFunction){if(this._isPlaying)throw new Error("Can not call Motion.seekTo() while Motion is already started or paused. Stop the Motion first.");if(target.length!=duration.length)throw new Error("Can not call Motion.seekTo() because the length of target and duration not matching.");if(target.length!=bezierInfo.length)throw new Error("Can not call Motion.seekTo() because the length of target and bezierInfo not matching.");this._valuesEnd=target;this._propertiesAreSetUp=false;this._duration=duration;this._bezierInfo=bezierInfo;this._easingFunctionList=easingFunction;return this}duration(duration){this._duration.fill(duration);return this}durationAll(duration){if(this._duration.length!=duration.length){throw new Error("Can not call Motion.durationAll() because the length of duration is not matching.")}this._duration=duration;return this}dynamic(dynamic=false){this._isDynamic=dynamic;return this}start(time=0,overrideStartingValues=false){if(this._isPlaying){return this}this._group&&this._group.add(this);this._repeat=this._initialRepeat;if(this._reversed){this._reversed=false;for(const property in this._valuesStartRepeat){this._swapEndStartRepeatValues(property);this._valuesStart[property]=this._valuesStartRepeat[property]}}this._isPlaying=true;this._isPaused=false;this._onStartCallbackFired=false;this._onEveryStartCallbackFired=false;this._isChainStopped=false;this._startTime=time;this._startTime+=this._delayTime;if(!this._propertiesAreSetUp||overrideStartingValues){this._propertiesAreSetUp=true;if(!this._isDynamic){const tmp=[];for(const obj of this._valuesEnd){const transformedObj={};for(const prop in obj){transformedObj[prop]=obj[prop]}tmp.push(transformedObj)}this._valuesEnd=tmp}for(let i=0;i<this._valuesEnd.length;i++){this._setupProperties(this._object,this._valuesStart,this._valuesEnd[i],this._valuesStartRepeat,overrideStartingValues)}}return this}startFromCurrentValues(time){return this.start(time,true)}_setupProperties(_object,_valuesStart,_valuesEnd,_valuesStartRepeat,overrideStartingValues){for(const property in _valuesEnd){const startValue=_object[property];const startValueIsArray=Array.isArray(startValue);const propType=startValueIsArray?"array":typeof startValue;let isInterpolationList=!startValueIsArray&&Array.isArray(_valuesEnd[property]);if(propType==="undefined"||propType==="function"){continue}if(isInterpolationList){const endValues=_valuesEnd[property];if(endValues.length===0){continue}const temp=[];for(let i=0,l=endValues.length;i<l;i+=1){const value=this._handleRelativeValue(startValue,endValues[i]);if(!(value instanceof ColorRGBA)&&!Array.isArray(value)&&isNaN(value)){isInterpolationList=false;console.warn("Found invalid interpolation list. Skipping.");break}temp.push(value)}if(isInterpolationList){_valuesEnd[property]=temp}}if(typeof _valuesStart[property]==="undefined"||overrideStartingValues){_valuesStart[property]=startValue}if(isInterpolationList){_valuesStartRepeat[property]=_valuesEnd[property].slice().reverse()}else{_valuesStartRepeat[property]=_valuesStart[property]||0}}}stop(){if(!this._isChainStopped){this._isChainStopped=true;this.stopChainedMotions()}if(!this._isPlaying){return this}this._group&&this._group.remove(this);this._isPlaying=false;this._isPaused=false;if(this._onStopCallback){this._onStopCallback(this._object)}return this}end(){this._goToEnd=true;this.update(Infinity);return this}pause(time=0){if(this._isPaused||!this._isPlaying){return this}this._isPaused=true;this._pauseStart=time;this._group&&this._group.remove(this);return this}resume(time=0){if(!this._isPaused||!this._isPlaying){return this}this._isPaused=false;this._startTime+=time-this._pauseStart;this._pauseStart=0;this._group&&this._group.add(this);return this}stopChainedMotions(){for(let i=0,numChainedMotions=this._chainedMotions.length;i<numChainedMotions;i++){this._chainedMotions[i].stop()}return this}group(group=mainGroup){this._group=group;return this}delay(amount=0){this._delayTime=amount;return this}repeat(times=0){this._initialRepeat=times;this._repeat=times;return this}repeatDelay(amount){this._repeatDelayTime=amount;return this}yoyo(yoyo=false){this._yoyo=yoyo;return this}easing(easingFunction=Easing.Linear.None){this._easingFunction=easingFunction;return this}interpolation(interpolationFunction=Interpolation.Linear){this._interpolationFunction=interpolationFunction;return this}chain(...motions){this._chainedMotions=motions;return this}onStart(callback){this._onStartCallback=callback;return this}onEveryStart(callback){this._onEveryStartCallback=callback;return this}onUpdate(callback){this._onUpdateCallback=callback;return this}onRepeat(callback){this._onRepeatCallback=callback;return this}onComplete(callback){this._onCompleteCallback=callback;return this}onStop(callback){this._onStopCallback=callback;return this}update(time=0,autoStart=true){var _a,_b,_c,_d,_e,_f;if(this._isPaused)return true;let accumulate=0;let i=0;const dis=time-this._startTime;for(i;i<this._duration.length;i++){accumulate+=this._duration[i];if(accumulate>=dis){break}}if(i==this._duration.length){i--}const duration=this._duration[i];const startTime=this._startTime+accumulate-duration;let property;let elapsed;const endTime=startTime+duration;if(!this._goToEnd&&!this._isPlaying){if(time>endTime){return false}if(autoStart){this.start(time,true)}}this._goToEnd=false;if(time-this._startTime<-1*KEYFRAME_TIME_FACTOR){return true}if(this._onStartCallbackFired===false){if(this._onStartCallback){this._onStartCallback(this._object)}this._onStartCallbackFired=true}if(this._onEveryStartCallbackFired===false){if(this._onEveryStartCallback){this._onEveryStartCallback(this._object)}this._onEveryStartCallbackFired=true}elapsed=(time-startTime)/duration;elapsed=duration===0||elapsed>1?1:elapsed;let value;if(((_a=this._easingFunctionList)===null||_a===void 0?void 0:_a[i])!=undefined){value=(_b=this._easingFunctionList)===null||_b===void 0?void 0:_b[i](elapsed)}else{value=this._easingFunction(elapsed)}if(i>0&&((_c=this._bezierInfo)===null||_c===void 0?void 0:_c[i])!=undefined){this._updateProperties(this._object,this._valuesEnd[i-1],this._valuesEnd[i],value,(_d=this._bezierInfo)===null||_d===void 0?void 0:_d[i])}else if(((_e=this._bezierInfo)===null||_e===void 0?void 0:_e[i])!=undefined){this._updateProperties(this._object,this._valuesStart,this._valuesEnd[i],value,(_f=this._bezierInfo)===null||_f===void 0?void 0:_f[i])}if(this._onUpdateCallback){this._onUpdateCallback(this._object,elapsed)}if(i<this._duration.length-1){return true}if(elapsed===1){if(this._repeat>0){if(isFinite(this._repeat)){this._repeat--}for(property in this._valuesStartRepeat){if(!this._yoyo&&typeof this._valuesEnd[i][property]==="string"){this._valuesStartRepeat[property]=this._valuesStartRepeat[property]+parseFloat(this._valuesEnd[i][property])}if(this._yoyo){this._swapEndStartRepeatValues(property)}this._valuesStart[property]=this._valuesStartRepeat[property]}if(this._yoyo){this._reversed=!this._reversed}if(this._repeatDelayTime!==undefined){this._startTime=time+this._repeatDelayTime}else{this._startTime=time+this._delayTime}if(this._onRepeatCallback){this._onRepeatCallback(this._object)}this._onEveryStartCallbackFired=false;return true}else{if(this._onCompleteCallback){this._onCompleteCallback(this._object)}for(let j=0,numChainedMotions=this._chainedMotions.length;j<numChainedMotions;j++){this._chainedMotions[j].start(this._startTime+duration,false)}this._isPlaying=false;return false}}return true}_updateProperties(_object,_valuesStart,_valuesEnd,value,bezierInfo){for(const property in _valuesEnd){if(_valuesStart[property]===undefined){continue}const start=_valuesStart[property]||0;let end=_valuesEnd[property];const startIsArray=Array.isArray(_object[property]);const endIsArray=Array.isArray(end);const isInterpolationList=!startIsArray&&endIsArray;if(isInterpolationList){_object[property]=this._interpolationFunction(end,value)}else if(typeof end==="object"&&end&&!(end instanceof ColorRGBA)&&!(end instanceof Array)){this._updateProperties(_object[property],start,end,value,bezierInfo)}else{if(typeof start==="number"&&(end instanceof String||typeof end==="number")){end=this._handleRelativeValue(start,end)}if(typeof end==="number"){_object[property]=this.numberInterpolate(value,bezierInfo)}else if(end instanceof ColorRGBA){_object[property]=this.colorInterpolate(value,bezierInfo)}else if(end instanceof Array){_object[property]=this.vectorInterpolate(value,bezierInfo)}else if(typeof end==="string"||typeof end==="boolean"){_object[property]=this.discreteInterpolate(value,bezierInfo)}else{console.warn("Motion object property has something wrong")}}}}_handleRelativeValue(start,end){if(typeof end!=="string"){return end}if(end.charAt(0)==="+"||end.charAt(0)==="-"){return start+parseFloat(end)}return parseFloat(end)}_swapEndStartRepeatValues(property){const tmp=this._valuesStartRepeat[property];for(let i=0;i<this._valuesEnd.length;i++){const endValue=this._valuesEnd[i][property];if(typeof endValue==="string"){this._valuesStartRepeat[property]=this._valuesStartRepeat[property]+parseFloat(endValue)}else{this._valuesStartRepeat[property]=this._valuesEnd[i][property]}this._valuesEnd[i][property]=tmp}}colorInterpolate(value,bezierInfo){const p0=bezierInfo.p0;const p3=bezierInfo.p3;const lhsv=ColorRGBA.RGB2HSV(new ColorRGBA(p0.r,p0.g,p0.b,p0.a));const rhsv=ColorRGBA.RGB2HSV(new ColorRGBA(p3.r,p3.g,p3.b,p3.a));if(!bezierInfo.isCubic){const t=value;if(Math.abs(lhsv.x-rhsv.x)>180){if(lhsv.x<rhsv.x)rhsv.x-=360;else lhsv.x-=360}const hsv=new Amaz$e.Vector4f(lhsv.x+(rhsv.x-lhsv.x)*t,lhsv.y+(rhsv.y-lhsv.y)*t,lhsv.z+(rhsv.z-lhsv.z)*t,lhsv.w+(rhsv.w-lhsv.w)*t);if(hsv.x<0){hsv.x+=360}const res=ColorRGBA.HSV2RGB(hsv);return[res.r,res.g,res.b,res.a]}const p1=bezierInfo.p1;const p2=bezierInfo.p2;const lctrl=ColorRGBA.RGB2HSV(new ColorRGBA(p1.r,p1.g,p1.b,1));const rctrl=ColorRGBA.RGB2HSV(new ColorRGBA(p2.r,p2.g,p2.b,1));if(Math.abs(lhsv.x-rhsv.x)>180){if(lhsv.x<rhsv.x){rhsv.x-=360;rctrl.x-=360}else{lhsv.x-=360;lctrl.x-=360}}const hsv=new Amaz$e.Vector4f(bezier_3(value,lhsv.x,lctrl.x,rctrl.x,rhsv.x),bezier_3(value,lhsv.y,lctrl.y,rctrl.y,rhsv.y),bezier_3(value,lhsv.z,lctrl.z,rctrl.z,rhsv.z),bezier_3(value,lhsv.w,lctrl.w,rctrl.w,rhsv.w));if(hsv.x<0){hsv.x+=360}const res=ColorRGBA.HSV2RGB(hsv);return[res.r,res.g,res.b,res.a]}numberInterpolate(value,bezierInfo){const p0=bezierInfo.p0;const p1=bezierInfo.p1;const p2=bezierInfo.p2;const p3=bezierInfo.p3;if(bezierInfo.isCubic&&bezierInfo.p0!=undefined){return bezier_3(value,p0,p1,p2,p3)}else{return p0+(p3-p0)*value}}vectorInterpolate(value,bezierInfo){const p0=bezierInfo.p0;const p1=bezierInfo.p1;const p2=bezierInfo.p2;const p3=bezierInfo.p3;const length=p0.length;const result=[];if(p0[0]instanceof ColorRGBA){for(let i=0;i<length;i++){if(p0[i]!=null&&p1[i]!=null&&p2[i]!=null&&p3[i]!=null){const res=this.colorInterpolate(value,new BezierInfo(bezierInfo.isCubic,p0[i],p1[i],p2[i],p3[i]));result.push(res)}}}else if(Array.isArray(p0[0])){for(let i=0;i<length;i++){const p0Item=ColorRGBA.castJsonArray4fToColorRGBA(p0[i]);const p1Item=ColorRGBA.castJsonArray4fToColorRGBA(p1[i]);const p2Item=ColorRGBA.castJsonArray4fToColorRGBA(p2[i]);const p3Item=ColorRGBA.castJsonArray4fToColorRGBA(p3[i]);if(p0Item&&p1Item&&p2Item&&p3Item){const res=this.colorInterpolate(value,new BezierInfo(bezierInfo.isCubic,p0Item,p2Item,p1Item,p3Item));result.push(res)}}}else if(typeof p0[0]==="number"){for(let i=0;i<length;i++){result.push(this.numberInterpolate(value,new BezierInfo(bezierInfo.isCubic,p0[i],p1[i],p2[i],p3[i])))}}return result}discreteInterpolate(value,bezierInfo){if(value>=.5){return bezierInfo.p3}else{return bezierInfo.p0}}}let IDX=-1;class keyframeUnitInfo{constructor(v,t,it){this.m_v=v;this.m_t=t;this.m_it=it;this.m_vti=0;this.m_vto=0;this.m_vi=v;this.m_vo=v}}class KeyframeAttrUtils{static getWordConvertedTime(word_start,word_end,anim_start,anim_end,time,delay){const percent=(time-anim_start)/(anim_end-anim_start);return percent*(word_end-word_start)+delay}static getRelativeTime(startTimeDst,endTimeDst,startTimeSrc,endTimeSrc,time){const percent=(time-startTimeSrc)/(endTimeSrc-startTimeSrc);return percent*(endTimeDst-startTimeDst)+startTimeDst}static cubicBezier(x,tvOld,tvNew){const vto=1*tvOld.m_vto/(tvNew.m_t-tvOld.m_t);const vti=1*tvNew.m_vti/(tvNew.m_t-tvOld.m_t)+1;const p1=[vto,0];const p2=[vti,1];const a=1-3*p2[0]+3*p1[0];const b=3*p2[0]-6*p1[0];const c=3*p1[0];if(x<0||Math.abs(x)<TIME_EPS){return 0}if(x>1||Math.abs(x-1)<TIME_EPS){return 1}let t2=x;for(let i=0;i<8;i++){const x2=((a*t2+b)*t2+c)*t2-x;if(Math.abs(x2)<CUBIC_BEZIER_EPSILON){return t2}const d2=(a*3*t2+b*2)*t2+c;if(Math.abs(d2)<TIME_EPS){break}t2=Math.max(0,Math.min(1,t2-x2/d2))}let t0=0;let t1=1;while(t0<t1&&Math.abs(t1-t0)>TIME_EPS){const t2=(t1-t0)*.5+t0;const x2=((a*t2+b)*t2+c)*t2;if(Math.abs(x2-x)<CUBIC_BEZIER_EPSILON)return t2;if(x>x2)t0=t2;else t1=t2}if(t2>1){t2=1}return t2}static getKeyframeValue(param){if("motionKeyFrameInfo"in param&&param.motionKeyFrameInfo.length>0){return KeyframeAttrUtils.createKeyframeFunc(param.motionKeyFrameInfo)}return param.value}static createKeyframeFunc(keyframeInfo){const motion=KeyframeAttrUtils.createMotion(keyframeInfo).start();return time=>{motion.update(time,false);return motion.getObject().v}}static createMotion(keyframeArray){const keyframes=[];for(let i=0;i<keyframeArray.length;i++){const info=keyframeArray[i];const keyframe=new keyframeUnitInfo(info.v,info.t,info.it);keyframe.m_vi=info.vi;keyframe.m_vo=info.vo;keyframe.m_vto=info.vto;keyframe.m_vti=info.vti;keyframes.push(keyframe)}const motion=new Motion({v:keyframes[0].m_v});if(keyframes[0].m_t>0)motion===null||motion===void 0?void 0:motion.delay(keyframes[0].m_t);const toList=[];const durationList=[];const bezierInfo=[];const easingList=[];for(let i=1;i<keyframes.length;i++){toList.push({v:keyframes[i].m_v});durationList.push(keyframes[i].m_t-keyframes[i-1].m_t);let isCubic=false;let easing=Easing.Linear.None;if(keyframes[i-1].m_it==="cubic"||keyframes[i].m_it==="cubic"){isCubic=true;easing=t=>KeyframeAttrUtils.cubicBezier(t,keyframes[i-1],keyframes[i])}bezierInfo.push(new BezierInfo(isCubic,keyframes[i-1].m_v,keyframes[i-1].m_vo,keyframes[i].m_vi,keyframes[i].m_v));easingList.push(easing)}motion===null||motion===void 0?void 0:motion.seekTo(toList,durationList,bezierInfo,easingList);return motion}static createOneMotion(keyframes,updateFunc,_interp,motionContext,isFirst,group,propertyName){if(isFirst){++IDX}const motion=new Motion({v:keyframes[0].m_v,motionContext:motionContext,idx:IDX,propertyName:propertyName},false,group).onUpdate(updateFunc);motion.interpolation(Interpolation.Linear);motion.easing(Easing.Linear.None);if(isFirst&&keyframes[0].m_t>0)motion===null||motion===void 0?void 0:motion.delay(keyframes[0].m_t);const toList=[];const durationList=[];const bezierInfo=[];const easingList=[];for(let i=1;i<keyframes.length;i++){toList.push({v:keyframes[i].m_v});durationList.push(keyframes[i].m_t-keyframes[i-1].m_t);let isCubic=false;let easing=Easing.Linear.None;if(keyframes[i-1].m_it==="cubic"||keyframes[i].m_it==="cubic"){isCubic=true;easing=t=>KeyframeAttrUtils.cubicBezier(t,keyframes[i-1],keyframes[i])}bezierInfo.push(new BezierInfo(isCubic,keyframes[i-1].m_v,keyframes[i-1].m_vo,keyframes[i].m_vi,keyframes[i].m_v));easingList.push(easing)}motion===null||motion===void 0?void 0:motion.seekTo(toList,durationList,bezierInfo,easingList);return motion}static createKeyframesColorValue(value){if(!(value instanceof Array)||value.length<=0){return}if(value[0]instanceof Array){for(let i=0;i<value.length;i++){const colorV=value[i];if(colorV.length===4)value[i]=ColorRGBA.castJsonArray4fToColorRGBA(colorV);else if(colorV.length===3)value[i]=ColorRGBA.castJsonArray4fToColorRGBA([colorV[0],colorV[1],colorV[2],1])}}else if(value.length===4){value=ColorRGBA.castJsonArray4fToColorRGBA(value)}}static createKeyframes(keyframeJson,propertyName,colorNames){const prop=keyframeJson.k[propertyName];const keyframes=[];const isColor=colorNames.includes(propertyName);{const t=0;if(isColor&&prop[0].v instanceof Array){KeyframeAttrUtils.createKeyframesColorValue(prop[0].v)}keyframes.push(new keyframeUnitInfo(prop[0].v,t,prop[0].it))}for(let i=0;i<prop.length;i++){if(isColor&&prop[i].v instanceof Array){KeyframeAttrUtils.createKeyframesColorValue(prop[i].v)}const keyframeUnit=new keyframeUnitInfo(prop[i].v,prop[i].t,prop[i].it);if(keyframeUnit.m_it==="cubic"){if(isColor&&prop[i].vi instanceof Array&&prop[i].vo instanceof Array){KeyframeAttrUtils.createKeyframesColorValue(prop[i].vi);KeyframeAttrUtils.createKeyframesColorValue(prop[i].vo);if(prop[i].v instanceof ColorRGBA&&prop[i].vi instanceof ColorRGBA&&prop[i].vo instanceof ColorRGBA){keyframeUnit.m_vi=prop[i].v.add(prop[i].vi);keyframeUnit.m_vo=prop[i].v.add(prop[i].vo)}}else{keyframeUnit.m_vi=prop[i].v+prop[i].vi;keyframeUnit.m_vo=prop[i].v+prop[i].vo}keyframeUnit.m_vti=prop[i].vti;keyframeUnit.m_vto=prop[i].vto}if(prop[i].t-keyframes[keyframes.length-1].m_t>TIME_EPS){keyframes.push(keyframeUnit)}}{const t=keyframeJson.e;if(t-keyframes[keyframes.length-1].m_t>TIME_EPS){if(isColor&&prop[prop.length-1].v instanceof Array){KeyframeAttrUtils.createKeyframesColorValue(prop[prop.length-1].v)}keyframes.push(new keyframeUnitInfo(prop[prop.length-1].v,t,prop[prop.length-1].it))}}return keyframes}static createOneMotionByRelativeTime(keyframes,updateFunc,interpolationFunc,motionContext,group,propertyName,startTimeDst,endTimeDst,startTimeSrc,endTimeSrc){const convertedTimes=[];for(let i=0;i<keyframes.length;i++){convertedTimes.push(KeyframeAttrUtils.getRelativeTime(startTimeDst,endTimeDst,startTimeSrc,endTimeSrc,keyframes[i].m_t))}const motion=new Motion({v:keyframes[0].m_v,motionContext:motionContext,propertyName:propertyName},false,group).onUpdate(updateFunc).interpolation(interpolationFunc).easing(Easing.Linear.None).delay(convertedTimes[0]);const toList=[];const durationList=[];const bezierInfo=[];const easingList=[];for(let i=1;i<keyframes.length;i++){toList.push({v:keyframes[i].m_v});durationList.push(convertedTimes[i]-convertedTimes[i-1]);let isCubic=false;let easing=Easing.Linear.None;if(keyframes[i-1].m_it==="cubic"||keyframes[i].m_it==="cubic"){isCubic=true;easing=t=>KeyframeAttrUtils.cubicBezier(t,keyframes[i-1],keyframes[i])}bezierInfo.push(new BezierInfo(isCubic,keyframes[i-1].m_v,keyframes[i-1].m_vo,keyframes[i].m_vi,keyframes[i].m_v));easingList.push(easing)}motion===null||motion===void 0?void 0:motion.seekTo(toList,durationList,bezierInfo,easingList);return motion}static isPostGroup(propertyName){const postGroupProperties=["sxy","sx","sy","px","py","rz"];return postGroupProperties.includes(propertyName)}static sortFn(obj1,obj2,keyLists){const index1=keyLists.findIndex((e=>e[0]===obj1.getObject().propertyName));const index2=keyLists.findIndex((e=>e[0]===obj2.getObject().propertyName));if(index1<0||index2<0)return 0;else return index1-index2}static sortKeyframesByAttributeSetter(group,attributeSetter){if(Object.prototype.hasOwnProperty.call(attributeSetter,"setterMap")){const motions=group.getAll();if(motions.length>1){const keyLists=Object.entries(attributeSetter.setterMap);motions.sort(((obj1,obj2)=>KeyframeAttrUtils.sortFn(obj1,obj2,keyLists)));group.removeAll();let idx=0;motions.forEach((motion=>{motion.setId(idx++);group.add(motion)}))}}}static isValidOverlayMode(overlayMode){if(["over","add","sub","mul"].includes(overlayMode)){return true}return false}}class CaptionAttributeSetter{static getValueCallback(propertyName){return function(obj){const value=obj.v;const motionContext=obj.motionContext;const selectorRange=motionContext.selector.range;const selectorUnit=obj.motionContext.selector.unit;motionContext.callbackParams.values.push({name:propertyName,value:value,start:selectorRange.m_startIndex,end:selectorRange.m_endIndex,overlayMode:motionContext.overlayMode,selectorUnit:selectorUnit})}}}CaptionAttributeSetter.colorNames=["tc","bc","sc","oc","tbc"];class BaseAnimationController{constructor(){this.m_startTimeInWidget=0;this.m_endTimeInWidget=0;this.m_keyframeParams=null;this.m_keyFramesDirty=true}setTimeRange(startTimeInWidget,endTimeInWidget){this.m_startTimeInWidget=startTimeInWidget;this.m_endTimeInWidget=endTimeInWidget}isEnable(timeInWidget){if(timeInWidget-this.m_startTimeInWidget>=-TIME_EPS&&timeInWidget-this.m_endTimeInWidget<=TIME_EPS){return true}return false}setKeyFrameParams(keyframeParams){this.m_keyframeParams=keyframeParams;this.m_keyFramesDirty=true}resetAnimation(){this.m_keyFramesDirty=true}checkIsDirty(){return this.m_keyFramesDirty}}class TextBaseAnimationController extends BaseAnimationController{constructor(){super()}updateKeyFrames(timeInWidget){}update(timeInWidget){return timeInWidget}}var commonjsGlobal=typeof globalThis!=="undefined"?globalThis:typeof window!=="undefined"?window:typeof global!=="undefined"?global:typeof self!=="undefined"?self:{};var alea$1={exports:{}};(function(module){(function(global,module,define){function Alea(seed){var me=this,mash=Mash();me.next=function(){var t=2091639*me.s0+me.c*2.3283064365386963e-10;me.s0=me.s1;me.s1=me.s2;return me.s2=t-(me.c=t|0)};me.c=1;me.s0=mash(" ");me.s1=mash(" ");me.s2=mash(" ");me.s0-=mash(seed);if(me.s0<0){me.s0+=1}me.s1-=mash(seed);if(me.s1<0){me.s1+=1}me.s2-=mash(seed);if(me.s2<0){me.s2+=1}mash=null}function copy(f,t){t.c=f.c;t.s0=f.s0;t.s1=f.s1;t.s2=f.s2;return t}function impl(seed,opts){var xg=new Alea(seed),state=opts&&opts.state,prng=xg.next;prng.int32=function(){return xg.next()*4294967296|0};prng.double=function(){return prng()+(prng()*2097152|0)*11102230246251565e-32};prng.quick=prng;if(state){if(typeof state=="object")copy(state,xg);prng.state=function(){return copy(xg,{})}}return prng}function Mash(){var n=4022871197;var mash=function(data){data=String(data);for(var i=0;i<data.length;i++){n+=data.charCodeAt(i);var h=.02519603282416938*n;n=h>>>0;h-=n;h*=n;n=h>>>0;h-=n;n+=h*4294967296}return(n>>>0)*2.3283064365386963e-10};return mash}if(module&&module.exports){module.exports=impl}else if(define&&define.amd){define((function(){return impl}))}else{this.alea=impl}})(commonjsGlobal,module,typeof undefined=="function")})(alea$1);var xor128$1={exports:{}};(function(module){(function(global,module,define){function XorGen(seed){var me=this,strseed="";me.x=0;me.y=0;me.z=0;me.w=0;me.next=function(){var t=me.x^me.x<<11;me.x=me.y;me.y=me.z;me.z=me.w;return me.w^=me.w>>>19^t^t>>>8};if(seed===(seed|0)){me.x=seed}else{strseed+=seed}for(var k=0;k<strseed.length+64;k++){me.x^=strseed.charCodeAt(k)|0;me.next()}}function copy(f,t){t.x=f.x;t.y=f.y;t.z=f.z;t.w=f.w;return t}function impl(seed,opts){var xg=new XorGen(seed),state=opts&&opts.state,prng=function(){return(xg.next()>>>0)/4294967296};prng.double=function(){do{var top=xg.next()>>>11,bot=(xg.next()>>>0)/4294967296,result=(top+bot)/(1<<21)}while(result===0);return result};prng.int32=xg.next;prng.quick=prng;if(state){if(typeof state=="object")copy(state,xg);prng.state=function(){return copy(xg,{})}}return prng}if(module&&module.exports){module.exports=impl}else if(define&&define.amd){define((function(){return impl}))}else{this.xor128=impl}})(commonjsGlobal,module,typeof undefined=="function")})(xor128$1);var xorwow$1={exports:{}};(function(module){(function(global,module,define){function XorGen(seed){var me=this,strseed="";me.next=function(){var t=me.x^me.x>>>2;me.x=me.y;me.y=me.z;me.z=me.w;me.w=me.v;return(me.d=me.d+362437|0)+(me.v=me.v^me.v<<4^(t^t<<1))|0};me.x=0;me.y=0;me.z=0;me.w=0;me.v=0;if(seed===(seed|0)){me.x=seed}else{strseed+=seed}for(var k=0;k<strseed.length+64;k++){me.x^=strseed.charCodeAt(k)|0;if(k==strseed.length){me.d=me.x<<10^me.x>>>4}me.next()}}function copy(f,t){t.x=f.x;t.y=f.y;t.z=f.z;t.w=f.w;t.v=f.v;t.d=f.d;return t}function impl(seed,opts){var xg=new XorGen(seed),state=opts&&opts.state,prng=function(){return(xg.next()>>>0)/4294967296};prng.double=function(){do{var top=xg.next()>>>11,bot=(xg.next()>>>0)/4294967296,result=(top+bot)/(1<<21)}while(result===0);return result};prng.int32=xg.next;prng.quick=prng;if(state){if(typeof state=="object")copy(state,xg);prng.state=function(){return copy(xg,{})}}return prng}if(module&&module.exports){module.exports=impl}else if(define&&define.amd){define((function(){return impl}))}else{this.xorwow=impl}})(commonjsGlobal,module,typeof undefined=="function")})(xorwow$1);var xorshift7$1={exports:{}};(function(module){(function(global,module,define){function XorGen(seed){var me=this;me.next=function(){var X=me.x,i=me.i,t,v;t=X[i];t^=t>>>7;v=t^t<<24;t=X[i+1&7];v^=t^t>>>10;t=X[i+3&7];v^=t^t>>>3;t=X[i+4&7];v^=t^t<<7;t=X[i+7&7];t=t^t<<13;v^=t^t<<9;X[i]=v;me.i=i+1&7;return v};function init(me,seed){var j,X=[];if(seed===(seed|0)){X[0]=seed}else{seed=""+seed;for(j=0;j<seed.length;++j){X[j&7]=X[j&7]<<15^seed.charCodeAt(j)+X[j+1&7]<<13}}while(X.length<8)X.push(0);for(j=0;j<8&&X[j]===0;++j);if(j==8)X[7]=-1;else X[j];me.x=X;me.i=0;for(j=256;j>0;--j){me.next()}}init(me,seed)}function copy(f,t){t.x=f.x.slice();t.i=f.i;return t}function impl(seed,opts){if(seed==null)seed=+new Date;var xg=new XorGen(seed),state=opts&&opts.state,prng=function(){return(xg.next()>>>0)/4294967296};prng.double=function(){do{var top=xg.next()>>>11,bot=(xg.next()>>>0)/4294967296,result=(top+bot)/(1<<21)}while(result===0);return result};prng.int32=xg.next;prng.quick=prng;if(state){if(state.x)copy(state,xg);prng.state=function(){return copy(xg,{})}}return prng}if(module&&module.exports){module.exports=impl}else if(define&&define.amd){define((function(){return impl}))}else{this.xorshift7=impl}})(commonjsGlobal,module,typeof undefined=="function")})(xorshift7$1);var xor4096$1={exports:{}};(function(module){(function(global,module,define){function XorGen(seed){var me=this;me.next=function(){var w=me.w,X=me.X,i=me.i,t,v;me.w=w=w+1640531527|0;v=X[i+34&127];t=X[i=i+1&127];v^=v<<13;t^=t<<17;v^=v>>>15;t^=t>>>12;v=X[i]=v^t;me.i=i;return v+(w^w>>>16)|0};function init(me,seed){var t,v,i,j,w,X=[],limit=128;if(seed===(seed|0)){v=seed;seed=null}else{seed=seed+"\0";v=0;limit=Math.max(limit,seed.length)}for(i=0,j=-32;j<limit;++j){if(seed)v^=seed.charCodeAt((j+32)%seed.length);if(j===0)w=v;v^=v<<10;v^=v>>>15;v^=v<<4;v^=v>>>13;if(j>=0){w=w+1640531527|0;t=X[j&127]^=v+w;i=0==t?i+1:0}}if(i>=128){X[(seed&&seed.length||0)&127]=-1}i=127;for(j=4*128;j>0;--j){v=X[i+34&127];t=X[i=i+1&127];v^=v<<13;t^=t<<17;v^=v>>>15;t^=t>>>12;X[i]=v^t}me.w=w;me.X=X;me.i=i}init(me,seed)}function copy(f,t){t.i=f.i;t.w=f.w;t.X=f.X.slice();return t}function impl(seed,opts){if(seed==null)seed=+new Date;var xg=new XorGen(seed),state=opts&&opts.state,prng=function(){return(xg.next()>>>0)/4294967296};prng.double=function(){do{var top=xg.next()>>>11,bot=(xg.next()>>>0)/4294967296,result=(top+bot)/(1<<21)}while(result===0);return result};prng.int32=xg.next;prng.quick=prng;if(state){if(state.X)copy(state,xg);prng.state=function(){return copy(xg,{})}}return prng}if(module&&module.exports){module.exports=impl}else if(define&&define.amd){define((function(){return impl}))}else{this.xor4096=impl}})(commonjsGlobal,module,typeof undefined=="function")})(xor4096$1);var tychei$1={exports:{}};(function(module){(function(global,module,define){function XorGen(seed){var me=this,strseed="";me.next=function(){var b=me.b,c=me.c,d=me.d,a=me.a;b=b<<25^b>>>7^c;c=c-d|0;d=d<<24^d>>>8^a;a=a-b|0;me.b=b=b<<20^b>>>12^c;me.c=c=c-d|0;me.d=d<<16^c>>>16^a;return me.a=a-b|0};me.a=0;me.b=0;me.c=2654435769|0;me.d=1367130551;if(seed===Math.floor(seed)){me.a=seed/4294967296|0;me.b=seed|0}else{strseed+=seed}for(var k=0;k<strseed.length+20;k++){me.b^=strseed.charCodeAt(k)|0;me.next()}}function copy(f,t){t.a=f.a;t.b=f.b;t.c=f.c;t.d=f.d;return t}function impl(seed,opts){var xg=new XorGen(seed),state=opts&&opts.state,prng=function(){return(xg.next()>>>0)/4294967296};prng.double=function(){do{var top=xg.next()>>>11,bot=(xg.next()>>>0)/4294967296,result=(top+bot)/(1<<21)}while(result===0);return result};prng.int32=xg.next;prng.quick=prng;if(state){if(typeof state=="object")copy(state,xg);prng.state=function(){return copy(xg,{})}}return prng}if(module&&module.exports){module.exports=impl}else if(define&&define.amd){define((function(){return impl}))}else{this.tychei=impl}})(commonjsGlobal,module,typeof undefined=="function")})(tychei$1);var seedrandom$1={exports:{}};(function(module){(function(global,pool,math){var width=256,chunks=6,digits=52,rngname="random",startdenom=math.pow(width,chunks),significance=math.pow(2,digits),overflow=significance*2,mask=width-1,nodecrypto;function seedrandom(seed,options,callback){var key=[];options=options==true?{entropy:true}:options||{};var shortseed=mixkey(flatten(options.entropy?[seed,tostring(pool)]:seed==null?autoseed():seed,3),key);var arc4=new ARC4(key);var prng=function(){var n=arc4.g(chunks),d=startdenom,x=0;while(n<significance){n=(n+x)*width;d*=width;x=arc4.g(1)}while(n>=overflow){n/=2;d/=2;x>>>=1}return(n+x)/d};prng.int32=function(){return arc4.g(4)|0};prng.quick=function(){return arc4.g(4)/4294967296};prng.double=prng;mixkey(tostring(arc4.S),pool);return(options.pass||callback||function(prng,seed,is_math_call,state){if(state){if(state.S){copy(state,arc4)}prng.state=function(){return copy(arc4,{})}}if(is_math_call){math[rngname]=prng;return seed}else return prng})(prng,shortseed,"global"in options?options.global:this==math,options.state)}function ARC4(key){var t,keylen=key.length,me=this,i=0,j=me.i=me.j=0,s=me.S=[];if(!keylen){key=[keylen++]}while(i<width){s[i]=i++}for(i=0;i<width;i++){s[i]=s[j=mask&j+key[i%keylen]+(t=s[i])];s[j]=t}(me.g=function(count){var t,r=0,i=me.i,j=me.j,s=me.S;while(count--){t=s[i=mask&i+1];r=r*width+s[mask&(s[i]=s[j=mask&j+t])+(s[j]=t)]}me.i=i;me.j=j;return r})(width)}function copy(f,t){t.i=f.i;t.j=f.j;t.S=f.S.slice();return t}function flatten(obj,depth){var result=[],typ=typeof obj,prop;if(depth&&typ=="object"){for(prop in obj){try{result.push(flatten(obj[prop],depth-1))}catch(e){}}}return result.length?result:typ=="string"?obj:obj+"\0"}function mixkey(seed,key){var stringseed=seed+"",smear,j=0;while(j<stringseed.length){key[mask&j]=mask&(smear^=key[mask&j]*19)+stringseed.charCodeAt(j++)}return tostring(key)}function autoseed(){try{var out;if(nodecrypto&&(out=nodecrypto.randomBytes)){out=out(width)}else{out=new Uint8Array(width);(global.crypto||global.msCrypto).getRandomValues(out)}return tostring(out)}catch(e){var browser=global.navigator,plugins=browser&&browser.plugins;return[+new Date,global,plugins,global.screen,tostring(pool)]}}function tostring(a){return String.fromCharCode.apply(0,a)}mixkey(math.random(),pool);if(module.exports){module.exports=seedrandom;try{nodecrypto=require("crypto")}catch(ex){}}else{math["seed"+rngname]=seedrandom}})(typeof self!=="undefined"?self:commonjsGlobal,[],Math)})(seedrandom$1);var alea=alea$1.exports;var xor128=xor128$1.exports;var xorwow=xorwow$1.exports;var xorshift7=xorshift7$1.exports;var xor4096=xor4096$1.exports;var tychei=tychei$1.exports;var sr=seedrandom$1.exports;sr.alea=alea;sr.xor128=xor128;sr.xorwow=xorwow;sr.xorshift7=xorshift7;sr.xor4096=xor4096;sr.tychei=tychei;var seedrandom=sr;class RandomMechanism{static createSequentialSelector(values){return values}static createRandomSelector(values){const resValues=[];const originValues=values.slice();const seed="randomSelector";const random=seedrandom(seed);while(originValues.length>0){const randomValue=random();const randomNumber=Math.floor(randomValue*originValues.length);resValues.push(originValues[randomNumber]);originValues.splice(randomNumber,1)}return resValues}}class ValueFromListInfo{constructor(valueType,values,selector=""){this.m_valueType="";this.m_values=[];this.m_selector="";this.m_valueType=valueType;this.m_values=values;this.m_selector=selector}get valueType(){return this.m_valueType}set valueType(valueType){this.m_valueType=valueType}get values(){return this.m_values}set values(values){this.m_values=values}get selector(){return this.m_selector}set selector(selector){this.m_selector=selector}}class ExpressionCalculator{constructor(mode){this.m_mode=mode}calculate(){}}class ValueFromList extends ExpressionCalculator{constructor(mode,info){super(mode);this.m_values=[];this.m_info=info}calculate(){if(this.m_info.selector=="sequential"){this.m_values=RandomMechanism.createSequentialSelector(this.m_info.values)}else if(this.m_info.selector=="random"){this.m_values=RandomMechanism.createRandomSelector(this.m_info.values)}return this.m_values}}const regex=/(\w+)\((.*?)\)/;class ExpressionInfo{constructor(mode,paramsString){this.m_mode=mode;this.m_paramsString=paramsString}}class ExpressionParser{static parse(expressionStr){const match=expressionStr.trim().match(regex);if(!match){console.error(TEMPLATE_TAG,"parse expression failed",expressionStr);return null}const mode=match[1];const paramsString=match[2];return new ExpressionInfo(mode,paramsString)}static parseValueFromListInfo(paramsString){const typeMatch=paramsString.match(/type="([^"]*)"/);const valueMatch=paramsString.match(/value="(\[.*?\])"/);const modeMatch=paramsString.match(/mode="([^"]*)"/);const dataType=typeMatch?typeMatch[1]:null;const jsonPart=valueMatch?valueMatch[1]:null;const mode=modeMatch?modeMatch[1]:null;if(dataType==null||jsonPart==null||mode==null){return new ValueFromListInfo("",[],"")}return new ValueFromListInfo(dataType,this.parseValues(jsonPart),mode)}static parseValues(valueString){const values=[];const jsonArray=JSON.parse(valueString);jsonArray.forEach((jsonArrayItem=>{values.push(jsonArrayItem)}));return values}}class Expression{constructor(expression){this.m_calculator=null;this.m_expressionStr=expression}parse(){const expressionInfo=ExpressionParser.parse(this.m_expressionStr);if(expressionInfo==null){console.error(TEMPLATE_TAG,"ExpressionParser parse failed",this.m_expressionStr);return}let expressionParas;switch(expressionInfo.m_mode){case"valueFromList":expressionParas=ExpressionParser.parseValueFromListInfo(expressionInfo.m_paramsString);this.m_calculator=new ValueFromList(expressionInfo.m_mode,expressionParas);break;default:console.error(TEMPLATE_TAG,"There is no matching mode in expression",this.m_expressionStr)}}update(){if(!this.m_calculator){console.error(TEMPLATE_TAG,"The m_calculator is null in expression ",this.m_expressionStr);return[]}return this.m_calculator.calculate()}static isValid(jsonStr){const content=JSON.parse(jsonStr);const color=content.styles[0].fill.content.solid.color;if(typeof color==="string"){return true}else{return false}}}var TextCommandType;(function(TextCommandType){TextCommandType[TextCommandType["CT_ENTER_EDIT_STATE"]=0]="CT_ENTER_EDIT_STATE";TextCommandType[TextCommandType["CT_EXIT_EDIT_STATE"]=1]="CT_EXIT_EDIT_STATE";TextCommandType[TextCommandType["CT_INPUT_STR"]=2]="CT_INPUT_STR";TextCommandType[TextCommandType["CT_INPUT_RICH_STR"]=3]="CT_INPUT_RICH_STR";TextCommandType[TextCommandType["CT_BACK_DELETE"]=4]="CT_BACK_DELETE";TextCommandType[TextCommandType["CT_FORWARD_DELETE"]=5]="CT_FORWARD_DELETE";TextCommandType[TextCommandType["CT_START_COMPOSE"]=6]="CT_START_COMPOSE";TextCommandType[TextCommandType["CT_PREEDIT_COMPOSE"]=7]="CT_PREEDIT_COMPOSE";TextCommandType[TextCommandType["CT_END_COMPOSE"]=8]="CT_END_COMPOSE";TextCommandType[TextCommandType["CT_FORCE_REFRESH"]=9]="CT_FORCE_REFRESH";TextCommandType[TextCommandType["CT_MOVE_CURSOR_LR"]=16]="CT_MOVE_CURSOR_LR";TextCommandType[TextCommandType["CT_MOVE_CURSOR_UPDOWN"]=17]="CT_MOVE_CURSOR_UPDOWN";TextCommandType[TextCommandType["CT_MOVE_CURSOR_LINE_BEGIN_END"]=18]="CT_MOVE_CURSOR_LINE_BEGIN_END";TextCommandType[TextCommandType["CT_MOVE_CURSOR_BY_INDEX"]=19]="CT_MOVE_CURSOR_BY_INDEX";TextCommandType[TextCommandType["CT_MOVE_CURSOR_BY_POS"]=20]="CT_MOVE_CURSOR_BY_POS";TextCommandType[TextCommandType["CT_MOVE_SELECT_HANDLE_BY_POS"]=21]="CT_MOVE_SELECT_HANDLE_BY_POS";TextCommandType[TextCommandType["CT_SELECT_CONTENT"]=32]="CT_SELECT_CONTENT";TextCommandType[TextCommandType["CT_SELECT_LR_CONTENT"]=33]="CT_SELECT_LR_CONTENT";TextCommandType[TextCommandType["CT_SELECT_UPDOWN_CONTENT"]=34]="CT_SELECT_UPDOWN_CONTENT";TextCommandType[TextCommandType["CT_SELECT_MOUSE_CONTENT"]=35]="CT_SELECT_MOUSE_CONTENT";TextCommandType[TextCommandType["CT_SELECT_ALL_CONTENT"]=36]="CT_SELECT_ALL_CONTENT";TextCommandType[TextCommandType["CT_EDIT_FONT"]=48]="CT_EDIT_FONT";TextCommandType[TextCommandType["CT_EDIT_COLOR"]=49]="CT_EDIT_COLOR";TextCommandType[TextCommandType["CT_EDIT_ALPHA"]=50]="CT_EDIT_ALPHA";TextCommandType[TextCommandType["CT_EDIT_BGCOLOR"]=51]="CT_EDIT_BGCOLOR";TextCommandType[TextCommandType["CT_EDIT_BG_ALPHA"]=52]="CT_EDIT_BG_ALPHA";TextCommandType[TextCommandType["CT_EDIT_SIZE"]=53]="CT_EDIT_SIZE";TextCommandType[TextCommandType["CT_EDIT_BOLD"]=54]="CT_EDIT_BOLD";TextCommandType[TextCommandType["CT_EDIT_ITALIC"]=55]="CT_EDIT_ITALIC";TextCommandType[TextCommandType["CT_EDIT_UNDERLINE"]=56]="CT_EDIT_UNDERLINE";TextCommandType[TextCommandType["CT_EDIT_OUTLINE"]=57]="CT_EDIT_OUTLINE";TextCommandType[TextCommandType["CT_EDIT_OUTLINE_COLOR"]=58]="CT_EDIT_OUTLINE_COLOR";TextCommandType[TextCommandType["CT_EDIT_OUTLINE_ALPHA"]=59]="CT_EDIT_OUTLINE_ALPHA";TextCommandType[TextCommandType["CT_EDIT_OUTLINE_WIDTH"]=60]="CT_EDIT_OUTLINE_WIDTH";TextCommandType[TextCommandType["CT_EDIT_SHADOW"]=61]="CT_EDIT_SHADOW";TextCommandType[TextCommandType["CT_EDIT_SHADOW_COLOR"]=62]="CT_EDIT_SHADOW_COLOR";TextCommandType[TextCommandType["CT_EDIT_SHADOW_ALPHA"]=63]="CT_EDIT_SHADOW_ALPHA";TextCommandType[TextCommandType["CT_EDIT_SHADOW_SMOOTH"]=64]="CT_EDIT_SHADOW_SMOOTH";TextCommandType[TextCommandType["CT_EDIT_SHADOW_OFFSET"]=65]="CT_EDIT_SHADOW_OFFSET";TextCommandType[TextCommandType["CT_EDIT_EFFECT_STYLE"]=66]="CT_EDIT_EFFECT_STYLE";TextCommandType[TextCommandType["CT_EDIT_TEXT_PRESET_STYLE_PARAM"]=67]="CT_EDIT_TEXT_PRESET_STYLE_PARAM";TextCommandType[TextCommandType["CT_EDIT_TEXT_PARAM"]=68]="CT_EDIT_TEXT_PARAM";TextCommandType[TextCommandType["CT_EDIT_TEMPLATE_TEXT_STYLE"]=69]="CT_EDIT_TEMPLATE_TEXT_STYLE";TextCommandType[TextCommandType["CT_EDIT_RESET_TEXT_CONTEXT"]=70]="CT_EDIT_RESET_TEXT_CONTEXT";TextCommandType[TextCommandType["CT_EDIT_DEFAULT_STYLE"]=71]="CT_EDIT_DEFAULT_STYLE";TextCommandType[TextCommandType["CT_EDIT_CONVERT_CASE"]=72]="CT_EDIT_CONVERT_CASE";TextCommandType[TextCommandType["CT_EDIT_FILL"]=73]="CT_EDIT_FILL";TextCommandType[TextCommandType["CT_EDIT_MULTI_STROKE"]=74]="CT_EDIT_MULTI_STROKE";TextCommandType[TextCommandType["CT_EDIT_MULTI_SHADOW"]=75]="CT_EDIT_MULTI_SHADOW";TextCommandType[TextCommandType["CT_EDIT_MULTI_SHADOW_STROKE"]=76]="CT_EDIT_MULTI_SHADOW_STROKE";TextCommandType[TextCommandType["CT_EDIT_MULTI_INNER_SHADOW"]=77]="CT_EDIT_MULTI_INNER_SHADOW";TextCommandType[TextCommandType["CT_EDIT_KEY_WORD_INFO"]=78]="CT_EDIT_KEY_WORD_INFO";TextCommandType[TextCommandType["CT_EDIT_BLOOM_STYLE"]=79]="CT_EDIT_BLOOM_STYLE";TextCommandType[TextCommandType["CT_EDIT_BACKGROUND_STYLE"]=80]="CT_EDIT_BACKGROUND_STYLE";TextCommandType[TextCommandType["CT_COMMAND_BATCH_PROCESS"]=128]="CT_COMMAND_BATCH_PROCESS";TextCommandType[TextCommandType["CT_GET_PLAIN_STR"]=256]="CT_GET_PLAIN_STR";TextCommandType[TextCommandType["CT_GET_RICH_STR"]=257]="CT_GET_RICH_STR";TextCommandType[TextCommandType["CT_GET_CURSOR_RECT"]=258]="CT_GET_CURSOR_RECT";TextCommandType[TextCommandType["CT_GET_CURSOR_CHAR_INDEX"]=259]="CT_GET_CURSOR_CHAR_INDEX";TextCommandType[TextCommandType["CT_GET_CHAR_RECT"]=260]="CT_GET_CHAR_RECT";TextCommandType[TextCommandType["CT_GET_SELECT_RANGE"]=261]="CT_GET_SELECT_RANGE";TextCommandType[TextCommandType["CT_GET_ERROR_INFO"]=262]="CT_GET_ERROR_INFO";TextCommandType[TextCommandType["CT_GET_EDIT_CONTEXT_INFO"]=263]="CT_GET_EDIT_CONTEXT_INFO";TextCommandType[TextCommandType["CT_GET_SELECT_HANDLE_RECT"]=264]="CT_GET_SELECT_HANDLE_RECT";TextCommandType[TextCommandType["CT_GET_TEXT_BASE_STYLE"]=267]="CT_GET_TEXT_BASE_STYLE";TextCommandType[TextCommandType["CT_NONE"]=512]="CT_NONE"})(TextCommandType||(TextCommandType={}));var TextUtils;(function(TextUtils){TextUtils.remap=function(value,fromMin,fromMax,toMin,toMax){if(fromMin===fromMax)return toMax;const normal=(value-fromMin)/(fromMax-fromMin);return toMin+(toMax-toMin)*normal};TextUtils.clamp=function(value,min,max){return Math.min(Math.max(value,min),max)};TextUtils.mix=function(start,end,value){return start+(end-start)*value};TextUtils.getTightRect=function(letter,outlineMaxWidth,expandAffector=.2){const pixelHeight=letter.letterStyle.fontSize*300/72;const rectDistance=pixelHeight*outlineMaxWidth;return[letter.rect.x+rectDistance,letter.rect.y+rectDistance,letter.rect.width-rectDistance*2+expandAffector*pixelHeight,letter.rect.height-rectDistance*2+expandAffector*pixelHeight]};TextUtils.getLettersCenter=function(letters,range){const center=[0,0];let letterCount=0;for(let i=range[0];i<range[1];i++){const letter=letters.get(i);if(letter.utf8==="\n")continue;center[0]+=letter.initialPosition.x;center[1]+=letter.initialPosition.y;letterCount++}center[0]/=letterCount;center[1]/=letterCount;return center};TextUtils.getTightRectByRange=function(letters,range,outlineMaxWidth,expandAffector=.2){if(range[0]<0||range[1]>letters.size())return[0,0,-1,-1];const tightPoints=[];const center=[0,0];let letterCount=0;for(let i=range[0];i<range[1];i++){const letter=letters.get(i);if(letter.utf8==="\n")continue;const rect=TextUtils.getTightRect(letter,outlineMaxWidth,expandAffector);if(tightPoints.length===0){tightPoints[0]=rect[0];tightPoints[1]=rect[1];tightPoints[2]=rect[0]+rect[2];tightPoints[3]=rect[1]+rect[3]}else{tightPoints[0]=Math.min(tightPoints[0],rect[0]);tightPoints[1]=Math.min(tightPoints[1],rect[1]);tightPoints[2]=Math.max(tightPoints[2],rect[0]+rect[2]);tightPoints[3]=Math.max(tightPoints[3],rect[1]+rect[3])}center[0]+=letter.initialPosition.x;center[1]+=letter.initialPosition.y;letterCount++}const width=tightPoints[2]-tightPoints[0];const height=tightPoints[3]-tightPoints[1];center[0]/=letterCount;center[1]/=letterCount;return[center[0]-width*.5,center[1]-height*.5,width,height]};TextUtils.getRectCenter=function(rect){return[rect[0]+rect[2]*.5,rect[1]+rect[3]*.5]}})(TextUtils||(TextUtils={}));class InitValues{constructor(textComp,start=-1,end=-1){this.selectStart=-1;this.selectEnd=-1;this.letterList=[];this.letterInitialPositionMap={};this.letterInstanceColorMap={};this.letterFontSizeMap={};this.m_cloneLetters=null;textComp.typeSettingDirty=true;textComp.forceTypeSetting();this.letters=textComp.letters.clone();this.backgrounds=textComp.backgrounds.clone();this.textStr=textComp.str;this.selectStart=start;this.selectEnd=end}recordForPerformance(textComp){this.letterList=[];this.letterInitialPositionMap={};this.m_cloneLetters=textComp.letters.clone();for(let i=0;i<this.m_cloneLetters.size();i++){const letter=this.m_cloneLetters.get(i);this.letterList.push(letter);this.letterInitialPositionMap[i]=[letter.initialPosition.x,letter.initialPosition.y,0];this.letterFontSizeMap[i]=letter.letterStyle.fontSize;const color=letter.instanceColor;this.letterInstanceColorMap[i]=[color.r,color.g,color.b,color.a]}}recover(textComp){if(this.m_cloneLetters===null)return;textComp.letters=this.m_cloneLetters;textComp.forceTypeSetting()}}var Amaz$d=effect.Amaz;const PAGE_POST_MOTION=new Group;const LINE_POST_MOTION=new Group;const WORD_POST_MOTION=new Group;const WORD_UNREAD_POST_MOTION=new Group;const LINE_UNREAD_POST_MOTION=new Group;const PAGE_MOTION=new Group;const LINE_MOTION=new Group;const WORD_MOTION=new Group;const WORD_UNREAD_MOTION=new Group;const LINE_UNREAD_MOTION=new Group;var ValueGeneratorType;(function(ValueGeneratorType){ValueGeneratorType["EXPRESSION"]="expression";ValueGeneratorType["KEYFRAME"]="keyFrame";ValueGeneratorType["TEMP"]="temp"})(ValueGeneratorType||(ValueGeneratorType={}));class ValueGeneratorContext{constructor(textComp,initValues,selector,overlayMode,callbackParams,targetAttribute,generatoType,context){this.textComp=null;this.initValues=null;this.selector=null;this.callbackParams=null;this.generatoType=ValueGeneratorType.KEYFRAME;this.textComp=textComp;this.initValues=initValues;this.selector=selector;this.overlayMode=overlayMode;this.callbackParams=callbackParams;this.targetAttribute=targetAttribute;this.generatoType=generatoType;this.generator=context}}class CaptionRuntimeParams{constructor(){this.word_start_index=0;this.line_start_index=0;this.page_start_index=0;this.keyword_start_index=0}}class CaptionAnimationController extends TextBaseAnimationController{constructor(textComp){super();this.m_captionInfo=new CaptionInfo;this.m_captionInfoDirty=false;this.m_currentCaptionPage=null;this.m_enabled=false;this.m_callbackParams={};this.m_runtimeParams=new CaptionRuntimeParams;this.m_pageAnimations=[];this.m_lineAnimations=[];this.m_wordAnimations=[];this.m_keywordAnimations=[];this.m_selectors={};this.m_needMerge=false;this.m_richText=null;this.m_needGenerateOnePage=false;this.m_textComp=textComp;this.m_initValues=new InitValues(textComp);textComp.typeSettingParam.lineSpacingMode=Amaz$d.LineSpacingMode.ADAPTIVE}get enabled(){return this.m_enabled}set enabled(value){this.m_enabled=value}set needMerge(value){this.m_needMerge=value}get needMerge(){return this.m_needMerge}get keywordAnimations(){return this.m_keywordAnimations}set richText(richText){this.m_richText=richText}get richText(){return this.m_richText}get captionInfo(){return this.m_captionInfo}set needGenerateOnePage(value){this.m_needGenerateOnePage=value}get needGenerateOnePage(){return this.m_needGenerateOnePage}setAnimsParameters(animationArr){var _a;(_a=this.m_richText)===null||_a===void 0?void 0:_a.setAnimationParameters(animationArr)}setRichTextParameters(paras){if(this.m_richText){this.m_richText.parameters=paras}}setCaptionDurationInfo(captionDurationInfo){if(captionDurationInfo!=null&&Object.keys(captionDurationInfo).length!=0&&"words"in captionDurationInfo){const words=captionDurationInfo.words;const validWords=[];for(let i=0;i<words.length;i++){if(words[i].start_time!=words[i].end_time)validWords.push(words[i])}for(let i=0;i<validWords.length-1;i++){validWords[i].end_time=validWords[i+1].start_time}for(let i=words.length-2;i>=0;i--){if(words[i].start_time==words[i].end_time){words[i].start_time=words[i+1].start_time;words[i].end_time=words[i+1].start_time}}}this.m_captionInfo.setCaptionDurationInfo(captionDurationInfo);this.m_captionInfoDirty=true;this.m_enabled=true}setCaptionRuntimeParams(runtimeParams){if(runtimeParams==null){return}if(runtimeParams.word_start_index!=null){this.m_runtimeParams.word_start_index=runtimeParams.word_start_index}if(runtimeParams.line_start_index!=null){this.m_runtimeParams.line_start_index=runtimeParams.line_start_index}if(runtimeParams.page_start_index!=null){this.m_runtimeParams.page_start_index=runtimeParams.page_start_index}if(runtimeParams.keyword_start_index!=null){this.m_runtimeParams.keyword_start_index=runtimeParams.keyword_start_index}}_getBasicKeywordSize(){let index=0;let letterIndex=0;if(this.m_captionInfo.caption_duration_info!=null){const words=this.m_captionInfo.caption_duration_info.words;let found=false;for(let i=0;i<words.length;i++){const text=words[i].text;for(let j=0;j<text.length;j++){if(!["\n"].includes(words[i].text)){if(words[i].is_key){letterIndex=index;found=true;break}index++}}if(found)break}}index=0;const letters=this.m_initValues.letters;for(let i=0;i<letters.size();i++){const letter=letters.get(i);if(letter.utf8!="\n"){if(index==letterIndex){return letter.letterStyle.fontSize}index++}}return DEFAULT_FONT_SIZE}static _getAbsoluteKeywordSize(caption_params){var _a;const keyword_rich_text_str=caption_params.keyword_rich_text;if(keyword_rich_text_str!=null&&isJson(keyword_rich_text_str)){const keyword_rich_text=JSON.parse(keyword_rich_text_str);if(((_a=keyword_rich_text===null||keyword_rich_text===void 0?void 0:keyword_rich_text.styles[0])===null||_a===void 0?void 0:_a.size)!=null){return keyword_rich_text.styles[0].size}}return-1}_getKeywordSupersize(){const caption_params=this.m_captionInfo.caption_params;if(caption_params.keyword_supersize==null)return 1;else return caption_params.keyword_supersize}_getSupersize(){const caption_params=this.m_captionInfo.caption_params;if(caption_params.keyword_supersize==null)return 1;let supersize=caption_params.keyword_supersize;if(["line","page"].includes(caption_params.keyword_isolation)&&caption_params.keyword_isolation_supersize!=null&&caption_params.keyword_isolation_supersize>0){supersize*=caption_params.keyword_isolation_supersize}return supersize}getKeywordFontSize(){this.updateCaptionInfo();return{keyword_supersize:this._getKeywordSupersize(),keyword_font_size:this._getBasicKeywordSize()*this._getSupersize()}}_updateKeywordSupersize(){const caption_params=this.m_captionInfo.caption_params;const absoluteKeywordSize=CaptionAnimationController._getAbsoluteKeywordSize(caption_params);if(absoluteKeywordSize>0){let size=absoluteKeywordSize/this._getBasicKeywordSize();if(["line","page"].includes(caption_params.keyword_isolation)&&caption_params.keyword_isolation_supersize!=null&&caption_params.keyword_isolation_supersize>0){size/=caption_params.keyword_isolation_supersize}caption_params.keyword_supersize=size}}getSplittingResult(){this.updateCaptionInfo();return{splitting_result:this.m_captionInfo.splitting_result}}query(queries){const outJson={queries:{}};if(queries.includes("keyword_supersize")||queries.includes("keyword_font_size")){const sizes=this.getKeywordFontSize();Object.assign(outJson.queries,sizes)}if(queries.includes("splitting_result")){const caption_duration_info=this.getSplittingResult();Object.assign(outJson.queries,caption_duration_info)}return outJson}setTextCapital(capital){if(capital==null){return}const changed=this.m_captionInfo.setCapital(capital);if(changed){this.m_captionInfoDirty=true}this.m_enabled=true}setKeyFrameParams(keyframeParams){super.setKeyFrameParams(keyframeParams);this.m_enabled=true}setCaptionParams(captionParams){var _a;this.m_captionInfoDirty=true;this.m_keyFramesDirty=true;this.m_enabled=true;if(Object.keys(captionParams).length==0){this.m_captionInfo.resetCaptionParams()}else{this.m_captionInfo.setCaptionParams(captionParams)}if(captionParams.keyword_supersize_mode=="absolute"){this._updateKeywordSupersize()}if(this.m_captionInfo.isRelative()){const keyword_rich_text_str=this.m_captionInfo.caption_params.keyword_rich_text;if(keyword_rich_text_str!=null&&isJson(keyword_rich_text_str)){const keyword_rich_text=JSON.parse(keyword_rich_text_str);if(((_a=keyword_rich_text===null||keyword_rich_text===void 0?void 0:keyword_rich_text.styles[0])===null||_a===void 0?void 0:_a.size)!=null){this.m_captionInfo.keyword_font_size=keyword_rich_text.styles[0].size;delete keyword_rich_text.styles[0].size;this.m_captionInfo.caption_params.keyword_rich_text=JSON.stringify(keyword_rich_text)}}}}resetCaptionParams(){this.m_captionInfo.resetCaptionParams()}updateCaptionInfo(){if(!this.m_captionInfoDirty)return;this.m_captionInfoDirty=false;this.m_captionInfo.applyKeywordCapital();this.m_captionInfo.updatePages()}findSelector(selectorId){if(this.m_selectors[selectorId]){return this.m_selectors[selectorId]}return null}refreshInitValues(){const range=this.m_textComp.getSelectRange(true);const VALID=3;if(range.x==VALID)this.m_initValues=new InitValues(this.m_textComp,range.y,range.z);else this.m_initValues=new InitValues(this.m_textComp)}_sortKeyframes(){const groups=[PAGE_POST_MOTION,LINE_POST_MOTION,WORD_UNREAD_POST_MOTION,LINE_UNREAD_POST_MOTION,WORD_POST_MOTION,PAGE_MOTION,LINE_MOTION,WORD_UNREAD_MOTION,LINE_UNREAD_MOTION,WORD_MOTION];for(let i=0;i<groups.length;i++){const group=groups[i];KeyframeAttrUtils.sortKeyframesByAttributeSetter(group,CaptionAttributeSetter)}}updateKeyFrames(){PAGE_POST_MOTION.removeAll();LINE_POST_MOTION.removeAll();WORD_UNREAD_POST_MOTION.removeAll();LINE_UNREAD_POST_MOTION.removeAll();WORD_POST_MOTION.removeAll();PAGE_MOTION.removeAll();LINE_MOTION.removeAll();WORD_UNREAD_MOTION.removeAll();LINE_UNREAD_MOTION.removeAll();WORD_MOTION.removeAll();this.m_callbackParams={};if(this.m_keyframeParams==null){return}const keyframes=this.m_keyframeParams.keyframes;if(keyframes==null){return}for(let i=0;i<keyframes.length;i++){const keyframe=keyframes[i];if(keyframe.selector_id==null){continue}const selector=this.findSelector(keyframe.selector_id);if(selector==null){console.error(TEMPLATE_TAG,"keyframe_params error keyframe "+i+" selector is null");continue}let overlayMode="over";if(KeyframeAttrUtils.isValidOverlayMode(keyframe.overlay_mode))overlayMode=keyframe.overlay_mode;const motionContext=new ValueGeneratorContext(this.m_textComp,this.m_initValues,selector,overlayMode,this.m_callbackParams,"",ValueGeneratorType.KEYFRAME,keyframe);motionContext.callbackParams=this.m_callbackParams;const firstPage=this.m_captionInfo.getPage(0);if(firstPage)this.createMotions(keyframe,motionContext,firstPage)}this._sortKeyframes()}createMotions(keyframe,motionContext,firstPage){if(keyframe.k==undefined){console.error(TEMPLATE_TAG,"keyframe.k is undefined");return}if((keyframe.e-keyframe.s)*KEYFRAME_TIME_FACTOR<TIME_EPS){console.error(TEMPLATE_TAG,"keyframe time range too short");return}for(const key in keyframe.k)this.createMotionsForOneProperty(keyframe,motionContext,firstPage,key)}createMotionsForOneProperty(keyframe,motionContext,firstPage,propertyName){const prop=keyframe.k[propertyName];const callback=CaptionAttributeSetter.getValueCallback(propertyName);if(prop==undefined){console.error(TEMPLATE_TAG,"prop is undefined");return}if(prop.length==0){console.error(TEMPLATE_TAG,"prop is empty");return}const interp=Interpolation.Linear;let group=WORD_MOTION;const selectorUnit=motionContext.selector.unit;let startTimeInWidget=0;let endTimeInWidget=0;if(selectorUnit=="word"){const currentWord=firstPage.getCurrentWord(0);if(currentWord!=null){startTimeInWidget=currentWord.m_startTimeInWidget;endTimeInWidget=currentWord.m_endTimeInWidget}group=KeyframeAttrUtils.isPostGroup(propertyName)?WORD_POST_MOTION:WORD_MOTION}else if(selectorUnit=="line"){const currentLine=firstPage.getCurrentLine(0);if(currentLine!=null){startTimeInWidget=currentLine.m_startTimeInWidget;endTimeInWidget=currentLine.m_endTimeInWidget}group=KeyframeAttrUtils.isPostGroup(propertyName)?LINE_POST_MOTION:LINE_MOTION}else if(selectorUnit=="page"){startTimeInWidget=firstPage.m_startTimeInWidget;endTimeInWidget=firstPage.m_endTimeInWidget;group=KeyframeAttrUtils.isPostGroup(propertyName)?PAGE_POST_MOTION:PAGE_MOTION}else if(selectorUnit=="word_unread"){startTimeInWidget=firstPage.m_startTimeInWidget;endTimeInWidget=firstPage.m_endTimeInWidget;group=KeyframeAttrUtils.isPostGroup(propertyName)?WORD_UNREAD_POST_MOTION:WORD_UNREAD_MOTION}else if(selectorUnit=="line_unread"){startTimeInWidget=firstPage.m_startTimeInWidget;endTimeInWidget=firstPage.m_endTimeInWidget;group=KeyframeAttrUtils.isPostGroup(propertyName)?LINE_UNREAD_POST_MOTION:LINE_UNREAD_MOTION}const keyframes=[];let v=prop[0].v;if(prop[0].v instanceof Array&&prop[0].v.length===4&&CaptionAttributeSetter.colorNames.includes(propertyName)){v=ColorRGBA.castJsonArray4fToColorRGBA(prop[0].v)}keyframes.push(new keyframeUnitInfo(v,startTimeInWidget,prop[0].it));for(let i=0;i<prop.length;i++){const t=prop[i].t*KEYFRAME_TIME_FACTOR;let v=prop[i].v;if(prop[i].v instanceof Array&&prop[i].v.length===4&&CaptionAttributeSetter.colorNames.includes(propertyName)){v=ColorRGBA.castJsonArray4fToColorRGBA(prop[i].v)}const keyframeUnit=new keyframeUnitInfo(v,t,prop[i].it);if(keyframeUnit.m_it==="cubic"){keyframeUnit.m_vti=prop[i].vti*KEYFRAME_TIME_FACTOR;keyframeUnit.m_vto=prop[i].vto*KEYFRAME_TIME_FACTOR;if(CaptionAttributeSetter.colorNames.includes(propertyName)){const colorV=ColorRGBA.castJsonArray4fToColorRGBA(prop[i].v);const colorVi=ColorRGBA.castJsonArray4fToColorRGBA(prop[i].vi);const colorV0=ColorRGBA.castJsonArray4fToColorRGBA(prop[i].v0);if(null!==colorV&&null!==colorVi&&null!==colorV0){keyframeUnit.m_vi=colorV.add(colorVi);keyframeUnit.m_vo=colorV.add(colorV0)}}else{keyframeUnit.m_vi=prop[i].v+prop[i].vi;keyframeUnit.m_vo=prop[i].v+prop[i].vo}}if(t-keyframes[keyframes.length-1].m_t>TIME_EPS){keyframes.push(keyframeUnit)}}if(endTimeInWidget-keyframes[keyframes.length-1].m_t>TIME_EPS){let v=prop[prop.length-1].v;if(prop[prop.length-1].v instanceof Array&&prop[prop.length-1].v.length===4){v=ColorRGBA.castJsonArray4fToColorRGBA(prop[prop.length-1].v)}keyframes.push(new keyframeUnitInfo(v,endTimeInWidget,prop[prop.length-1].it))}KeyframeAttrUtils.createOneMotion(keyframes,callback,interp,motionContext,true,group,propertyName).start()}applyKeywordSupersize(range,keyword_supersize){for(let i=0;i<range.length;i+=2){this.m_callbackParams.values.push({name:"s",value:keyword_supersize,start:range[i],end:range[i+1],overlayMode:"mul",selectorUnit:"word"})}}updateKeyword(currentPage){if(currentPage==undefined||currentPage==null){console.error(TEMPLATE_TAG,"updateKeyword currentPage is null")}if(!this.m_captionInfo.caption_params.keyword_rich_text||!this.m_captionInfo.caption_params.enable_keyword){return}const rangeInPage=[];for(let i=0;i<currentPage.m_words.length;i++){if(currentPage.m_words[i].m_isKey){rangeInPage.push(currentPage.m_words[i].startIndex,currentPage.m_words[i].endIndex)}}if(rangeInPage.length>1){if(this.m_captionInfo.isRelative()){const supersize=this._getSupersize();this.applyKeywordSupersize(rangeInPage,supersize)}const keyword_rich_text_str=this.m_captionInfo.getKeywordRichText();this.m_textComp.applyTextStyle(keyword_rich_text_str,rangeInPage)}this.updateExpressions(this.m_keywordAnimations,0)}get currentCaptionPage(){if(this.m_currentCaptionPage)return this.m_currentCaptionPage;else{return null}}resetCurrentCaptionPage(){this.m_currentCaptionPage=null}initFrame(){this.m_textComp.setString(this.m_initValues.textStr,false);const oldLetters=this.m_initValues.letters;const letters=this.m_textComp.letters;if(oldLetters.size()!=letters.size()){console.error(TEMPLATE_TAG,"oldLetters.size() != letters.size()",oldLetters.size(),letters.size())}for(let i=0;i<letters.size();i++){const letter=letters.get(i);const oldLetter=oldLetters.get(i);letter.letterStyle=oldLetter.letterStyle.clone()}this.m_textComp.bloomDirty=true;this.m_textComp.forceTypeSetting();for(let i=0;i<letters.size();i++){const letter=letters.get(i);const oldLetter=oldLetters.get(i);letter.position=oldLetter.position;letter.scale=oldLetter.scale;letter.rotate=oldLetter.rotate;letter.extraMatrix=oldLetter.extraMatrix;letter.instanceColor=oldLetter.instanceColor}const backgrounds=this.m_textComp.backgrounds;const oldBackgrounds=this.m_initValues.backgrounds;if(oldBackgrounds.size()==0){this.m_textComp.backgrounds.clear()}else{for(let i=0;i<backgrounds.size();i++){const oldBg=oldBackgrounds.get(i);backgrounds.set(i,oldBg.clone())}}this.m_keyFramesDirty=true}static convertToLetterIndices(charIndices,lettersVec){const letterIndices=[];if(charIndices.length==0)return letterIndices;let utf16Size=0;const letters=this.getLettersWithoutReturn(lettersVec);const letterSize=letters.length;let charPos=0;let charIndex=charIndices[charPos];for(let i=0;i<letterSize;i++){const letter=letters[i];const curUtf16Size=utf16Size+letter.getUTF16Size();if(curUtf16Size>charIndex){letterIndices.push(i);if(charPos>=charIndices.length-1)break;charIndex=charIndices[++charPos]}utf16Size=curUtf16Size}const count=charIndices.length-letterIndices.length;for(let i=0;i<count;i++)letterIndices.push(letterSize);return letterIndices}updateLetterIndices(){const startPageIndex=0;const endPageIndex=this.m_captionInfo.pages.length-1;const charIndices=[0];let charCount=0;let returnCount=0;for(let pageIndex=startPageIndex;pageIndex<=endPageIndex;pageIndex++){const page=this.m_captionInfo.pages[pageIndex];charCount+=page.m_string.length;for(let j=0;j<page.m_string.length;j++)if(["\n"].includes(page.m_string[j]))returnCount++;charIndices.push(charCount-returnCount)}const letterIndices=CaptionAnimationController.convertToLetterIndices(charIndices,this.m_initValues.letters);for(let pageIndex=startPageIndex;pageIndex<=endPageIndex;pageIndex++){const page=this.m_captionInfo.pages[pageIndex];page.m_utf16StartLetterIndex=letterIndices[pageIndex-startPageIndex];page.m_utf16EndLetterIndex=letterIndices[pageIndex-startPageIndex+1]}}static getLettersWithoutReturn(letters){const lettersWithoutReturn=[];for(let i=0;i<letters.size();i++){const letter=letters.get(i);if(letter.utf8!="\n"){lettersWithoutReturn.push(letter)}}return lettersWithoutReturn}static updateLettersActionType(letters){const letterLineAry=[];let letterInOneLine=[];for(let i=0;i<letters.size();i++){const letter=letters.get(i);if(letter.utf8!="\n")letterInOneLine.push(letter);else{letterLineAry.push(letterInOneLine);letterInOneLine=[]}}if(letterInOneLine.length>0){letterLineAry.push(letterInOneLine)}for(let i=0;i<letterLineAry.length;i++){const letterLine=letterLineAry[i];if(letterLine.length>0){for(let j=0;j<letterLine.length;j++){const letter=letterLine[j];if(letter.utf8==" ")letter.actionType=Amaz$d.LetterActionType.TYPESETTING_DISABLE;else break}for(let j=letterLine.length-1;j>=0;j--){const letter=letterLine[j];if(letter.utf8==" ")letter.actionType=Amaz$d.LetterActionType.TYPESETTING_DISABLE;else break}}}}recoverLetters(str,startIndex=0){this.m_textComp.setString(str,false);const oldLetters=CaptionAnimationController.getLettersWithoutReturn(this.m_initValues.letters);const letters=CaptionAnimationController.getLettersWithoutReturn(this.m_textComp.letters);if(oldLetters.length<letters.length+startIndex){console.error(TEMPLATE_TAG,"without returns, oldLetters.length < letters.length + startIndex",oldLetters.length,letters.length,startIndex)}for(let i=0;i<letters.length;i++){const letter=letters[i];const oldLetter=oldLetters[i+startIndex];letter.letterStyle=oldLetter.letterStyle.clone()}CaptionAnimationController.updateLettersActionType(this.m_textComp.letters)}switchPage(currentPage){this.m_currentCaptionPage=currentPage;this.updateLetterIndices();if(this.m_currentCaptionPage.m_utf16StartLetterIndex<0){console.error(TEMPLATE_TAG,"utf16StartLetterIndex < 0");return}this.recoverLetters(this.m_currentCaptionPage.m_string,this.m_currentCaptionPage.m_utf16StartLetterIndex)}_updateLetterVirtualReturn(currentPage,textComp){var _a;if(currentPage==null||textComp==null){return}for(let j=0;j<currentPage.m_lines.length-1;j++){const line=currentPage.m_lines[j];const endUtf16Idx=(_a=line.endWord)===null||_a===void 0?void 0:_a.endIndex;if(endUtf16Idx!==undefined){const endLetterIdx=textComp.convertIdUToL(endUtf16Idx-1);const letter=textComp.letters.get(endLetterIdx);if(letter.utf8!="\n"){letter.actionType=Amaz$d.LetterActionType.TYPESETTING_AUTO_LINE_BREAK}}}}updateSelectors(timeInPage){this.m_selectors={};if(this.m_keyframeParams==null){return}const selectors=this.m_keyframeParams.selectors;if(selectors==null){return}for(let i=0;i<selectors.length;i++){const selector=new Selector(selectors[i]);this.m_selectors[selector.id]=selector}Object.values(this.m_selectors).forEach((selector=>{selector.captionPage=this.currentCaptionPage;selector.update(timeInPage)}))}doesAnimEffectExist(){if(this.m_keyframeParams==null){return false}if(this.m_captionInfo.caption_params.max_units_per_line!=null&&this.m_captionInfo.caption_params.max_units_per_line>0){return true}if(this.m_keyframeParams.selectors==null){return false}if("keyframes"in this.m_keyframeParams&&this.m_keyframeParams.keyframes.length>0){return true}if("value_generators"in this.m_keyframeParams&&this.m_keyframeParams.value_generators.length>0){return true}return false}update(timeInWidget){var _a,_b,_c,_d,_e;if(!this.m_enabled)return timeInWidget;this.updateCaptionInfo();const currentPage=this.m_captionInfo.getPage(timeInWidget);let timeInPage=timeInWidget;if(!((_a=this.m_richText)===null||_a===void 0?void 0:_a.hasAnimation())&&!this.needGenerateOnePage){if(currentPage!=null){timeInPage=currentPage.getTimeInPage(timeInWidget);this.switchPage(currentPage)}else{const pageLength=this.m_captionInfo.pages.length;if(pageLength>0){const wordEndtimeInWidget=this.m_captionInfo.pages[pageLength-1].m_endTimeInWidget;if(!Number.isNaN(wordEndtimeInWidget)&&timeInWidget>=wordEndtimeInWidget&&this.doesAnimEffectExist()){console.log("[CaptionAnimationController] time exceeds limit and contains anim effect, clear text!");this.m_textComp.setString("",false)}else if(!this.doesAnimEffectExist()){this.needGenerateOnePage=true}}}}if(this.m_keyframeParams!=null){if(this.m_keyFramesDirty==true){this.m_keyFramesDirty=false;this.updateSelectors(timeInPage);this.updateKeyFrames()}}this.m_callbackParams.values=[];if(this.m_keyframeParams&&"value_generators"in this.m_keyframeParams){this.updateValueGenerators(this.m_keyframeParams.value_generators)}if(this.needGenerateOnePage){if(this.m_captionInfo.caption_duration_info_capital==null){return timeInWidget}const page=CaptionInfo.generateOnePage(this.m_captionInfo.caption_duration_info_capital);if(this.m_captionInfo.pages.length!==0)this.m_captionInfo.pages=[];this.m_captionInfo.pages.push(page);this.m_currentCaptionPage=page;if(this.m_textComp.str.length==page.m_string.length){this.m_textComp.str=page.m_string}const range=[];const wordLength=this.m_currentCaptionPage.m_words.length;for(let i=0;i<wordLength;i++){if(this.m_currentCaptionPage.m_words[i].m_isKey){range.push(this.m_currentCaptionPage.m_words[i].startIndex,this.m_currentCaptionPage.m_words[i].endIndex)}}if(range.length>1&&this.m_captionInfo.hasValidCaptionParams()){this.m_textComp.applyTextStyle(this.m_captionInfo.getKeywordRichTextWithSize(),range)}this.updateExpressions(this.m_keywordAnimations,0);this.flushCallbackQueue();this.m_textComp.forceTypeSetting();this.m_needMerge=false;if((_b=this.m_richText)===null||_b===void 0?void 0:_b.hasAnimation()){(_c=this.m_richText)===null||_c===void 0?void 0:_c.onUpdate(timeInWidget+this.m_richText.startTimeInTrack)}return timeInWidget}if((_d=this.m_richText)===null||_d===void 0?void 0:_d.hasAnimation()){(_e=this.m_richText)===null||_e===void 0?void 0:_e.onUpdate(timeInWidget+this.m_richText.startTimeInTrack);return timeInWidget}this.m_textComp.forceTypeSetting();let wordCountInPage=-1;let wordCountInLine=-1;let currentLine=null;let currentWord=null;if(currentPage){const startTimeInPage=currentPage.m_startTimeInWidget;currentLine=currentPage.getCurrentLine(timeInPage);currentWord=currentPage.getCurrentWord(timeInPage);const max_lines_per_page=this.m_captionInfo.caption_params.max_lines_per_page;let max_units_per_line=this.m_captionInfo.caption_params.max_units_per_line;max_units_per_line=max_units_per_line==0?1e5:max_units_per_line;wordCountInPage=max_lines_per_page*max_units_per_line;wordCountInLine=max_units_per_line;this.updatePageAnimations(timeInPage,wordCountInPage,currentPage,startTimeInPage);if(currentLine)this.updateLineAnimations(timeInPage,wordCountInLine,currentLine,startTimeInPage);WORD_UNREAD_MOTION.update(timeInPage,true,{wordCount:wordCountInPage,targetStart:currentPage.m_startTimeInWidget-startTimeInPage,targetEnd:currentPage.m_endTimeInWidget-startTimeInPage});LINE_UNREAD_MOTION.update(timeInPage,true,{wordCount:wordCountInLine,targetStart:currentPage.m_startTimeInWidget-startTimeInPage,targetEnd:currentPage.m_endTimeInWidget-startTimeInPage});this.flushCallbackQueue();this.updateKeyword(currentPage);this.m_textComp.forceTypeSetting();if(currentWord)this.updateWordAnimations(timeInPage,1,currentWord,startTimeInPage);this.flushCallbackQueue();this.m_textComp.forceTypeSetting();this.updatePagePostAnimations(timeInPage,wordCountInPage,currentPage,startTimeInPage);if(currentLine)this.updateLinePostAnimations(timeInPage,wordCountInLine,currentLine,startTimeInPage);WORD_UNREAD_POST_MOTION.update(timeInPage,true,{wordCount:wordCountInPage,targetStart:currentPage.m_startTimeInWidget-startTimeInPage,targetEnd:currentPage.m_endTimeInWidget-startTimeInPage});LINE_UNREAD_POST_MOTION.update(timeInPage,true,{wordCount:wordCountInLine,targetStart:currentPage.m_startTimeInWidget-startTimeInPage,targetEnd:currentPage.m_endTimeInWidget-startTimeInPage});if(currentWord)this.updateWordPostAnimations(timeInPage,1,currentWord,startTimeInPage);this.flushCallbackQueue()}return timeInPage}updateValueGenerators(valueGenerators){this.m_pageAnimations=[];this.m_lineAnimations=[];this.m_wordAnimations=[];this.m_keywordAnimations=[];if(valueGenerators==null){return}for(let i=0;i<valueGenerators.length;i++){const valueGenerator=valueGenerators[i];const selector=this.findSelector(valueGenerator.selector_id);if(selector==null){console.error(TEMPLATE_TAG,"updateValueGenerators error valueGenerator "+i+" selector is null");continue}const generator=valueGenerator.generator;const expression=new Expression(generator);const targetAttribute=valueGenerator.target_attribute;const valueGeneratorContext=new ValueGeneratorContext(this.m_textComp,this.m_initValues,selector,"over",this.m_callbackParams,targetAttribute,ValueGeneratorType.EXPRESSION,expression);const selectorUnit=selector.unit;switch(selectorUnit){case SelectorUnit.WORD:this.m_wordAnimations.push(valueGeneratorContext);break;case SelectorUnit.LINE:this.m_lineAnimations.push(valueGeneratorContext);break;case SelectorUnit.PAGE:this.m_pageAnimations.push(valueGeneratorContext);break;case SelectorUnit.KEYWORD:this.m_keywordAnimations.push(valueGeneratorContext);break;default:console.error(TEMPLATE_TAG,"");continue}}}updateExpressions(valueGeneratorContext,timeInPage){var _a,_b,_c,_d;if(!this.m_currentCaptionPage){return}const vgLen=valueGeneratorContext.length;for(let i=0;i<vgLen;i++){const vg=valueGeneratorContext[i];if(vg.generatoType!="expression"){continue}const expression=vg.generator;expression.parse();const expressionRes=expression.update();const seletor=vg.selector;const selectorUnit=seletor===null||seletor===void 0?void 0:seletor.unit;switch(selectorUnit){case SelectorUnit.WORD:const currentWord=this.m_currentCaptionPage.getCurrentWord(timeInPage);if(currentWord!=null){const globalIndex=currentWord.m_globalIndex;const value=expressionRes[(globalIndex+this.m_runtimeParams.word_start_index)%expressionRes.length];this.m_callbackParams.values.push({name:vg.targetAttribute,value:value,start:currentWord.startIndex,end:currentWord.endIndex,overlayMode:vg.overlayMode,selectorUnit:selectorUnit})}break;case SelectorUnit.LINE:const currentLine=this.m_currentCaptionPage.getCurrentLine(timeInPage);if(currentLine){const globalIndex=currentLine.m_globalIndex;const value=expressionRes[(globalIndex+this.m_runtimeParams.line_start_index)%expressionRes.length];this.m_callbackParams.values.push({name:vg.targetAttribute,value:value,start:(_a=currentLine.startWord)===null||_a===void 0?void 0:_a.startIndex,end:(_b=currentLine.endWord)===null||_b===void 0?void 0:_b.endIndex,overlayMode:vg.overlayMode,selectorUnit:selectorUnit})}break;case SelectorUnit.PAGE:const globalIndex=this.m_currentCaptionPage.m_globalIndex;const value=expressionRes[(globalIndex+this.m_runtimeParams.page_start_index)%expressionRes.length];this.m_callbackParams.values.push({name:vg.targetAttribute,value:value,start:(_c=this.m_currentCaptionPage.startWord)===null||_c===void 0?void 0:_c.startIndex,end:(_d=this.m_currentCaptionPage.endWord)===null||_d===void 0?void 0:_d.endIndex,overlayMode:vg.overlayMode,selectorUnit:selectorUnit});break;case SelectorUnit.KEYWORD:const expressionResLength=expressionRes.length;let keywordIndex=0;for(let pageIndex=0;pageIndex<this.m_captionInfo.pages.length;pageIndex++){const curPage=this.m_captionInfo.pages[pageIndex];for(let i=0;i<curPage.m_words.length;i++){if(curPage.m_words[i].m_isKey&&![" ","\n"].includes(curPage.m_words[i].m_string)){if(curPage==this.m_currentCaptionPage||this.needMerge){const value=expressionRes[(keywordIndex+this.m_runtimeParams.keyword_start_index)%expressionResLength];this.m_callbackParams.values.push({name:vg.targetAttribute,value:value,start:curPage.m_words[i].startIndex,end:curPage.m_words[i].endIndex,overlayMode:vg.overlayMode,selectorUnit:selectorUnit})}keywordIndex++}}}break;default:console.error(TEMPLATE_TAG,"");continue}}}updatePageAnimations(timeInPage,wordCount,currentPage,startTimeInPage){PAGE_MOTION.update(timeInPage,true,{wordCount:wordCount,targetStart:currentPage.m_startTimeInWidget-startTimeInPage,targetEnd:currentPage.m_endTimeInWidget-startTimeInPage});this.updateExpressions(this.m_pageAnimations,timeInPage)}updateLineAnimations(timeInPage,wordCount,currentLine,startTimeInPage){LINE_MOTION.update(timeInPage,true,{wordCount:wordCount,targetStart:currentLine.m_startTimeInWidget-startTimeInPage,targetEnd:currentLine.m_endTimeInWidget-startTimeInPage});this.updateExpressions(this.m_lineAnimations,timeInPage)}updateWordAnimations(timeInPage,wordCount,currentWord,startTimeInPage){WORD_MOTION.update(timeInPage,true,{wordCount:wordCount,targetStart:currentWord.m_startTimeInWidget-startTimeInPage,targetEnd:currentWord.m_endTimeInWidget-startTimeInPage});this.updateExpressions(this.m_wordAnimations,timeInPage)}updatePagePostAnimations(timeInPage,wordCount,currentPage,startTimeInPage){PAGE_POST_MOTION.update(timeInPage,true,{wordCount:wordCount,targetStart:currentPage.m_startTimeInWidget-startTimeInPage,targetEnd:currentPage.m_endTimeInWidget-startTimeInPage})}updateLinePostAnimations(timeInPage,wordCount,currentLine,startTimeInPage){LINE_POST_MOTION.update(timeInPage,true,{wordCount:wordCount,targetStart:currentLine.m_startTimeInWidget-startTimeInPage,targetEnd:currentLine.m_endTimeInWidget-startTimeInPage})}updateWordPostAnimations(timeInPage,wordCount,currentWord,startTimeInPage){WORD_POST_MOTION.update(timeInPage,true,{wordCount:wordCount,targetStart:currentWord.m_startTimeInWidget-startTimeInPage,targetEnd:currentWord.m_endTimeInWidget-startTimeInPage})}flushCallbackQueue(){if(this.m_callbackParams.values.length>0){const jsonStr=JSON.stringify(this.m_callbackParams);AmazUtils$1.swingTemplateUtils.captionSetParamsBatch(this.m_textComp,jsonStr);this.m_callbackParams.values=[]}}setSelectRange(start,end){if(start>=0&&end>=0){const textCmd=new Amaz$d.TextCommand;textCmd.type=TextCommandType.CT_SELECT_CONTENT;textCmd.iParam1=start;textCmd.iParam2=end-start;this.m_textComp.pushCommand(textCmd);this.m_textComp.forceFlushCommandQueue()}}postUpdate(timeInWidget){var _a;if((_a=this.m_richText)===null||_a===void 0?void 0:_a.hasAnimation()){return timeInWidget}if(!this.m_enabled)return timeInWidget;if(this.m_keyframeParams!=null||this.needGenerateOnePage){this.initFrame()}this.setSelectRange(this.m_initValues.selectStart,this.m_initValues.selectEnd);return timeInWidget}onTextChangeForScript(){this.resetAnimation();this.resetCurrentCaptionPage();this.refreshInitValues()}}var Amaz$c=effect.Amaz;class RootAttributeSetter{static setRootScaleX(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="sx"){widget.scale=new Amaz$c.Vector3f(value,widget.scale.y,widget.scale.z)}}static setRootScaleY(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="sy"){widget.scale=new Amaz$c.Vector3f(widget.scale.x,value,widget.scale.z)}}static setRootRotationZ(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="rz"){widget.rotation=new Amaz$c.Vector3f(widget.rotation.x,widget.rotation.y,value)}}static setRootPosX(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="px"){widget.position=new Amaz$c.Vector3f(value,widget.position.y,widget.position.z)}}static setRootPosY(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="py"){widget.position=new Amaz$c.Vector3f(widget.position.x,value,widget.position.z)}}}RootAttributeSetter.setterMap={px:RootAttributeSetter.setRootPosX,py:RootAttributeSetter.setRootPosY,sx:RootAttributeSetter.setRootScaleX,sy:RootAttributeSetter.setRootScaleY,rz:RootAttributeSetter.setRootRotationZ};class RootValueGeneratorContext{constructor(widget,overlayMode,propertyName){this.widget=null;this.widget=widget;this.overlayMode=overlayMode;this.propertyName=propertyName}}class RootAnimationController extends BaseAnimationController{constructor(widget){super();this.m_host=null;this.m_root_motion=new Group;this.m_host=widget}checkIsDirty(){if(this.m_keyFramesDirty==true){return true}if(this.m_keyFramesDirty==false&&this.m_endTimeInWidget<TIME_EPS){return true}return false}updateKeyFrames(){if(!this.checkIsDirty()){return}this.m_keyFramesDirty=false;this.m_root_motion.removeAll();if(this.m_keyframeParams==null){return}const keyframes=this.m_keyframeParams.keyframes;if(keyframes==null){return}for(let i=0;i<keyframes.length;i++){const keyframe=keyframes[i];let overlayMode="over";if(KeyframeAttrUtils.isValidOverlayMode(keyframe.overlay_mode))overlayMode=keyframe.overlay_mode;if(keyframe.k==null){console.error(TEMPLATE_TAG,"keyframe.k is undefined");return}const startTimeInWidget=keyframe.s*KEYFRAME_TIME_FACTOR;const endTimeInWidget=keyframe.e*KEYFRAME_TIME_FACTOR;if(endTimeInWidget-startTimeInWidget<TIME_EPS){console.error(TEMPLATE_TAG,"keyframe time range too short");return}this.setTimeRange(startTimeInWidget,endTimeInWidget);Object.keys(RootAttributeSetter.setterMap).forEach((key=>{if(keyframe.k[key]!=null){const rootMotionContext=new RootValueGeneratorContext(this.m_host,overlayMode,key);this.createMotionsRootProperty(keyframe,rootMotionContext,key)}}))}}createMotionsRootProperty(keyframeJson,motionContext,propertyName){const prop=keyframeJson.k[propertyName];const callback=RootAttributeSetter.setterMap[propertyName];if(prop==null){console.error(TEMPLATE_TAG,"prop is null");return}if(prop.length==0&&motionContext){console.error(TEMPLATE_TAG,"prop is empty");return}const interp=Interpolation.Linear;const keyframes=KeyframeAttrUtils.createKeyframes(keyframeJson,propertyName,[]);KeyframeAttrUtils.createOneMotionByRelativeTime(keyframes,callback,interp,motionContext,this.m_root_motion,propertyName,this.m_startTimeInWidget,this.m_endTimeInWidget,keyframeJson.s,keyframeJson.e).start()}update(timeInWidget){if(this.m_keyframeParams!=null){this.m_root_motion.update(timeInWidget,true)}return 0}}var Amaz$b=effect.Amaz;var Vec3$4=Amaz$b.Vector3f;const TrackingMode={TRACKING_FOLLOW_POSITION:1<<0,TRACKING_FOLLOW_SCALE:1<<1,TRACKING_FOLLOW_ROTATION:1<<2,TRACKING_FOLLOW_POSITION_ABS:1<<3};class ClassicTRS{constructor(){this.position=new Vec3$4(0,0,0);this.rotation=new Vec3$4(0,0,0);this.scale=new Vec3$4(1,1,1)}setPosition(pos){this.position=pos}setRotation(rot){this.rotation=rot}setScale(s){this.scale=s}setTRS(pos,rot,s){this.position=pos;this.rotation=rot;this.scale=s}getPosition(){return this.position}getRotation(){return this.rotation}getScale(){return this.scale}getTRS(){return[this.position,this.rotation,this.scale]}}class TrackingTRSParams{constructor(){this.centerX=0;this.centerY=0;this.width=0;this.height=0;this.rotationZ=0}setParams(centerX,centerY,width,height,rotation){this.centerX=centerX;this.centerY=centerY;this.width=width;this.height=height;this.rotationZ=rotation}reset(){this.centerX=0;this.centerY=0;this.width=0;this.height=0;this.rotationZ=0}}class TrackingProcessor{constructor(){this.m_baseParams=null;this.m_trackingParams=null;this.m_trackingMode=0;this.m_needAdaptTrackingTRS=false;this.m_finalTRS=null;this.m_baseParams=new TrackingTRSParams;this.m_trackingParams=new TrackingTRSParams;this.m_trackingMode=0;this.m_needAdaptTrackingTRS=false}reset(){if(this.m_baseParams==null){this.m_baseParams=new TrackingTRSParams}else{this.m_baseParams.reset()}if(this.m_trackingParams==null){this.m_trackingParams=new TrackingTRSParams}else{this.m_trackingParams.reset()}this.m_trackingMode=0;this.m_needAdaptTrackingTRS=false}saveFinalTRS(position,rotation,scale){if(this.m_finalTRS==null){this.m_finalTRS=new ClassicTRS}this.m_finalTRS.setTRS(position,rotation,scale)}finalTRSIsAvaileble(){return this.m_finalTRS!=null}resetFinalTRS(){this.m_finalTRS=null}getFinalTRS(){var _a,_b,_c;const pos=(_a=this.m_finalTRS)===null||_a===void 0?void 0:_a.getPosition();const rot=(_b=this.m_finalTRS)===null||_b===void 0?void 0:_b.getRotation();const s=(_c=this.m_finalTRS)===null||_c===void 0?void 0:_c.getScale();this.m_finalTRS=null;return[pos,rot,s]}getFinalPosition(){var _a;return(_a=this.m_finalTRS)===null||_a===void 0?void 0:_a.getPosition()}getFinalRotation(){var _a;return(_a=this.m_finalTRS)===null||_a===void 0?void 0:_a.getRotation()}getFinalScale(){var _a;return(_a=this.m_finalTRS)===null||_a===void 0?void 0:_a.getScale()}updateTrackingData(seekParams){var _a,_b;if(seekParams.has("trackingParams")){const trackingParams=seekParams.get("trackingParams");if(trackingParams.has("baseTRS")){const baseTRS=trackingParams.get("baseTRS");(_a=this.m_baseParams)===null||_a===void 0?void 0:_a.setParams(baseTRS.get("centerX"),baseTRS.get("centerY"),baseTRS.get("width"),baseTRS.get("height"),baseTRS.get("rotation"))}if(trackingParams.has("trackingTRS")){const trackingTRS=trackingParams.get("trackingTRS");(_b=this.m_trackingParams)===null||_b===void 0?void 0:_b.setParams(trackingTRS.get("centerX"),trackingTRS.get("centerY"),trackingTRS.get("width"),trackingTRS.get("height"),trackingTRS.get("rotation"))}if(trackingParams.has("trackingMode")){this.m_trackingMode=trackingParams.get("trackingMode")}this.m_needAdaptTrackingTRS=true}else{this.m_needAdaptTrackingTRS=false;console.error("TrackingProcessor seekParams do not have trackingParams!")}}adaptTRS(position,rotation,scale){if(!this.m_needAdaptTrackingTRS||this.m_baseParams==null||this.m_trackingParams==null){return}const outPos=position.copy();const outScale=scale.copy();const outRot=rotation.copy();const scaleX=this.m_trackingParams.width/this.m_baseParams.width;const scaleY=this.m_trackingParams.height/this.m_baseParams.height;if(this.m_trackingMode&TrackingMode.TRACKING_FOLLOW_POSITION){outPos.x=this.m_trackingParams.centerX+scaleX*(outPos.x-this.m_baseParams.centerX);outPos.y=this.m_trackingParams.centerY+scaleY*(outPos.y-this.m_baseParams.centerY)}else if(this.m_trackingMode&TrackingMode.TRACKING_FOLLOW_POSITION_ABS){outPos.x=this.m_trackingParams.centerX+(outPos.x-this.m_baseParams.centerX);outPos.y=this.m_trackingParams.centerY+(outPos.y-this.m_baseParams.centerY)}if(this.m_trackingMode&TrackingMode.TRACKING_FOLLOW_SCALE){const uniformScale=(scaleX+scaleY)/2;outScale.x*=uniformScale;outScale.y*=uniformScale}if(this.m_trackingMode&TrackingMode.TRACKING_FOLLOW_ROTATION){outRot.z+=this.m_trackingParams.rotationZ-this.m_baseParams.rotationZ}this.m_needAdaptTrackingTRS=false;this.reset();this.saveFinalTRS(outPos,outRot,outScale)}}class BaseAssociationController{constructor(Widget){this.m_widget=null;this.m_associationParams=null;this.m_associationParamsDirty=true;this.m_widget=Widget}get widget(){return this.m_widget}set associationParams(params){if(this.m_associationParams==null||this.m_associationParams!=params){this.m_associationParams=params;this.m_associationParamsDirty=true}}get associationParams(){return this.m_associationParams}set paramsDirty(paramsDirty){this.m_associationParamsDirty=paramsDirty}get paramsDirty(){return this.m_associationParamsDirty}}var Amaz$a=effect.Amaz;class RootAssociationController extends BaseAssociationController{constructor(){super(...arguments);this.m_oriWordInfo=null;this.m_curWordInfo=null;this.m_oriWordInfoStr="";this.m_curWordInfoStr="";this.m_dirty=false}set oriWordInfo(wordInfo){this.m_oriWordInfo=wordInfo}get oriWordInfo(){return this.m_oriWordInfo}set curWordInfo(wordInfo){this.m_curWordInfo=wordInfo}get curWordInfo(){return this.m_curWordInfo}set oriWordInfoStr(wordInfoStr){this.m_oriWordInfoStr=wordInfoStr}get oriWordInfoStr(){return this.m_oriWordInfoStr}set curWordInfoStr(wordInfoStr){this.m_curWordInfoStr=wordInfoStr}get curWordInfoStr(){return this.m_curWordInfoStr}set dirty(dirty){this.m_dirty=dirty}get dirty(){return this.m_dirty}setAssociationParams(params){super.associationParams=params;this.updateAssociationParams()}updateAssociationParams(){if(!this.m_associationParamsDirty)return;if("word_info"in super.associationParams){const oriWordInfoParam=super.associationParams.word_info;if(oriWordInfoParam==null){return}const oriWordInfoStr=JSON.stringify(oriWordInfoParam);if(oriWordInfoStr!=this.oriWordInfoStr){this.oriWordInfoStr=oriWordInfoStr;this.curWordInfoStr=this.oriWordInfoStr;if(!this.oriWordInfo){this.oriWordInfo=new Amaz$a.TextWordInfo}if(!this.curWordInfo){this.curWordInfo=new Amaz$a.TextWordInfo}AmazUtils$1.TextWordMatchUtils.parseWordInfoByJsonStr(this.oriWordInfo,oriWordInfoStr);this.curWordInfo.initWith(this.oriWordInfo);this.m_dirty=true}}if("current_word_info"in super.associationParams){const curWordInfoParam=super.associationParams.current_word_info;if(curWordInfoParam==null){return}if(!this.curWordInfo){this.curWordInfo=new Amaz$a.TextWordInfo}const curWordInfoStr=JSON.stringify(curWordInfoParam);if(curWordInfoStr!=this.curWordInfoStr){this.curWordInfoStr=curWordInfoStr;AmazUtils$1.TextWordMatchUtils.parseWordInfoByJsonStr(this.curWordInfo,curWordInfoStr);this.m_dirty=true}}this.m_associationParamsDirty=false}updateByAssociation(_widgets){for(let wId=0;wId<_widgets.length;++wId){const widget=_widgets[wId];if(widget==null){continue}if(this.widget&&widget.widgetType==WidgetType.TEXT){const textWidget=widget;if(textWidget&&textWidget.textAssociationController&&textWidget.textAssociationController.wordInfoDirty==true){const textControl=textWidget.textAssociationController;if(textControl.wordIdRange.length>=2){if(!this.curWordInfo||!textControl.curWordInfo){continue}const wordIdOffset=textControl.wordIdRange[0];let msTimeOffset=(textControl.startTimeInTrack-this.widget.startTimeInTrack)*1e3;msTimeOffset=msTimeOffset-.5|0;this.curWordInfo.merge(textControl.curWordInfo,wordIdOffset,msTimeOffset);this.dirty=true;break}}}}if(this.m_dirty==true){if(this.curWordInfo==null){return}for(let wId=0;wId<_widgets.length;++wId){const widget=_widgets[wId];if(widget==null){continue}if(widget.widgetType==WidgetType.TEXT){const textWidget=widget;if(textWidget&&textWidget.textAssociationController){const textControl=textWidget.textAssociationController;let canUpdate=true;if(textControl.wordIdRangeDirty==true||textControl.wordInfoDirty==true){textControl.wordIdRangeDirty=false;textControl.wordInfoDirty=false;canUpdate=false}if(canUpdate&&textControl.wordIdRange.length>=2){const startId=textControl.wordIdRange[0];const endId=textControl.wordIdRange[1];const dirtyVec=this.curWordInfo.dirtyIndex;for(let i=0;i<dirtyVec.size();++i){const dirtyId=dirtyVec.get(i);if(startId<=dirtyId&&dirtyId<=endId){textControl.dirty=true;textControl.updateAssociationParams();textControl.updateByAssociation();if(textControl.needUpdateCaption){textWidget.setParamsToAnims()}break}}}}}}this.curWordInfo.dirtyIndex.clear();this.m_dirty=false}}}var Amaz$9=effect.Amaz;var Vec2$3=Amaz$9.Vector2f;var Vec3$3=Amaz$9.Vector3f;var Vec4=Amaz$9.Vector4f;class LayoutInfo{constructor(){this.m_position=new Vec3$3(0,0,0);this.m_scale=new Vec3$3(1,1,1);this.m_rotation=new Vec3$3(0,0,0);this.m_widgetResolutionType=0;this.m_screenSize=new Vec2$3(720,1280);this.m_widgetOriginalPixelSize=new Vec2$3(0,0);this.m_layoutDirty=true;this.m_boundingBox=new Vec4(0,0,0,0);this.m_localBoundingBox=new Vec4(0,0,0,0)}copy(){const newInfo=new LayoutInfo;newInfo.m_position=this.m_position.copy();newInfo.m_scale=this.m_scale.copy();newInfo.m_rotation=this.m_rotation.copy();newInfo.m_widgetResolutionType=this.m_widgetResolutionType;newInfo.m_screenSize=this.m_screenSize.copy();newInfo.m_widgetOriginalPixelSize=this.m_widgetOriginalPixelSize.copy();return newInfo}isEqualTo(layoutInfo){return this.m_position.eq(layoutInfo.m_position)&&this.m_scale.eq(layoutInfo.m_scale)&&this.m_rotation.eq(layoutInfo.m_rotation)&&this.m_widgetResolutionType===layoutInfo.m_widgetResolutionType&&this.m_screenSize.eq(layoutInfo.m_screenSize)&&this.m_widgetOriginalPixelSize.eq(layoutInfo.m_widgetOriginalPixelSize)}}var Amaz$8=effect.Amaz;var Quat=Amaz$8.Quaternionf;var Vec2$2=Amaz$8.Vector2f;var Vec3$2=Amaz$8.Vector3f;const LAYER_SIZE=12e7;const ORDER_SIZE=4e4;const VEC3_UNIT_Z=new Vec3$2(0,0,1);var WidgetType;(function(WidgetType){WidgetType[WidgetType["ROOT"]=0]="ROOT";WidgetType[WidgetType["SPRITE"]=1]="SPRITE";WidgetType[WidgetType["SHAPE"]=2]="SHAPE";WidgetType[WidgetType["TEXT"]=3]="TEXT"})(WidgetType||(WidgetType={}));var WidgetResolutionType;(function(WidgetResolutionType){WidgetResolutionType[WidgetResolutionType["DESIGN"]=0]="DESIGN";WidgetResolutionType[WidgetResolutionType["DESIGN_HEIGHT"]=1]="DESIGN_HEIGHT";WidgetResolutionType[WidgetResolutionType["NORMALIZED"]=2]="NORMALIZED";WidgetResolutionType[WidgetResolutionType["ORIGINAL"]=3]="ORIGINAL"})(WidgetResolutionType||(WidgetResolutionType={}));const PI=3.141592653589793;class Widget{constructor(name,widgetType,scene){this.m_timeRangeInTrack=new TimeRange(0,0);this.m_enable=true;this.m_cameraLayer=0;this.m_layer=0;this.m_localOrder=0;this.m_position=new Vec3$2(0,0,0);this.m_scale=new Vec3$2(1,1,1);this.m_rotation=new Vec3$2(0,0,0);this.m_rootEntity=null;this.m_constrainedWidgets=null;this.m_needUpdateConstrainedWidgets=false;this.m_widgetParamUpdated=true;this.m_updateOrder=true;this.m_needUpdateShapeBlendOrder=true;this.m_timeRangeUpdated=true;this.m_screenSizeChanged=true;this.m_widgetResolutionType=WidgetResolutionType.DESIGN;this.m_screenSize=new Vec2$2(720,1280);this.m_pixelRatio=640;this.m_extraScale=new Vec3$2(1,1,1);this.m_orthoScale=1;this.m_previewTime=1.5;this.m_rootAnimationController=null;this.m_rootKeyframeParams=null;this.m_rootAssociationController=null;this.m_rootWidget=null;this.m_actionType="";this.m_layoutInfo=new LayoutInfo;this.m_updateTrackingData=false;this.m_trackerHoldFinalTRS=false;this.m_trackingProcessor=new TrackingProcessor;this.m_name=name;this.m_widgetType=widgetType;this.m_scene=scene}get widgetName(){return this.m_name}set position(pos){if(!this.m_position.eq(pos)){this.m_position=pos;this.m_widgetParamUpdated=true}}get position(){return this.m_position}set scale(scale){if(!this.m_scale.eq(scale)){this.m_scale=scale;this.m_widgetParamUpdated=true}}get scale(){return this.m_scale}set rotation(rotate){if(!this.m_rotation.eq(rotate)){this.m_rotation=rotate;this.m_widgetParamUpdated=true}}get rotation(){return this.m_rotation}set duration(duration){if(this.duration!==duration&&duration>=0){this.endTimeInTrack=this.startTimeInTrack+duration;this.m_widgetParamUpdated=true;this.m_timeRangeUpdated=true}}get duration(){return this.m_timeRangeInTrack.duration}set layer(layer){if(this.m_layer!==layer){this.m_layer=layer;this.m_widgetParamUpdated=true;this.m_updateOrder=true;this.m_needUpdateShapeBlendOrder=true}}get layer(){return this.m_layer}get cameraLayer(){return this.m_cameraLayer}set previewTime(timeStamp){if(this.m_previewTime!==timeStamp){this.m_previewTime=timeStamp}}get previewTime(){return this.m_previewTime}set localOrder(localOrder){if(this.m_localOrder!==localOrder){this.m_localOrder=localOrder;this.m_widgetParamUpdated=true;this.m_updateOrder=true;this.m_needUpdateShapeBlendOrder=true}}get localOrder(){return this.m_localOrder}set enable(enable){if(this.m_enable!==enable){this.m_enable=enable;this.m_widgetParamUpdated=true}}get enable(){return this.m_enable}get widgetType(){return this.m_widgetType}set widgetResolutionType(resolutionType){this.m_widgetResolutionType=resolutionType}get widgetResolutionType(){return this.m_widgetResolutionType}get scene(){return this.m_scene}set rootEntity(root){this.m_rootEntity=root}get rootEntity(){if(!this.m_rootEntity){this.createWidgetRootEntity(this.m_scene)}return this.m_rootEntity}set startTimeInTrack(startTimeInTrack){if(this.m_timeRangeInTrack.startTime!==startTimeInTrack){this.m_timeRangeInTrack.startTime=startTimeInTrack;this.m_timeRangeUpdated=true}}get startTimeInTrack(){return this.m_timeRangeInTrack.startTime}set endTimeInTrack(endTimeInTrack){if(this.m_timeRangeInTrack.endTime!==endTimeInTrack){this.m_timeRangeInTrack.endTime=endTimeInTrack;this.m_timeRangeUpdated=true}}get endTimeInTrack(){return this.m_timeRangeInTrack.endTime}get screenSize(){return this.m_screenSize}set extraScale(scale){if(!this.m_extraScale.eq(scale)){this.m_extraScale=scale;this.m_widgetParamUpdated=true}}get extraScale(){return this.m_extraScale}get trackFinalPosition(){return this.m_trackingProcessor.getFinalPosition()}get trackFinalRotation(){return this.m_trackingProcessor.getFinalRotation()}get trackFinalScale(){return this.m_trackingProcessor.getFinalScale()}set trackerHoldFinalTRS(hold){this.m_trackerHoldFinalTRS=hold}get trackerHoldFinalTRS(){return this.m_trackerHoldFinalTRS}setTimeRange(startTimeInTrack,endTimeInTrack){this.startTimeInTrack=startTimeInTrack;this.endTimeInTrack=endTimeInTrack}set actionType(type){this.m_actionType=type}get actionType(){return this.m_actionType}set layoutInfo(layoutInfo){this.layoutInfo=layoutInfo}get layoutInfo(){return this.m_layoutInfo}set layoutDirty(dirty){if(this.layoutInfo.m_layoutDirty!=dirty){this.layoutInfo.m_layoutDirty=dirty}}get layoutDirty(){return this.layoutInfo.m_layoutDirty}set boundingBox(bbox){if(!this.layoutInfo.m_boundingBox.eq(bbox)){this.layoutInfo.m_boundingBox=bbox}}get boundingBox(){return this.layoutInfo.m_boundingBox}set localBoundingBox(bbox){if(!this.layoutInfo.m_localBoundingBox.eq(bbox)){this.layoutInfo.m_localBoundingBox=bbox}}get localBoundingBox(){return this.layoutInfo.m_localBoundingBox}get rootWidget(){return this.m_rootWidget}set rootWidget(root){this.m_rootWidget=root}get rootAssociationController(){return this.m_rootAssociationController}set rootAnimationController(controller){this.m_rootAnimationController=controller}getTimeInWidget(timeInTrack){return Math.max(timeInTrack-this.startTimeInTrack,0)}getTimeInSegment(timeInTrack){if(this.rootWidget){return Math.max(timeInTrack-this.rootWidget.startTimeInTrack,0)}return this.getTimeInWidget(timeInTrack)}set parameters(jsonParam){if(jsonParam){if("position"in jsonParam){const configPosition=AmazUtils$1.CastJsonArray3fToAmazVector3f(jsonParam.position);if(null!==configPosition){this.position=configPosition}else{console.error("widget set parameters json config position is not vector3f!")}}if("scale"in jsonParam){const configScale=AmazUtils$1.CastJsonArray3fToAmazVector3f(jsonParam.scale);if(null!==configScale){this.scale=configScale}else{console.error("widget set parameters json config scale is not vector3f!")}}if("rotation"in jsonParam){const configRotation=AmazUtils$1.CastJsonArray3fToAmazVector3f(jsonParam.rotation);if(null!=configRotation){this.rotation=configRotation}else{console.error("widget set parameters json config rotation is not vector3f!")}}if("layer"in jsonParam){const configLayer=jsonParam.layer;this.layer=configLayer}if("preview_time"in jsonParam){const configPreTime=jsonParam.preview_time;this.previewTime=configPreTime}if("visible"in jsonParam){const configEnable=jsonParam.visible;this.enable=configEnable}if("order_in_layer"in jsonParam){const configLocalOrder=jsonParam.order_in_layer;this.localOrder=configLocalOrder}let needUpdateEndTime=false;if("start_time"in jsonParam){const configStart_time=jsonParam.start_time;this.startTimeInTrack=configStart_time;needUpdateEndTime=true}if("duration"in jsonParam){const configDuration=jsonParam.duration;this.duration=configDuration;needUpdateEndTime=true}if(needUpdateEndTime){this.endTimeInTrack=this.startTimeInTrack+this.duration}if("keyframe_params"in jsonParam&&this.widgetType===WidgetType.ROOT){if(this.m_rootAnimationController==null){this.m_rootAnimationController=new RootAnimationController(this)}if(this.m_rootKeyframeParams!=jsonParam.keyframe_params){this.m_rootKeyframeParams=jsonParam.keyframe_params;this.m_rootAnimationController.setKeyFrameParams(this.m_rootKeyframeParams)}}if("bounding_box"in jsonParam){const configBbox=AmazUtils$1.CastJsonArray4fToAmazVector4f(jsonParam.bounding_box);if(null!==configBbox){this.boundingBox=configBbox}else{console.error("widget set parameters json config bounding_box is not vector4f!")}}if("local_boundingBox"in jsonParam){const configLocalBbox=AmazUtils$1.CastJsonArray4fToAmazVector4f(jsonParam.local_boundingBox);if(null!==configLocalBbox){this.localBoundingBox=configLocalBbox}else{console.error("widget set parameters json config local_boundingBox is not vector4f!")}}if("association_params"in jsonParam&&this.widgetType===WidgetType.ROOT){const configAssociationParams=jsonParam.association_params;if(!this._checkRootAssoParams(configAssociationParams)){return}if(configAssociationParams!=null){if(this.m_rootAssociationController==null){this.m_rootAssociationController=new RootAssociationController(this)}this.m_rootAssociationController.setAssociationParams(configAssociationParams)}else{console.error("root widget set parameters json config association_params is not json object!")}}if("action_type"in jsonParam){const action_type=jsonParam.action_type;this.m_actionType=action_type}}}get parameters(){const typeStr=this.getWidgetTypeStr();const widgetParam={name:this.m_name,type:typeStr,layer:this.layer,order_in_layer:this.localOrder,preview_time:this.previewTime,start_time:this.startTimeInTrack,duration:this.duration,position:[this.position.x,this.position.y,this.position.z],scale:[this.scale.x,this.scale.y,this.scale.z],rotation:[this.rotation.x,this.rotation.y,this.rotation.z],action_type:this.m_actionType,visible:this.enable};if(this.rootAssociationController&&this.rootAssociationController.associationParams){widgetParam.association_params=this.rootAssociationController.associationParams}return widgetParam}getWidgetTypeStr(){let typeStr="";if(this.widgetType===WidgetType.ROOT){typeStr="root"}else if(this.widgetType===WidgetType.SPRITE){typeStr="sticker"}else if(this.widgetType===WidgetType.SHAPE){typeStr="shape"}else if(this.widgetType===WidgetType.TEXT){typeStr="text"}return typeStr}onResize(screenSize,pixelRatio,extraScale){this.m_screenSize=screenSize;this.m_pixelRatio=pixelRatio/this.m_orthoScale;this.m_extraScale=extraScale;this.m_screenSizeChanged=true;this.m_widgetParamUpdated=true}onUpdate(timeInTrack){if(this.m_rootAnimationController!=null&&this.widgetType===WidgetType.ROOT){this.m_rootAnimationController.updateKeyFrames();const timeInWidget=this.getTimeInWidget(timeInTrack);if(this.m_rootAnimationController.isEnable(timeInWidget)){this.m_rootAnimationController.update(timeInWidget)}}if(this.m_trackerHoldFinalTRS&&this.widgetType===WidgetType.ROOT){if(this.m_updateTrackingData){if(this.m_rootEntity){this.m_trackingProcessor.adaptTRS(this.m_position,this.m_rotation,this.m_scale)}this.m_updateTrackingData=false;this.m_widgetParamUpdated=true}else{this.m_trackerHoldFinalTRS=false;this.m_widgetParamUpdated=true;this.m_trackingProcessor.resetFinalTRS()}}this.updateRootEntityParam();if(this.widgetType===WidgetType.ROOT){this.checkLayoutDirty()}}onLateUpdate(_timeInTrack){}getConstrainedWidgets(){return this.m_constrainedWidgets}createWidgetRootEntity(scene){this.m_rootEntity=AmazUtils$1.createEntity(this.m_name,scene);this.m_rootEntity.layer=this.m_cameraLayer;this.m_rootEntity.transform={position:new Vec3$2(0,0,0),scale:new Vec3$2(1,1,1),rotation:new Vec3$2(0,0,0)}}setWidgetRootEntity(root){this.m_rootEntity=root}createRoot(jsonParam,scene){this.parameters=jsonParam;this.createWidgetRootEntity(scene)}getWidgetRootEntity(){return this.m_rootEntity}removeWidgetRootEntity(scene){if(null!=this.m_rootEntity){scene.removeEntity(this.m_rootEntity);this.m_rootEntity=null}}updateConstrainedWidgets(name,widget){if(null===this.m_constrainedWidgets){this.m_constrainedWidgets=new Map;this.m_constrainedWidgets.set(name,widget)}else{if(!this.m_constrainedWidgets.has(name)){this.m_constrainedWidgets.set(name,widget)}}}updateOrder(rootSortingOrder){const renderers=AmazUtils$1.getRenderers(this.m_rootEntity);const size=renderers.size();for(let i=0;i<size;i++){const localOrder=0;const renderer=renderers.get(i);const sortingOrder=rootSortingOrder+localOrder;renderer.sortingOrder=sortingOrder}}updateTrackingProcessData(seekParams){var _a;this.m_trackerHoldFinalTRS=true;(_a=this.m_trackingProcessor)===null||_a===void 0?void 0:_a.updateTrackingData(seekParams);this.m_updateTrackingData=true}updateRootEntityParam(){if(this.m_widgetParamUpdated){if(null!=this.m_rootEntity){if(this.canNotRender()){this.m_enable=false}this.m_rootEntity.visible=this.m_enable;let finalPosition=this.position.copy();let finalScale=this.scale.copy();let finalRotation=this.rotation.copy();if(this.m_trackerHoldFinalTRS&&this.m_name==="rootWidget"){finalPosition=this.trackFinalPosition.copy();finalScale=this.trackFinalScale.copy();finalRotation=this.trackFinalRotation.copy()}if(this.m_name==="rootWidget"){finalPosition.x=.5*finalPosition.x*this.m_screenSize.x/this.m_pixelRatio;finalPosition.y=.5*finalPosition.y*this.m_screenSize.y/this.m_pixelRatio}else{finalPosition.x=finalPosition.x/this.m_pixelRatio;finalPosition.y=finalPosition.y/this.m_pixelRatio}const trans=this.m_rootEntity.getComponent("Transform");trans.localPosition=finalPosition;trans.localScale=finalScale.scale(this.m_extraScale);let rotatationQ=new Quat;rotatationQ=rotatationQ.axisAngleToQuaternion(VEC3_UNIT_Z,finalRotation.z/180*PI);trans.localOrientation=rotatationQ;const sortlayer=this.m_layer*LAYER_SIZE+this.m_localOrder*ORDER_SIZE;if(this.m_name!=="rootWidget"&&this.m_updateOrder){this.updateOrder(sortlayer);this.m_updateOrder=false}}this.m_widgetParamUpdated=false}}checkIsInRange(timeInTrack){return this.compareFloatRange(this.m_timeRangeInTrack.startTime,this.m_timeRangeInTrack.endTime,timeInTrack)}compareFloatRange(x,y,t,closedInterval=true){const precision=1e-6;const equal=(x,y)=>Math.abs(x-y)<precision;return closedInterval?t>=x&&t<=y||equal(t,x)||equal(t,y):t>x&&t<y&&!equal(t,x)&&!equal(t,y)}onUpdateAnimationDuration(_originTime,_newTime){}canNotRender(){return this.actionType==="can_not_render"}updateLayoutInfo(){const layoutInfo=this.layoutInfo;layoutInfo.m_position=this.position.copy();layoutInfo.m_scale=this.scale.copy();layoutInfo.m_rotation=this.rotation.copy();layoutInfo.m_widgetResolutionType=this.widgetResolutionType;layoutInfo.m_screenSize=this.screenSize.copy()}checkLayoutDirty(){if(Widget.m_isPostLayoutEnable==false){return}const oriLayoutInfo=this.layoutInfo.copy();this.updateLayoutInfo();if(this.widgetType==WidgetType.ROOT){if(!oriLayoutInfo.isEqualTo(this.layoutInfo)){this.layoutDirty=true}}}_checkRootAssoParams(assoParam){if(assoParam==null){return false}if(!("word_info"in assoParam)){return false}const wordInfo=assoParam.word_info;if(wordInfo==null){return false}if(!("words"in wordInfo)||!Array.isArray(wordInfo.words)||wordInfo.words.length===0){return false}if(!("text"in wordInfo)||typeof wordInfo.text!=="string"||wordInfo.text.trim().length===0){return false}return true}}Widget.m_isPostLayoutEnable=false;var Amaz$7=effect.Amaz;var AnimationType;(function(AnimationType){AnimationType[AnimationType["none"]=0]="none";AnimationType[AnimationType["in"]=1]="in";AnimationType[AnimationType["out"]=2]="out";AnimationType[AnimationType["loop"]=3]="loop"})(AnimationType||(AnimationType={}));var ScriptType;(function(ScriptType){ScriptType[ScriptType["LUA"]=0]="LUA";ScriptType[ScriptType["JAVASCRIPT"]=1]="JAVASCRIPT"})(ScriptType||(ScriptType={}));const prefabName="anim.prefab";const contentFileName="/content.json";class Animation2D{constructor(path,resourceID,scriptType){this.m_animationType=AnimationType.none;this.m_animationTimeRangeInWidget=new TimeRange(0,0);this.m_animationLoopDuration=0;this.m_scriptComponent=null;this.m_state={started:false,entered:false};this.m_prefabAnimComponent=undefined;this.m_animationPropertyKey=[];this.m_animationPropertyValue=[];this.m_animationPropertys=new Map;this.m_needMerge=false;this.m_animationPath=path;this.m_animationResourceID=resourceID;if(scriptType==="js"){this.m_scriptType=ScriptType.JAVASCRIPT}else{this.m_scriptType=ScriptType.LUA}}set animationPath(path){this.m_animationPath=path}get animationPath(){return this.m_animationPath}set animationType(ani_type){this.m_animationType=ani_type}get animationType(){return this.m_animationType}setAnimationProperty(animationPropertys){var _a;if(animationPropertys){for(const[key,value]of animationPropertys){this.m_animationPropertys.set(key,value)}}for(const[key,value]of this.m_animationPropertys){(_a=this.script)===null||_a===void 0?void 0:_a.call("onSetProperty",[key,value])}}get animationScriptType(){return this.m_scriptType}get animationResourceID(){return this.m_animationResourceID}set animationStartTimeInWidget(startTimeInWidget){this.m_animationTimeRangeInWidget.startTime=startTimeInWidget}get animationStartTimeInWidget(){return this.m_animationTimeRangeInWidget.startTime}set animationEndTimeInWidget(endTimeInWidget){this.m_animationTimeRangeInWidget.endTime=endTimeInWidget}get animationEndTimeInWidget(){return this.m_animationTimeRangeInWidget.endTime}set animationLoopDuration(loop_duration){var _a;this.m_animationLoopDuration=loop_duration;if(this.state.entered&&this.animationType===AnimationType.loop&&loop_duration>0){(_a=this.script)===null||_a===void 0?void 0:_a.call("setDuration",[loop_duration])}}get animationLoopDuration(){return this.m_animationLoopDuration}set animationDuration(duration){var _a;this.m_animationTimeRangeInWidget.duration=duration;if(this.state.entered&&this.animationType!==AnimationType.loop){(_a=this.script)===null||_a===void 0?void 0:_a.call("setDuration",[duration])}}get animationDuration(){return this.m_animationTimeRangeInWidget.duration}get script(){return this.m_scriptComponent}get state(){return this.m_state}onEnter(){var _a,_b,_c;if(!this.m_state.entered){if(this.animationType!==AnimationType.loop){(_a=this.script)===null||_a===void 0?void 0:_a.call("setDuration",[this.animationDuration])}else{if(this.animationLoopDuration>0){(_b=this.script)===null||_b===void 0?void 0:_b.call("setDuration",[this.animationLoopDuration])}}(_c=this.script)===null||_c===void 0?void 0:_c.call("onEnter");this.m_state.entered=true}}onLeave(){var _a,_b,_c;if(this.m_state.entered){if(this.m_animationType==AnimationType.in){(_a=this.script)===null||_a===void 0?void 0:_a.call("seek",[this.animationDuration])}else if(this.m_animationType==AnimationType.out){(_b=this.script)===null||_b===void 0?void 0:_b.call("seek",[0])}(_c=this.script)===null||_c===void 0?void 0:_c.call("onLeave");this.m_state.entered=false}}seek(timeInWidget){var _a;if(this.state.entered){let seekTime=timeInWidget-this.animationStartTimeInWidget;if(seekTime<0)seekTime=0;(_a=this.script)===null||_a===void 0?void 0:_a.call("seek",[seekTime])}}onStart(){if(this.script&&!this.m_state.started){this.script.call("onStart");this.m_state.started=true}}onClear(){var _a;if(this.m_state.started){(_a=this.script)===null||_a===void 0?void 0:_a.call("clear");this.m_state.started=false;this.m_state.entered=false}}get loaded(){return this.m_scriptComponent!=null}loadAnimation(path,entity){const pm=Amaz$7.AmazingManager.getSingleton("PrefabManager");const anim=pm.loadPrefab(path,prefabName);if(anim){this.m_prefabAnimComponent=anim.getRootEntity().getComponent("ScriptComponent");if(this.m_prefabAnimComponent){this.m_scriptComponent=this.m_prefabAnimComponent.instantiate();const vec=new Amaz$7.Vector;vec.pushBack(this.m_scriptComponent);entity.components=vec}}else{const contentPath=path+contentFileName;const contentJsonStr=AmazFileUtils.readFileContent(contentPath);if(contentJsonStr!==undefined){const contentJson=JSON.parse(contentJsonStr);if(contentJson&&"filemap"in contentJson){const fileMapJson=contentJson.filemap;if(fileMapJson&&"prefab"in fileMapJson){const prefabFileName=fileMapJson.prefab;const anim=pm.loadPrefab(path,prefabFileName);if(anim){this.m_prefabAnimComponent=anim.getRootEntity().getComponent("ScriptComponent");if(this.m_prefabAnimComponent){this.m_scriptComponent=this.m_prefabAnimComponent.instantiate();const vec=new Amaz$7.Vector;vec.pushBack(this.m_scriptComponent);entity.components=vec}else{console.error(TEMPLATE_TAG,"loadAnimation failed!")}}else{console.error(TEMPLATE_TAG,"Animation loadPrefab failed!")}}else{console.error(TEMPLATE_TAG,"prefab key is not in filemap!")}}else{console.error(TEMPLATE_TAG,"filemap key is not in contentJson!")}}else{console.error(TEMPLATE_TAG,"contentJsonStr is undefined")}}return true}unloadAnmation(entity){if(null!=this.m_scriptComponent){this.onClear();entity.removeComponentCom(this.m_scriptComponent);this.m_scriptComponent=null}}reloadAnimation(entity){var _a;if(this.m_scriptComponent&&this.m_prefabAnimComponent){this.onClear();entity.removeComponentCom(this.m_scriptComponent);this.m_scriptComponent=this.m_prefabAnimComponent.instantiate();const vec=new Amaz$7.Vector;vec.pushBack(this.m_scriptComponent);entity.components=vec;for(const[key,value]of this.m_animationPropertys){(_a=this.script)===null||_a===void 0?void 0:_a.call("onSetProperty",[key,value])}}}checkStateStarted(seekInAnim,scene){if(this.script&&scene){if(seekInAnim){this.script.enabled=true}else{this.script.enabled=false}if(this.script.enabled&&this.m_state.started==false){if(!this.script.getScript()){const scriptSystem=scene.getSystem("ScriptSystem");scriptSystem.initAndStartScript(this.script)}this.m_state.started=true}}}}var configValidator={exports:{}};configValidator.exports=validate20$5;configValidator.exports.default=validate20$5;function validate20$5(data,{instancePath:instancePath="",parentData:parentData,parentDataProperty:parentDataProperty,rootData:rootData=data}={}){let vErrors=null;let errors=0;{if(data&&typeof data=="object"&&!Array.isArray(data)){let missing0;if(data.type===undefined&&(missing0="type")){validate20$5.errors=[{instancePath:instancePath,schemaPath:"#/required",keyword:"required",params:{missingProperty:missing0},message:"must have required property '"+missing0+"'"}];return false}else{for(const key0 in data){if(!(key0==="type"||key0==="root"||key0==="children"||key0==="childrenOperation"||key0==="globals"||key0==="sub_template_info"||key0==="subType")){validate20$5.errors=[{instancePath:instancePath,schemaPath:"#/additionalProperties",keyword:"additionalProperties",params:{additionalProperty:key0},message:"must NOT have additional properties"}];return false}}{if(data.type!==undefined){const _errs2=errors;if(typeof data.type!=="string"){validate20$5.errors=[{instancePath:instancePath+"/type",schemaPath:"#/properties/type/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid0=_errs2===errors}else{var valid0=true}if(valid0){if(data.root!==undefined){let data1=data.root;const _errs4=errors;if(!(data1&&typeof data1=="object"&&!Array.isArray(data1))&&data1!==null){validate20$5.errors=[{instancePath:instancePath+"/root",schemaPath:"#/properties/root/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}{if(data1&&typeof data1=="object"&&!Array.isArray(data1)){let missing1;if(data1.name===undefined&&(missing1="name")||data1.preview_time===undefined&&(missing1="preview_time")||data1.duration===undefined&&(missing1="duration")){validate20$5.errors=[{instancePath:instancePath+"/root",schemaPath:"#/properties/root/required",keyword:"required",params:{missingProperty:missing1},message:"must have required property '"+missing1+"'"}];return false}else{if(data1.name!==undefined){const _errs7=errors;if(typeof data1.name!=="string"){validate20$5.errors=[{instancePath:instancePath+"/root/name",schemaPath:"#/properties/root/properties/name/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid1=_errs7===errors}else{var valid1=true}if(valid1){if(data1.preview_time!==undefined){let data3=data1.preview_time;const _errs9=errors;if(!(typeof data3=="number"&&isFinite(data3))){validate20$5.errors=[{instancePath:instancePath+"/root/preview_time",schemaPath:"#/properties/root/properties/preview_time/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid1=_errs9===errors}else{var valid1=true}if(valid1){if(data1.duration!==undefined){let data4=data1.duration;const _errs11=errors;if(!(typeof data4=="number"&&isFinite(data4))){validate20$5.errors=[{instancePath:instancePath+"/root/duration",schemaPath:"#/properties/root/properties/duration/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid1=_errs11===errors}else{var valid1=true}}}}}}var valid0=_errs4===errors}else{var valid0=true}if(valid0){if(data.children!==undefined){let data5=data.children;const _errs13=errors;if(!Array.isArray(data5)&&data5!==null){validate20$5.errors=[{instancePath:instancePath+"/children",schemaPath:"#/properties/children/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}{if(Array.isArray(data5)){var valid2=true;const len0=data5.length;for(let i0=0;i0<len0;i0++){let data6=data5[i0];const _errs16=errors;if(!(data6&&typeof data6=="object"&&!Array.isArray(data6))){validate20$5.errors=[{instancePath:instancePath+"/children/"+i0,schemaPath:"#/properties/children/items/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}var valid2=_errs16===errors;if(!valid2){break}}}}var valid0=_errs13===errors}else{var valid0=true}}}}}}else{validate20$5.errors=[{instancePath:instancePath,schemaPath:"#/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}}validate20$5.errors=vErrors;return errors===0}var spriteValidator={exports:{}};spriteValidator.exports=validate20$4;spriteValidator.exports.default=validate20$4;const schema22$4={type:"object",additionalProperties:true,required:["type"],properties:{name:{type:"string",nullable:false},type:{enum:["text","shape","sticker"]},position:{type:"array",items:{type:"number"},maxItems:3,minItems:3},rotation:{type:"array",items:{type:"number"},maxItems:3,minItems:3},scale:{type:"array",items:{type:"number"},maxItems:3,minItems:3},order_in_layer:{type:"integer"},start_time:{type:"number"},duration:{type:"number"},sticker_format:{enum:["png","jpeg","gif","mp4","seq"]},sticker_design_type:{enum:[0,1]},sticker_path:{type:"string"},sticker_resource_id:{type:"string"},resource_name_list:{type:"array",items:{type:"string",nullable:true}},sticker_alpha:{type:"number"},sticker_flipX:{type:"boolean"},sticker_flipY:{type:"boolean"},fps:{type:"number"},sticker_loop:{type:"boolean"},anims:{type:"array",nullable:true,items:{type:"object"}}}};function validate20$4(data,{instancePath:instancePath="",parentData:parentData,parentDataProperty:parentDataProperty,rootData:rootData=data}={}){let vErrors=null;let errors=0;{if(data&&typeof data=="object"&&!Array.isArray(data)){let missing0;if(data.type===undefined&&(missing0="type")){validate20$4.errors=[{instancePath:instancePath,schemaPath:"#/required",keyword:"required",params:{missingProperty:missing0},message:"must have required property '"+missing0+"'"}];return false}else{if(data.name!==undefined){const _errs2=errors;if(typeof data.name!=="string"){validate20$4.errors=[{instancePath:instancePath+"/name",schemaPath:"#/properties/name/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid0=_errs2===errors}else{var valid0=true}if(valid0){if(data.type!==undefined){let data1=data.type;const _errs5=errors;if(!(data1==="text"||data1==="shape"||data1==="sticker")){validate20$4.errors=[{instancePath:instancePath+"/type",schemaPath:"#/properties/type/enum",keyword:"enum",params:{allowedValues:schema22$4.properties.type.enum},message:"must be equal to one of the allowed values"}];return false}var valid0=_errs5===errors}else{var valid0=true}if(valid0){if(data.position!==undefined){let data2=data.position;const _errs6=errors;{if(Array.isArray(data2)){if(data2.length>3){validate20$4.errors=[{instancePath:instancePath+"/position",schemaPath:"#/properties/position/maxItems",keyword:"maxItems",params:{limit:3},message:"must NOT have more than 3 items"}];return false}else{if(data2.length<3){validate20$4.errors=[{instancePath:instancePath+"/position",schemaPath:"#/properties/position/minItems",keyword:"minItems",params:{limit:3},message:"must NOT have fewer than 3 items"}];return false}else{var valid1=true;const len0=data2.length;for(let i0=0;i0<len0;i0++){let data3=data2[i0];const _errs8=errors;if(!(typeof data3=="number"&&isFinite(data3))){validate20$4.errors=[{instancePath:instancePath+"/position/"+i0,schemaPath:"#/properties/position/items/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid1=_errs8===errors;if(!valid1){break}}}}}else{validate20$4.errors=[{instancePath:instancePath+"/position",schemaPath:"#/properties/position/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}}var valid0=_errs6===errors}else{var valid0=true}if(valid0){if(data.rotation!==undefined){let data4=data.rotation;const _errs10=errors;{if(Array.isArray(data4)){if(data4.length>3){validate20$4.errors=[{instancePath:instancePath+"/rotation",schemaPath:"#/properties/rotation/maxItems",keyword:"maxItems",params:{limit:3},message:"must NOT have more than 3 items"}];return false}else{if(data4.length<3){validate20$4.errors=[{instancePath:instancePath+"/rotation",schemaPath:"#/properties/rotation/minItems",keyword:"minItems",params:{limit:3},message:"must NOT have fewer than 3 items"}];return false}else{var valid2=true;const len1=data4.length;for(let i1=0;i1<len1;i1++){let data5=data4[i1];const _errs12=errors;if(!(typeof data5=="number"&&isFinite(data5))){validate20$4.errors=[{instancePath:instancePath+"/rotation/"+i1,schemaPath:"#/properties/rotation/items/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid2=_errs12===errors;if(!valid2){break}}}}}else{validate20$4.errors=[{instancePath:instancePath+"/rotation",schemaPath:"#/properties/rotation/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}}var valid0=_errs10===errors}else{var valid0=true}if(valid0){if(data.scale!==undefined){let data6=data.scale;const _errs14=errors;{if(Array.isArray(data6)){if(data6.length>3){validate20$4.errors=[{instancePath:instancePath+"/scale",schemaPath:"#/properties/scale/maxItems",keyword:"maxItems",params:{limit:3},message:"must NOT have more than 3 items"}];return false}else{if(data6.length<3){validate20$4.errors=[{instancePath:instancePath+"/scale",schemaPath:"#/properties/scale/minItems",keyword:"minItems",params:{limit:3},message:"must NOT have fewer than 3 items"}];return false}else{var valid3=true;const len2=data6.length;for(let i2=0;i2<len2;i2++){let data7=data6[i2];const _errs16=errors;if(!(typeof data7=="number"&&isFinite(data7))){validate20$4.errors=[{instancePath:instancePath+"/scale/"+i2,schemaPath:"#/properties/scale/items/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid3=_errs16===errors;if(!valid3){break}}}}}else{validate20$4.errors=[{instancePath:instancePath+"/scale",schemaPath:"#/properties/scale/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}}var valid0=_errs14===errors}else{var valid0=true}if(valid0){if(data.order_in_layer!==undefined){let data8=data.order_in_layer;const _errs18=errors;if(!(typeof data8=="number"&&!(data8%1)&&!isNaN(data8)&&isFinite(data8))){validate20$4.errors=[{instancePath:instancePath+"/order_in_layer",schemaPath:"#/properties/order_in_layer/type",keyword:"type",params:{type:"integer"},message:"must be integer"}];return false}var valid0=_errs18===errors}else{var valid0=true}if(valid0){if(data.start_time!==undefined){let data9=data.start_time;const _errs20=errors;if(!(typeof data9=="number"&&isFinite(data9))){validate20$4.errors=[{instancePath:instancePath+"/start_time",schemaPath:"#/properties/start_time/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs20===errors}else{var valid0=true}if(valid0){if(data.duration!==undefined){let data10=data.duration;const _errs22=errors;if(!(typeof data10=="number"&&isFinite(data10))){validate20$4.errors=[{instancePath:instancePath+"/duration",schemaPath:"#/properties/duration/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs22===errors}else{var valid0=true}if(valid0){if(data.sticker_format!==undefined){let data11=data.sticker_format;const _errs24=errors;if(!(data11==="png"||data11==="jpeg"||data11==="gif"||data11==="mp4"||data11==="seq")){validate20$4.errors=[{instancePath:instancePath+"/sticker_format",schemaPath:"#/properties/sticker_format/enum",keyword:"enum",params:{allowedValues:schema22$4.properties.sticker_format.enum},message:"must be equal to one of the allowed values"}];return false}var valid0=_errs24===errors}else{var valid0=true}if(valid0){if(data.sticker_design_type!==undefined){let data12=data.sticker_design_type;const _errs25=errors;if(!(data12===0||data12===1)){validate20$4.errors=[{instancePath:instancePath+"/sticker_design_type",schemaPath:"#/properties/sticker_design_type/enum",keyword:"enum",params:{allowedValues:schema22$4.properties.sticker_design_type.enum},message:"must be equal to one of the allowed values"}];return false}var valid0=_errs25===errors}else{var valid0=true}if(valid0){if(data.sticker_path!==undefined){const _errs26=errors;if(typeof data.sticker_path!=="string"){validate20$4.errors=[{instancePath:instancePath+"/sticker_path",schemaPath:"#/properties/sticker_path/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid0=_errs26===errors}else{var valid0=true}if(valid0){if(data.sticker_resource_id!==undefined){const _errs28=errors;if(typeof data.sticker_resource_id!=="string"){validate20$4.errors=[{instancePath:instancePath+"/sticker_resource_id",schemaPath:"#/properties/sticker_resource_id/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid0=_errs28===errors}else{var valid0=true}if(valid0){if(data.resource_name_list!==undefined){let data15=data.resource_name_list;const _errs30=errors;{if(Array.isArray(data15)){var valid4=true;const len3=data15.length;for(let i3=0;i3<len3;i3++){let data16=data15[i3];const _errs32=errors;if(typeof data16!=="string"&&data16!==null){validate20$4.errors=[{instancePath:instancePath+"/resource_name_list/"+i3,schemaPath:"#/properties/resource_name_list/items/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid4=_errs32===errors;if(!valid4){break}}}else{validate20$4.errors=[{instancePath:instancePath+"/resource_name_list",schemaPath:"#/properties/resource_name_list/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}}var valid0=_errs30===errors}else{var valid0=true}if(valid0){if(data.sticker_alpha!==undefined){let data17=data.sticker_alpha;const _errs35=errors;if(!(typeof data17=="number"&&isFinite(data17))){validate20$4.errors=[{instancePath:instancePath+"/sticker_alpha",schemaPath:"#/properties/sticker_alpha/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs35===errors}else{var valid0=true}if(valid0){if(data.sticker_flipX!==undefined){const _errs37=errors;if(typeof data.sticker_flipX!=="boolean"){validate20$4.errors=[{instancePath:instancePath+"/sticker_flipX",schemaPath:"#/properties/sticker_flipX/type",keyword:"type",params:{type:"boolean"},message:"must be boolean"}];return false}var valid0=_errs37===errors}else{var valid0=true}if(valid0){if(data.sticker_flipY!==undefined){const _errs39=errors;if(typeof data.sticker_flipY!=="boolean"){validate20$4.errors=[{instancePath:instancePath+"/sticker_flipY",schemaPath:"#/properties/sticker_flipY/type",keyword:"type",params:{type:"boolean"},message:"must be boolean"}];return false}var valid0=_errs39===errors}else{var valid0=true}if(valid0){if(data.fps!==undefined){let data20=data.fps;const _errs41=errors;if(!(typeof data20=="number"&&isFinite(data20))){validate20$4.errors=[{instancePath:instancePath+"/fps",schemaPath:"#/properties/fps/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs41===errors}else{var valid0=true}if(valid0){if(data.sticker_loop!==undefined){const _errs43=errors;if(typeof data.sticker_loop!=="boolean"){validate20$4.errors=[{instancePath:instancePath+"/sticker_loop",schemaPath:"#/properties/sticker_loop/type",keyword:"type",params:{type:"boolean"},message:"must be boolean"}];return false}var valid0=_errs43===errors}else{var valid0=true}if(valid0){if(data.anims!==undefined){let data22=data.anims;const _errs45=errors;if(!Array.isArray(data22)&&data22!==null){validate20$4.errors=[{instancePath:instancePath+"/anims",schemaPath:"#/properties/anims/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}{if(Array.isArray(data22)){var valid5=true;const len4=data22.length;for(let i4=0;i4<len4;i4++){let data23=data22[i4];const _errs48=errors;if(!(data23&&typeof data23=="object"&&!Array.isArray(data23))){validate20$4.errors=[{instancePath:instancePath+"/anims/"+i4,schemaPath:"#/properties/anims/items/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}var valid5=_errs48===errors;if(!valid5){break}}}}var valid0=_errs45===errors}else{var valid0=true}}}}}}}}}}}}}}}}}}}}}else{validate20$4.errors=[{instancePath:instancePath,schemaPath:"#/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}}validate20$4.errors=vErrors;return errors===0}var widgetValidator={exports:{}};widgetValidator.exports=validate20$3;widgetValidator.exports.default=validate20$3;const schema22$3={type:"object",additionalProperties:true,required:["type"],properties:{type:{enum:["text","shape","sticker"]},name:{type:"string",nullable:false}}};function validate20$3(data,{instancePath:instancePath="",parentData:parentData,parentDataProperty:parentDataProperty,rootData:rootData=data}={}){let vErrors=null;let errors=0;{if(data&&typeof data=="object"&&!Array.isArray(data)){let missing0;if(data.type===undefined&&(missing0="type")){validate20$3.errors=[{instancePath:instancePath,schemaPath:"#/required",keyword:"required",params:{missingProperty:missing0},message:"must have required property '"+missing0+"'"}];return false}else{if(data.type!==undefined){let data0=data.type;const _errs2=errors;if(!(data0==="text"||data0==="shape"||data0==="sticker")){validate20$3.errors=[{instancePath:instancePath+"/type",schemaPath:"#/properties/type/enum",keyword:"enum",params:{allowedValues:schema22$3.properties.type.enum},message:"must be equal to one of the allowed values"}];return false}var valid0=_errs2===errors}else{var valid0=true}if(valid0){if(data.name!==undefined){const _errs3=errors;if(typeof data.name!=="string"){validate20$3.errors=[{instancePath:instancePath+"/name",schemaPath:"#/properties/name/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid0=_errs3===errors}else{var valid0=true}}}}else{validate20$3.errors=[{instancePath:instancePath,schemaPath:"#/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}}validate20$3.errors=vErrors;return errors===0}var animValidator={exports:{}};animValidator.exports=validate20$2;animValidator.exports.default=validate20$2;const schema22$2={type:"object",required:["anim_type"],additionalProperties:false,properties:{anim_type:{enum:["in","out","loop","caption"]},anim_script_type:{enum:["lua","js"]},anim_resource_id:{type:"string"},anim_resource_path:{type:"string"},anim_start_time:{type:"number"},duration:{type:"number"},loop_duration:{type:"number"}}};function validate20$2(data,{instancePath:instancePath="",parentData:parentData,parentDataProperty:parentDataProperty,rootData:rootData=data}={}){let vErrors=null;let errors=0;{if(data&&typeof data=="object"&&!Array.isArray(data)){let missing0;if(data.anim_type===undefined&&(missing0="anim_type")){validate20$2.errors=[{instancePath:instancePath,schemaPath:"#/required",keyword:"required",params:{missingProperty:missing0},message:"must have required property '"+missing0+"'"}];return false}else{for(const key0 in data){if(!(key0==="anim_type"||key0==="anim_script_type"||key0==="anim_resource_id"||key0==="anim_resource_path"||key0==="anim_start_time"||key0==="duration"||key0==="loop_duration")){validate20$2.errors=[{instancePath:instancePath,schemaPath:"#/additionalProperties",keyword:"additionalProperties",params:{additionalProperty:key0},message:"must NOT have additional properties"}];return false}}{if(data.anim_type!==undefined){let data0=data.anim_type;const _errs2=errors;if(!(data0==="in"||data0==="out"||data0==="loop"||data0==="caption")){validate20$2.errors=[{instancePath:instancePath+"/anim_type",schemaPath:"#/properties/anim_type/enum",keyword:"enum",params:{allowedValues:schema22$2.properties.anim_type.enum},message:"must be equal to one of the allowed values"}];return false}var valid0=_errs2===errors}else{var valid0=true}if(valid0){if(data.anim_script_type!==undefined){let data1=data.anim_script_type;const _errs3=errors;if(!(data1==="lua"||data1==="js")){validate20$2.errors=[{instancePath:instancePath+"/anim_script_type",schemaPath:"#/properties/anim_script_type/enum",keyword:"enum",params:{allowedValues:schema22$2.properties.anim_script_type.enum},message:"must be equal to one of the allowed values"}];return false}var valid0=_errs3===errors}else{var valid0=true}if(valid0){if(data.anim_resource_id!==undefined){const _errs4=errors;if(typeof data.anim_resource_id!=="string"){validate20$2.errors=[{instancePath:instancePath+"/anim_resource_id",schemaPath:"#/properties/anim_resource_id/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid0=_errs4===errors}else{var valid0=true}if(valid0){if(data.anim_resource_path!==undefined){const _errs6=errors;if(typeof data.anim_resource_path!=="string"){validate20$2.errors=[{instancePath:instancePath+"/anim_resource_path",schemaPath:"#/properties/anim_resource_path/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid0=_errs6===errors}else{var valid0=true}if(valid0){if(data.anim_start_time!==undefined){let data4=data.anim_start_time;const _errs8=errors;if(!(typeof data4=="number"&&isFinite(data4))){validate20$2.errors=[{instancePath:instancePath+"/anim_start_time",schemaPath:"#/properties/anim_start_time/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs8===errors}else{var valid0=true}if(valid0){if(data.duration!==undefined){let data5=data.duration;const _errs10=errors;if(!(typeof data5=="number"&&isFinite(data5))){validate20$2.errors=[{instancePath:instancePath+"/duration",schemaPath:"#/properties/duration/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs10===errors}else{var valid0=true}if(valid0){if(data.loop_duration!==undefined){let data6=data.loop_duration;const _errs12=errors;if(!(typeof data6=="number"&&isFinite(data6))){validate20$2.errors=[{instancePath:instancePath+"/loop_duration",schemaPath:"#/properties/loop_duration/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs12===errors}else{var valid0=true}}}}}}}}}}else{validate20$2.errors=[{instancePath:instancePath,schemaPath:"#/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}}validate20$2.errors=vErrors;return errors===0}var dependValidator={exports:{}};dependValidator.exports=validate20$1;dependValidator.exports.default=validate20$1;const schema22$1={type:"object",required:["depend_resource_list"],additionalProperties:false,properties:{depend_resource_list:{type:"array",nullable:true,items:{type:"object",required:["type","resource_id"],additionalProperties:false,properties:{type:{enum:["fonts","text_animation","sticker_animation","shape_animation","sticker_resource","flower"]},resource_id:{type:"string"},resource_path:{}}}}}};function validate20$1(data,{instancePath:instancePath="",parentData:parentData,parentDataProperty:parentDataProperty,rootData:rootData=data}={}){let vErrors=null;let errors=0;{if(data&&typeof data=="object"&&!Array.isArray(data)){let missing0;if(data.depend_resource_list===undefined&&(missing0="depend_resource_list")){validate20$1.errors=[{instancePath:instancePath,schemaPath:"#/required",keyword:"required",params:{missingProperty:missing0},message:"must have required property '"+missing0+"'"}];return false}else{for(const key0 in data){if(!(key0==="depend_resource_list")){validate20$1.errors=[{instancePath:instancePath,schemaPath:"#/additionalProperties",keyword:"additionalProperties",params:{additionalProperty:key0},message:"must NOT have additional properties"}];return false}}{if(data.depend_resource_list!==undefined){let data0=data.depend_resource_list;if(!Array.isArray(data0)&&data0!==null){validate20$1.errors=[{instancePath:instancePath+"/depend_resource_list",schemaPath:"#/properties/depend_resource_list/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}{if(Array.isArray(data0)){var valid1=true;const len0=data0.length;for(let i0=0;i0<len0;i0++){let data1=data0[i0];const _errs5=errors;{if(data1&&typeof data1=="object"&&!Array.isArray(data1)){let missing1;if(data1.type===undefined&&(missing1="type")||data1.resource_id===undefined&&(missing1="resource_id")){validate20$1.errors=[{instancePath:instancePath+"/depend_resource_list/"+i0,schemaPath:"#/properties/depend_resource_list/items/required",keyword:"required",params:{missingProperty:missing1},message:"must have required property '"+missing1+"'"}];return false}else{for(const key1 in data1){if(!(key1==="type"||key1==="resource_id"||key1==="resource_path")){validate20$1.errors=[{instancePath:instancePath+"/depend_resource_list/"+i0,schemaPath:"#/properties/depend_resource_list/items/additionalProperties",keyword:"additionalProperties",params:{additionalProperty:key1},message:"must NOT have additional properties"}];return false}}{if(data1.type!==undefined){let data2=data1.type;const _errs8=errors;if(!(data2==="fonts"||data2==="text_animation"||data2==="sticker_animation"||data2==="shape_animation"||data2==="sticker_resource"||data2==="flower")){validate20$1.errors=[{instancePath:instancePath+"/depend_resource_list/"+i0+"/type",schemaPath:"#/properties/depend_resource_list/items/properties/type/enum",keyword:"enum",params:{allowedValues:schema22$1.properties.depend_resource_list.items.properties.type.enum},message:"must be equal to one of the allowed values"}];return false}var valid2=_errs8===errors}else{var valid2=true}if(valid2){if(data1.resource_id!==undefined){const _errs9=errors;if(typeof data1.resource_id!=="string"){validate20$1.errors=[{instancePath:instancePath+"/depend_resource_list/"+i0+"/resource_id",schemaPath:"#/properties/depend_resource_list/items/properties/resource_id/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid2=_errs9===errors}else{var valid2=true}}}}}else{validate20$1.errors=[{instancePath:instancePath+"/depend_resource_list/"+i0,schemaPath:"#/properties/depend_resource_list/items/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}}var valid1=_errs5===errors;if(!valid1){break}}}}}}}}else{validate20$1.errors=[{instancePath:instancePath,schemaPath:"#/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}}validate20$1.errors=vErrors;return errors===0}var textValidator={exports:{}};textValidator.exports=validate20;textValidator.exports.default=validate20;const schema22={type:"object",additionalProperties:true,required:["type"],properties:{name:{type:"string",nullable:false},type:{enum:["text"]},position:{type:"array",items:{type:"number"},maxItems:3,minItems:3},rotation:{type:"array",items:{type:"number"},maxItems:3,minItems:3},scale:{type:"array",items:{type:"number"},maxItems:3,minItems:3},order_in_layer:{type:"integer"},start_time:{type:"number"},duration:{type:"number"},text_params:{type:"object",additionalProperties:true,properties:{richText:{type:"string"}}},anims:{type:"array",nullable:true,items:{type:"object"}}}};function validate20(data,{instancePath:instancePath="",parentData:parentData,parentDataProperty:parentDataProperty,rootData:rootData=data}={}){let vErrors=null;let errors=0;{if(data&&typeof data=="object"&&!Array.isArray(data)){let missing0;if(data.type===undefined&&(missing0="type")){validate20.errors=[{instancePath:instancePath,schemaPath:"#/required",keyword:"required",params:{missingProperty:missing0},message:"must have required property '"+missing0+"'"}];return false}else{if(data.name!==undefined){const _errs2=errors;if(typeof data.name!=="string"){validate20.errors=[{instancePath:instancePath+"/name",schemaPath:"#/properties/name/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}var valid0=_errs2===errors}else{var valid0=true}if(valid0){if(data.type!==undefined){const _errs5=errors;if(!(data.type==="text")){validate20.errors=[{instancePath:instancePath+"/type",schemaPath:"#/properties/type/enum",keyword:"enum",params:{allowedValues:schema22.properties.type.enum},message:"must be equal to one of the allowed values"}];return false}var valid0=_errs5===errors}else{var valid0=true}if(valid0){if(data.position!==undefined){let data2=data.position;const _errs6=errors;{if(Array.isArray(data2)){if(data2.length>3){validate20.errors=[{instancePath:instancePath+"/position",schemaPath:"#/properties/position/maxItems",keyword:"maxItems",params:{limit:3},message:"must NOT have more than 3 items"}];return false}else{if(data2.length<3){validate20.errors=[{instancePath:instancePath+"/position",schemaPath:"#/properties/position/minItems",keyword:"minItems",params:{limit:3},message:"must NOT have fewer than 3 items"}];return false}else{var valid1=true;const len0=data2.length;for(let i0=0;i0<len0;i0++){let data3=data2[i0];const _errs8=errors;if(!(typeof data3=="number"&&isFinite(data3))){validate20.errors=[{instancePath:instancePath+"/position/"+i0,schemaPath:"#/properties/position/items/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid1=_errs8===errors;if(!valid1){break}}}}}else{validate20.errors=[{instancePath:instancePath+"/position",schemaPath:"#/properties/position/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}}var valid0=_errs6===errors}else{var valid0=true}if(valid0){if(data.rotation!==undefined){let data4=data.rotation;const _errs10=errors;{if(Array.isArray(data4)){if(data4.length>3){validate20.errors=[{instancePath:instancePath+"/rotation",schemaPath:"#/properties/rotation/maxItems",keyword:"maxItems",params:{limit:3},message:"must NOT have more than 3 items"}];return false}else{if(data4.length<3){validate20.errors=[{instancePath:instancePath+"/rotation",schemaPath:"#/properties/rotation/minItems",keyword:"minItems",params:{limit:3},message:"must NOT have fewer than 3 items"}];return false}else{var valid2=true;const len1=data4.length;for(let i1=0;i1<len1;i1++){let data5=data4[i1];const _errs12=errors;if(!(typeof data5=="number"&&isFinite(data5))){validate20.errors=[{instancePath:instancePath+"/rotation/"+i1,schemaPath:"#/properties/rotation/items/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid2=_errs12===errors;if(!valid2){break}}}}}else{validate20.errors=[{instancePath:instancePath+"/rotation",schemaPath:"#/properties/rotation/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}}var valid0=_errs10===errors}else{var valid0=true}if(valid0){if(data.scale!==undefined){let data6=data.scale;const _errs14=errors;{if(Array.isArray(data6)){if(data6.length>3){validate20.errors=[{instancePath:instancePath+"/scale",schemaPath:"#/properties/scale/maxItems",keyword:"maxItems",params:{limit:3},message:"must NOT have more than 3 items"}];return false}else{if(data6.length<3){validate20.errors=[{instancePath:instancePath+"/scale",schemaPath:"#/properties/scale/minItems",keyword:"minItems",params:{limit:3},message:"must NOT have fewer than 3 items"}];return false}else{var valid3=true;const len2=data6.length;for(let i2=0;i2<len2;i2++){let data7=data6[i2];const _errs16=errors;if(!(typeof data7=="number"&&isFinite(data7))){validate20.errors=[{instancePath:instancePath+"/scale/"+i2,schemaPath:"#/properties/scale/items/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid3=_errs16===errors;if(!valid3){break}}}}}else{validate20.errors=[{instancePath:instancePath+"/scale",schemaPath:"#/properties/scale/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}}var valid0=_errs14===errors}else{var valid0=true}if(valid0){if(data.order_in_layer!==undefined){let data8=data.order_in_layer;const _errs18=errors;if(!(typeof data8=="number"&&!(data8%1)&&!isNaN(data8)&&isFinite(data8))){validate20.errors=[{instancePath:instancePath+"/order_in_layer",schemaPath:"#/properties/order_in_layer/type",keyword:"type",params:{type:"integer"},message:"must be integer"}];return false}var valid0=_errs18===errors}else{var valid0=true}if(valid0){if(data.start_time!==undefined){let data9=data.start_time;const _errs20=errors;if(!(typeof data9=="number"&&isFinite(data9))){validate20.errors=[{instancePath:instancePath+"/start_time",schemaPath:"#/properties/start_time/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs20===errors}else{var valid0=true}if(valid0){if(data.duration!==undefined){let data10=data.duration;const _errs22=errors;if(!(typeof data10=="number"&&isFinite(data10))){validate20.errors=[{instancePath:instancePath+"/duration",schemaPath:"#/properties/duration/type",keyword:"type",params:{type:"number"},message:"must be number"}];return false}var valid0=_errs22===errors}else{var valid0=true}if(valid0){if(data.text_params!==undefined){let data11=data.text_params;const _errs24=errors;{if(data11&&typeof data11=="object"&&!Array.isArray(data11)){if(data11.richText!==undefined){if(typeof data11.richText!=="string"){validate20.errors=[{instancePath:instancePath+"/text_params/richText",schemaPath:"#/properties/text_params/properties/richText/type",keyword:"type",params:{type:"string"},message:"must be string"}];return false}}}else{validate20.errors=[{instancePath:instancePath+"/text_params",schemaPath:"#/properties/text_params/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}}var valid0=_errs24===errors}else{var valid0=true}if(valid0){if(data.anims!==undefined){let data13=data.anims;const _errs29=errors;if(!Array.isArray(data13)&&data13!==null){validate20.errors=[{instancePath:instancePath+"/anims",schemaPath:"#/properties/anims/type",keyword:"type",params:{type:"array"},message:"must be array"}];return false}{if(Array.isArray(data13)){var valid5=true;const len3=data13.length;for(let i3=0;i3<len3;i3++){let data14=data13[i3];const _errs32=errors;if(!(data14&&typeof data14=="object"&&!Array.isArray(data14))){validate20.errors=[{instancePath:instancePath+"/anims/"+i3,schemaPath:"#/properties/anims/items/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}var valid5=_errs32===errors;if(!valid5){break}}}}var valid0=_errs29===errors}else{var valid0=true}}}}}}}}}}}}else{validate20.errors=[{instancePath:instancePath,schemaPath:"#/type",keyword:"type",params:{type:"object"},message:"must be object"}];return false}}validate20.errors=vErrors;return errors===0}class TemplateConfigParser{static parseConfig(configStr){const config=configStringToObject(configStr);if(TemplateConfigParser.useValidator){const validator=configValidator.exports;if(validator(config,{rootData:config})){if(config.type==="ScriptTemplate"){return config}}else{console.error(TEMPLATE_TAG,"TemplateConfigParser",JSON.stringify(validator.errors));return undefined}}return config}static parseChildNode(configStr){const config=configStringToObject(configStr);if(TemplateConfigParser.useValidator){const validator=widgetValidator.exports;if(validator(config,{rootData:config})){return config}else{console.error(TEMPLATE_TAG,"parseChildNode",JSON.stringify(validator.errors));return undefined}}return config}static parseSpriteConfig(configStr){const config=configStringToObject(configStr);if(TemplateConfigParser.useValidator){const validator=spriteValidator.exports;if(validator(config,{rootData:config})){return config}else{console.error(TEMPLATE_TAG,"parseSpriteConfig",JSON.stringify(validator.errors));return undefined}}return config}static parseTextConfig(configStr){const config=configStringToObject(configStr);if(TemplateConfigParser.useValidator){const validator=textValidator.exports;if(validator(config,{rootData:config})){return config}else{console.error(TEMPLATE_TAG,"parseTextConfig",JSON.stringify(validator.errors));return undefined}}return config}static parseAnimationConfig(configStr){const config=configStringToObject(configStr);if(TemplateConfigParser.useValidator){const validator=animValidator.exports;if(validator(config,{rootData:config})){return config}else{console.error(TEMPLATE_TAG,"parseAnimationConfig",JSON.stringify(validator.errors));return undefined}}return config}static parseDependConfig(configStr){const config=configStringToObject(configStr);if(TemplateConfigParser.useValidator){const validator=dependValidator.exports;if(validator(config,{rootData:config})){return config}else{console.error(TEMPLATE_TAG,"parseDependConfig",JSON.stringify(validator.errors));return undefined}}return config}}TemplateConfigParser.useValidator=true;function configStringToObject(configStr){let config=configStr;if(typeof configStr=="string"){config=JSON.parse(configStr)}return config}var Shape;(function(Shape){Shape[Shape["Square"]=0]="Square";Shape[Shape["UpSlope"]=1]="UpSlope";Shape[Shape["DownSlope"]=2]="DownSlope";Shape[Shape["Triangle"]=3]="Triangle";Shape[Shape["Custom"]=4]="Custom"})(Shape||(Shape={}));var Unit;(function(Unit){Unit[Unit["Percent"]=0]="Percent";Unit[Unit["Index"]=1]="Index"})(Unit||(Unit={}));const shapeCurveFuncMap={[Shape.Square]:(x1,x2,interval)=>x=>{const p1=x1+interval;const p2=x2-interval;if(x<x1||x>x2){return 0}else if(x>=x1&&x<=p1){return TextUtils.remap(x,x1,p1,0,1)}else if(x>=p2&&x<=x2){return TextUtils.remap(x,x2,p2,0,1)}return 1},[Shape.UpSlope]:(x1,x2)=>x=>{if(x<x1)return 0;if(x>x2)return 1;return(x-x1)/(x2-x1)},[Shape.DownSlope]:(x1,x2)=>x=>{if(x<x1)return 1;if(x>x2)return 0;return(x2-x)/(x2-x1)},[Shape.Triangle]:(x1,x2)=>x=>{if(x<x1||x>x2)return 0;const mid=(x1+x2)/2;if(x<mid)return(x-x1)/(mid-x1);else return(x2-x)/(x2-mid)},[Shape.Custom]:(x1,x2,customCurve)=>x=>{if(typeof customCurve==="number")return customCurve;if(x<x1)return customCurve(x1);if(x>x2)return customCurve(x2);return customCurve(x)}};class BaseSelector{constructor(){this.m_attrs=[];this.m_enabled=true;this.start=0;this.end=0;this.offset=0;this.unit=Unit.Percent;this.shape=Shape.Square;this.smooth=0;this.scopeStart=0;this.scopeEnd=0;this.m_enabled=true}static getShapeCurve(shape,smooth,x1,x2,time,customCurve=0){switch(shape){case Shape.Square:{const interval=typeof smooth==="function"?smooth(time)*.5:smooth*.5;return shapeCurveFuncMap[Shape.Square](x1,x2,interval)}case Shape.UpSlope:return shapeCurveFuncMap[Shape.UpSlope](x1,x2);case Shape.DownSlope:return shapeCurveFuncMap[Shape.DownSlope](x1,x2);case Shape.Triangle:return shapeCurveFuncMap[Shape.Triangle](x1,x2);case Shape.Custom:return shapeCurveFuncMap[Shape.Custom](x1,x2,customCurve);default:console.error("Caption Selector Error","Invalid shape: "+shape);return()=>0}}}BaseSelector.Shape=Shape;BaseSelector.Unit=Unit;var ClipName;(function(ClipName){ClipName["Alpha"]="li";ClipName["Color"]="lic";ClipName["Position"]="t";ClipName["DistanceFromCenter"]="dfc";ClipName["Rotate"]="r";ClipName["Scale"]="s";ClipName["FontSizeScale"]="fss";ClipName["Anchor"]="a";ClipName["ExtraMatrix"]="lem";ClipName["AnchorBasedRange"]="abr"})(ClipName||(ClipName={}));var AnchorBased;(function(AnchorBased){AnchorBased[AnchorBased["Letter"]=0]="Letter";AnchorBased[AnchorBased["Line"]=1]="Line";AnchorBased[AnchorBased["Word"]=2]="Word";AnchorBased[AnchorBased["Page"]=3]="Page"})(AnchorBased||(AnchorBased={}));var PositionType;(function(PositionType){PositionType[PositionType["Offset"]=0]="Offset";PositionType[PositionType["DistanceFromCenter"]=1]="DistanceFromCenter";PositionType[PositionType["SpaceX"]=2]="SpaceX";PositionType[PositionType["SpaceY"]=3]="SpaceY"})(PositionType||(PositionType={}));class ExtraMatrixCaculator{constructor(textComp,range){this.positionStorage={};this.distanceFromCenterStorage={};this.positionType=PositionType.Offset;this.rotateStorage={};this.scaleStorage={};this.anchorStorage={};this.anchorBased=AnchorBased.Letter;this.m_letterAnchorBasedRangeMap={};this.m_basedRect=[0,0,-1,-1];this.m_wholeRect=[0,0,-1,-1];this.m_enabled=false;this.reset();const wholeRect=textComp.getLettersRect(0,textComp.letters.size(),false,false);this.m_wholeRect=[wholeRect.x,wholeRect.y,wholeRect.width,wholeRect.height];if(range[0]<0||range[1]<0)return;if(range[0]===0&&range[1]===textComp.letters.size()){this.m_basedRect=this.m_wholeRect}else{const baseRect=textComp.getLettersRect(range[0],range[1]);this.m_basedRect=[baseRect.x,baseRect.y,baseRect.width,baseRect.height]}}set basedRect(value){this.m_basedRect=value}get basedRect(){return this.m_basedRect}set enabled(value){if(this.m_enabled===value)return;this.m_enabled=value}get enabled(){return this.m_enabled}reset(){this.m_enabled=false;this.positionStorage={};this.rotateStorage={};this.scaleStorage={};this.anchorStorage={};this.distanceFromCenterStorage={};this.anchorBased=AnchorBased.Letter}preCaculate(initValues){for(const index in this.positionStorage){if(this.positionType===PositionType.Offset){this.positionStorage[index]=[this.positionStorage[index][0]*this.m_wholeRect[2]*.5,this.positionStorage[index][1]*this.m_wholeRect[3]*.5,0]}else{const center=[this.positionStorage[index][0]*this.m_wholeRect[2]*.5,this.positionStorage[index][1]*this.m_wholeRect[3]*.5,0];const initalPosition=initValues.letterInitialPositionMap[index];if(this.positionType===PositionType.DistanceFromCenter){this.positionStorage[index]=[(initalPosition[0]-center[0])*this.distanceFromCenterStorage[index],(initalPosition[1]-center[1])*this.distanceFromCenterStorage[index],0]}else if(this.positionType===PositionType.SpaceX){this.positionStorage[index]=[(initalPosition[0]-center[0])*this.distanceFromCenterStorage[index],0,0]}else if(this.positionType===PositionType.SpaceY){this.positionStorage[index]=[0,(initalPosition[1]-center[1])*this.distanceFromCenterStorage[index],0]}else{this.positionStorage[index]=[0,0,0]}}}const letterSize=initValues.letterList.length;if(this.anchorBased===AnchorBased.Line||this.anchorBased===AnchorBased.Word){const letterRangeByRowthMap={};for(let i=0;i<letterSize;i++){const letter=initValues.letterList[i];const rowth=letter.rowth;if(letterRangeByRowthMap[rowth]===undefined){letterRangeByRowthMap[rowth]=[]}letterRangeByRowthMap[rowth].push(i)}for(const rowth in letterRangeByRowthMap){const range=letterRangeByRowthMap[rowth];for(const index of range){this.m_letterAnchorBasedRangeMap[index]=[range[0],range[range.length-1]+1]}}}else{for(let i=0;i<letterSize;i++){if(this.anchorBased===AnchorBased.Letter){this.m_letterAnchorBasedRangeMap[i]=[i,i+1]}else if(this.anchorBased===AnchorBased.Page){this.m_letterAnchorBasedRangeMap[i]=[0,initValues.letterList.length]}}}}compose(index){const position=this.positionStorage[index]===undefined?[0,0,0]:this.positionStorage[index];const rotate=this.rotateStorage[index]===undefined?[0,0,0]:this.rotateStorage[index];const scale=this.scaleStorage[index]===undefined?[1,1,1]:this.scaleStorage[index];const anchor=this.anchorStorage[index]===undefined?[0,0,0]:this.anchorStorage[index];const anchorBasedRange=this.m_letterAnchorBasedRangeMap[index];return[...position,...rotate,...scale,...anchor,...anchorBasedRange]}}class BaseClip{constructor(){this.m_property=""}}class TextAlphaClip extends BaseClip{constructor(info){super();this.m_property=ClipName.Alpha;this.m_alpha=KeyframeAttrUtils.getKeyframeValue(info)}checkInitValue(animingMap,initValues,index){if(animingMap[index]===undefined){animingMap[index]={}}if(initValues.letterInstanceColorMap[index]&&animingMap[index][this.m_property]===undefined){const alpha=initValues.letterInstanceColorMap[index][3];animingMap[index][this.m_property]=alpha}}updateAnimingData(time,progress,animingMap,range,mappingIndexFunc){const alpha=typeof this.m_alpha==="number"?this.m_alpha:this.m_alpha(time);for(let i=range[0];i<range[1];i++){if(animingMap[i]===undefined||animingMap[i][this.m_property]===undefined)continue;const index=mappingIndexFunc?mappingIndexFunc(i):i;const mixProgress=typeof progress==="number"?progress:progress(i);const oriAlpha=animingMap[index][this.m_property];const targetAlpha=TextUtils.mix(oriAlpha,alpha,mixProgress);animingMap[index][this.m_property]=targetAlpha}}}var Mode;(function(Mode){Mode[Mode["Fixed"]=0]="Fixed";Mode[Mode["Normal"]=1]="Normal"})(Mode||(Mode={}));class TextAnchorClip extends BaseClip{constructor(info){super();this.m_positionStroage={};this.based=AnchorBased.Letter;this.m_property=ClipName.Anchor;this.m_mode=info.mode.value;this.m_position=[KeyframeAttrUtils.getKeyframeValue(info.x),KeyframeAttrUtils.getKeyframeValue(info.y)];this.caculateInExtraMatrix=true;this.based=info.based.value}checkInitValue(animingMap,initValues,index){if(animingMap[index]===undefined){animingMap[index]={}}if(animingMap[index][ClipName.ExtraMatrix]===undefined){animingMap[index][ClipName.ExtraMatrix]=[]}}updateAnimingData(time,progress,animingMap,range,mappingIndexFunc){const posX=typeof this.m_position[0]==="number"?this.m_position[0]:this.m_position[0](time);const posY=typeof this.m_position[1]==="number"?this.m_position[1]:this.m_position[1](time);for(let i=range[0];i<range[1];i++){if(animingMap[i]===undefined)continue;const oriAnchor=[0,0];const index=mappingIndexFunc?mappingIndexFunc(i):i;if(this.m_mode===Mode.Fixed){this.m_positionStroage[index]=[posX,posY,0]}else{const mixProgress=typeof progress==="number"?progress:progress(i);this.m_positionStroage[index]=[TextUtils.mix(oriAnchor[0],posX,mixProgress),TextUtils.mix(oriAnchor[1],posY,mixProgress),0]}}}assignInCaculator(index,extraMatrixCaculator){if(this.m_positionStroage[index]===undefined)return;extraMatrixCaculator.anchorBased=this.based;extraMatrixCaculator.anchorStorage=this.m_positionStroage}}class TextColorClip extends BaseClip{constructor(info){super();this.m_property=ClipName.Color;this.m_color=[info.value[0],info.value[1],info.value[2]]}checkInitValue(animingMap,initValues,index){if(animingMap[index]===undefined){animingMap[index]={}}if(initValues.letterInstanceColorMap[index]&&animingMap[index][this.m_property]===undefined){const color=initValues.letterInstanceColorMap[index];animingMap[index][this.m_property]=[color[0],color[1],color[2]]}}updateAnimingData(time,progress,animingMap,range,mappingIndexFunc){for(let i=range[0];i<range[1];i++){if(animingMap[i]===undefined||animingMap[i][this.m_property]===undefined)continue;const oriColor=animingMap[i][this.m_property];const color=typeof progress==="number"?[TextUtils.mix(oriColor[0],this.m_color[0],progress),TextUtils.mix(oriColor[1],this.m_color[1],progress),TextUtils.mix(oriColor[2],this.m_color[2],progress)]:[TextUtils.mix(oriColor[0],this.m_color[0],progress(i)),TextUtils.mix(oriColor[1],this.m_color[1],progress(i)),TextUtils.mix(oriColor[2],this.m_color[2],progress(i))];const index=mappingIndexFunc?mappingIndexFunc(i):i;animingMap[index][this.m_property]=color}}}class TextPositionClip extends BaseClip{constructor(info){super();this.m_positionStroage={};this.m_distanceFromCenter=1;this.m_distanceFromCenterStroage={};this.m_type=PositionType.Offset;this.m_property=ClipName.Position;this.m_position=[KeyframeAttrUtils.getKeyframeValue(info.x),KeyframeAttrUtils.getKeyframeValue(info.y)];this.m_type=info.type.value;this.caculateInExtraMatrix=true;if((this.m_type===PositionType.DistanceFromCenter||this.m_type===PositionType.SpaceX||this.m_type===PositionType.SpaceY)&&"distance"in info){this.m_distanceFromCenter=KeyframeAttrUtils.getKeyframeValue(info.distance)}}checkInitValue(animingMap,initValues,index){if(animingMap[index]===undefined){animingMap[index]={}}if(animingMap[index][ClipName.ExtraMatrix]===undefined){animingMap[index][ClipName.ExtraMatrix]=[]}if(this.m_type===PositionType.DistanceFromCenter||this.m_type===PositionType.SpaceX||this.m_type===PositionType.SpaceY){if(animingMap[index][ClipName.DistanceFromCenter]===undefined){animingMap[index][ClipName.DistanceFromCenter]=0}}}updateAnimingData(time,progress,animingMap,range,mappingIndexFunc){const posX=typeof this.m_position[0]==="number"?this.m_position[0]:this.m_position[0](time);const posY=typeof this.m_position[1]==="number"?this.m_position[1]:this.m_position[1](time);for(let i=range[0];i<range[1];i++){if(animingMap[i]===undefined)continue;const oriPosition=[0,0,0];const mixProgress=typeof progress==="number"?progress:progress(i);const index=mappingIndexFunc?mappingIndexFunc(i):i;this.m_positionStroage[index]=[TextUtils.mix(oriPosition[0],posX,mixProgress),TextUtils.mix(oriPosition[1],posY,mixProgress),0];if(this.m_type===PositionType.DistanceFromCenter||this.m_type===PositionType.SpaceX||this.m_type===PositionType.SpaceY){const distanceFromCenter=typeof this.m_distanceFromCenter==="number"?this.m_distanceFromCenter:this.m_distanceFromCenter(time);this.m_distanceFromCenterStroage[index]=TextUtils.mix(0,distanceFromCenter,mixProgress)}}}assignInCaculator(index,extraMatrixCaculator){if(this.m_positionStroage[index]===undefined)return;extraMatrixCaculator.enabled=true;extraMatrixCaculator.positionStorage=this.m_positionStroage;extraMatrixCaculator.positionType=this.m_type;if(this.m_type===PositionType.DistanceFromCenter||this.m_type===PositionType.SpaceX||this.m_type===PositionType.SpaceY){extraMatrixCaculator.distanceFromCenterStorage=this.m_distanceFromCenterStroage}}}class TextRotateClip extends BaseClip{constructor(info){super();this.m_rotateStroage={};this.m_property=ClipName.Rotate;this.m_rotate=[KeyframeAttrUtils.getKeyframeValue(info.x),KeyframeAttrUtils.getKeyframeValue(info.y),KeyframeAttrUtils.getKeyframeValue(info.z)];this.caculateInExtraMatrix=true}checkInitValue(animingMap,initValues,index){if(animingMap[index]===undefined){animingMap[index]={}}if(animingMap[index][ClipName.ExtraMatrix]===undefined){animingMap[index][ClipName.ExtraMatrix]=[]}}updateAnimingData(time,progress,animingMap,range,mappingIndexFunc){const rotateX=typeof this.m_rotate[0]==="number"?this.m_rotate[0]:this.m_rotate[0](time);const rotateY=typeof this.m_rotate[1]==="number"?this.m_rotate[1]:this.m_rotate[1](time);const rotateZ=typeof this.m_rotate[2]==="number"?this.m_rotate[2]:this.m_rotate[2](time);for(let i=range[0];i<range[1];i++){if(animingMap[i]===undefined)continue;const oriRotate=[0,0,0];const mixProgress=typeof progress==="number"?progress:progress(i);const index=mappingIndexFunc?mappingIndexFunc(i):i;this.m_rotateStroage[index]=[TextUtils.mix(oriRotate[0],rotateX,mixProgress),TextUtils.mix(oriRotate[1],rotateY,mixProgress),TextUtils.mix(oriRotate[2],rotateZ,mixProgress)]}}assignInCaculator(index,extraMatrixCaculator){if(this.m_rotateStroage[index]===undefined)return;extraMatrixCaculator.enabled=true;extraMatrixCaculator.rotateStorage=this.m_rotateStroage}}class TextScaleClip extends BaseClip{constructor(info){super();this.m_separation=false;this.m_scaleStroage={};this.m_property=ClipName.Scale;this.m_scale=[KeyframeAttrUtils.getKeyframeValue(info.x),KeyframeAttrUtils.getKeyframeValue(info.y)];this.m_separation=info.separation.value;this.caculateInExtraMatrix=true}checkInitValue(animingMap,initValues,index){if(animingMap[index]===undefined){animingMap[index]={}}if(animingMap[index][ClipName.ExtraMatrix]===undefined){animingMap[index][ClipName.ExtraMatrix]=[]}}updateAnimingData(time,progress,animingMap,range,mappingIndexFunc){const scaleX=typeof this.m_scale[0]==="number"?this.m_scale[0]:this.m_scale[0](time);const scaleY=this.m_separation?typeof this.m_scale[1]==="number"?this.m_scale[1]:this.m_scale[1](time):scaleX;for(let i=range[0];i<range[1];i++){if(animingMap[i]===undefined)continue;const oriScale=[1,1,1];const mixProgress=typeof progress==="number"?progress:progress(i);const index=mappingIndexFunc?mappingIndexFunc(i):i;this.m_scaleStroage[index]=[TextUtils.mix(oriScale[0],scaleX,mixProgress),TextUtils.mix(oriScale[1],scaleY,mixProgress),1]}}assignInCaculator(index,extraMatrixCaculator){if(this.m_scaleStroage[index]===undefined)return;extraMatrixCaculator.enabled=true;extraMatrixCaculator.scaleStorage=this.m_scaleStroage}}class TextFontSizeClip extends BaseClip{constructor(info){super();this.m_property=ClipName.FontSizeScale;this.m_fontSizeScale=KeyframeAttrUtils.getKeyframeValue(info);this.affectTypeSetting=true}checkInitValue(animingMap,initValues,index){if(animingMap[index]===undefined){animingMap[index]={}}if(animingMap[index][this.m_property]===undefined){const fontSize=initValues.letterFontSizeMap[index];animingMap[index][this.m_property]=[fontSize,1]}}updateAnimingData(time,progress,animingMap,range,mappingIndexFunc){const fontSizeScale=typeof this.m_fontSizeScale==="number"?this.m_fontSizeScale:this.m_fontSizeScale(time);for(let i=range[0];i<range[1];i++){if(animingMap[i]===undefined||animingMap[i][this.m_property]===undefined)continue;const oriFontSizeScale=animingMap[i][this.m_property];const mixProgress=typeof progress==="number"?progress:progress(i);const index=mappingIndexFunc?mappingIndexFunc(i):i;animingMap[index][this.m_property]=[oriFontSizeScale[0],TextUtils.mix(oriFontSizeScale[1],fontSizeScale,mixProgress)]}}}const ClipNameMap={alpha:ClipName.Alpha,color:ClipName.Color,position:ClipName.Position,rotate:ClipName.Rotate,scale:ClipName.Scale,fontSizeScale:ClipName.FontSizeScale,anchor:ClipName.Anchor,extraMatrix:ClipName.ExtraMatrix};class ClipFactory{static createClip(info,clipType){const clipName=ClipNameMap[clipType];switch(clipName){case ClipName.Alpha:return new TextAlphaClip(info);case ClipName.Color:return new TextColorClip(info);case ClipName.Position:return new TextPositionClip(info);case ClipName.Rotate:return new TextRotateClip(info);case ClipName.Scale:return new TextScaleClip(info);case ClipName.FontSizeScale:return new TextFontSizeClip(info);case ClipName.Anchor:return new TextAnchorClip(info);default:console.error(`Unknown attr type: ${clipType}`);return undefined}}}var ConstrainedType;(function(ConstrainedType){ConstrainedType[ConstrainedType["Normal"]=0]="Normal";ConstrainedType[ConstrainedType["SameInLines"]=1]="SameInLines"})(ConstrainedType||(ConstrainedType={}));class TextSelector extends BaseSelector{constructor(info,letters){super();this.randomSort=false;this.randomSeed=0;this.constrained=ConstrainedType.Normal;this.intensity=1;this.m_randomIndexMap={};this.m_constrainedLineMap=null;this.m_lineBreakMap={};const selectorAttrs=info.selector_attrs;this.start=KeyframeAttrUtils.getKeyframeValue(selectorAttrs.start);this.end=KeyframeAttrUtils.getKeyframeValue(selectorAttrs.end);this.offset=KeyframeAttrUtils.getKeyframeValue(selectorAttrs.offset);this.unit=BaseSelector.Unit.Percent;this.shape=selectorAttrs.shape.value;this.smooth=KeyframeAttrUtils.getKeyframeValue(selectorAttrs.smooth);this.randomSort=selectorAttrs.randomSort.value;this.randomSeed=KeyframeAttrUtils.getKeyframeValue(selectorAttrs.randomSeed);this.customShape=selectorAttrs.customShape;this.scopeStart=0;this.scopeEnd=letters.length;if("constrained"in selectorAttrs){this.constrained=selectorAttrs.constrained.value}if("intensity"in selectorAttrs){this.intensity=KeyframeAttrUtils.getKeyframeValue(selectorAttrs.intensity)}if("base_attrs"in info){const baseAttrs=info.base_attrs;for(const key of Object.keys(baseAttrs)){const clip=ClipFactory.createClip(baseAttrs[key],key);if(clip){this.m_attrs.push(clip)}}}}checkInitValueByAttrs(time,animingMap,initValues){if(this.scopeStart<this.scopeEnd){for(let i=this.scopeStart;i<this.scopeEnd;i++){for(const attr of this.m_attrs){attr.checkInitValue(animingMap,initValues,i)}}if(this.constrained===ConstrainedType.SameInLines&&this.m_constrainedLineMap===null){this.m_constrainedLineMap={};for(let i=this.scopeStart;i<this.scopeEnd;i++){const letter=initValues.letterList[i];const rowth=letter.rowth;if(this.m_constrainedLineMap[rowth]===undefined){this.m_constrainedLineMap[rowth]=[]}this.m_constrainedLineMap[rowth].push(i);if(letter.utf8==="\n"){this.m_lineBreakMap[rowth]=i}}}this.m_enabled=true}else{console.error(`error start: ${this.scopeStart} end: ${this.scopeEnd} in selector`);this.m_enabled=false}}updateAnimingData(time,animingMap){if(!this.m_enabled)return;this._updateIfRandomSort(time);if(this.constrained===ConstrainedType.SameInLines){this._updateAnimingDataForSameInLines(time,animingMap)}else{this._updateAnimingDataForNormal(time,animingMap)}}_updateAnimingDataForSameInLines(time,animingMap){let offsetInLetters=0;for(const rowth in this.m_constrainedLineMap){const indices=this.m_constrainedLineMap[Number(rowth)];if(indices&&indices.length>0){let count=indices.length;if(this.m_lineBreakMap[Number(rowth)]!==undefined){count-=1}const offset=typeof this.offset==="function"?this.offset(time):this.offset;let start=typeof this.start==="function"?this.start(time):this.start;let end=typeof this.end==="function"?this.end(time):this.end;start=(start+offset)*count;end=(end+offset)*count;start-=.5;end-=.5;const shapeFunc=this._getShapeFunc(time,start+offsetInLetters,end+offsetInLetters,TextSelector.adaptCustomShape(this.customShape,start,end,offsetInLetters));for(const attr of this.m_attrs){attr.updateAnimingData(time,shapeFunc,animingMap,[offsetInLetters,offsetInLetters+count],this.randomSort?index=>this.m_randomIndexMap[index]:undefined)}offsetInLetters+=indices.length}}}_updateAnimingDataForNormal(time,animingMap){const count=this.scopeEnd-this.scopeStart;const offset=typeof this.offset==="function"?this.offset(time):this.offset;let start=typeof this.start==="function"?this.start(time):this.start;start=(start+offset)*count;let end=typeof this.end==="function"?this.end(time):this.end;end=(end+offset)*count;start-=.5;end-=.5;const shapeFunc=this._getShapeFunc(time,start,end,TextSelector.adaptCustomShape(this.customShape,start,end));for(const attr of this.m_attrs){attr.updateAnimingData(time,shapeFunc,animingMap,[this.scopeStart,this.scopeEnd],this.randomSort?index=>this.m_randomIndexMap[index]:undefined)}}_updateIfRandomSort(time){for(let i=this.scopeStart;i<this.scopeEnd;i++){this.m_randomIndexMap[i]=i}if(this.randomSort){const seed=typeof this.randomSeed==="function"?this.randomSeed(time):this.randomSeed;const random=seedrandom(seed.toString());const values=Object.values(this.m_randomIndexMap);for(let i=values.length-1;i>0;i--){const randVal=random();const j=Math.floor(randVal*(i+1));if(this._checkIndexesIsLineBreak(i,j))continue;[values[i],values[j]]=[values[j],values[i]]}Object.keys(this.m_randomIndexMap).forEach(((key,index)=>{this.m_randomIndexMap[Number(key)]=values[index]}))}}_checkIndexesIsLineBreak(index1,index2){if(this.m_constrainedLineMap===null||this.constrained!==ConstrainedType.SameInLines){return false}for(const rowth in this.m_lineBreakMap){if(this.m_lineBreakMap[Number(rowth)]===index1||this.m_lineBreakMap[Number(rowth)]===index2){return true}}return false}_getShapeFunc(time,start,end,customShape=0){const tmpShapeFunc=this.shape===Shape.Custom?BaseSelector.getShapeCurve(this.shape,this.smooth,start,end,time,customShape):BaseSelector.getShapeCurve(this.shape,this.smooth,start,end,time);const intensity=typeof this.intensity==="function"?this.intensity(time):this.intensity;return x=>tmpShapeFunc(x)*intensity}static adaptCustomShape(customShape,start,end,offsetInLetters=0){if("motionKeyFrameInfo"in customShape&&customShape.motionKeyFrameInfo.length>0){const keyframeInfo=customShape.motionKeyFrameInfo;const count=end-start;const keyframes=[];const scale=count/TextSelector.STUDIO_TOTAL_TIME;for(let i=0;i<keyframeInfo.length;i++){const keyframe=Object.assign({},keyframeInfo[i]);keyframe.t=keyframe.t*scale+start;keyframe.vti=keyframe.vti*scale;keyframe.vto=keyframe.vto*scale;keyframes.push(keyframe)}const motion=KeyframeAttrUtils.createMotion(keyframes).delay(0).start(keyframes[0].t);return x=>{motion.update(x-offsetInLetters,false);const value=motion.getObject().v;return value}}return customShape.value}handleExtraMatrix(lettersAnimingMap,caculator,initValues){for(let i=this.scopeStart;i<this.scopeEnd;i++){if(lettersAnimingMap[i]===undefined)continue;for(const attr of this.m_attrs){if("caculateInExtraMatrix"in attr&&"assignInCaculator"in attr){attr.assignInCaculator(i,caculator)}}}if(caculator.enabled){caculator.preCaculate(initValues);for(let i=this.scopeStart;i<this.scopeEnd;i++){if(lettersAnimingMap[i]===undefined)continue;const compose=caculator.compose(i);lettersAnimingMap[i][ClipName.ExtraMatrix].push(compose)}}}}TextSelector.STUDIO_TOTAL_TIME=3;var Amaz$6=effect.Amaz;class TextPostEffect{constructor(info,animationPath){this.m_params={};this.m_entity=null;this.m_postProcess=null;this.m_scriptComp=null;this.selectorShapeValueParams={};this.m_path=info.path;this.name=info.name;this.m_prefabName=info.prefabName;if("isRelativePath"in info&&info.isRelativePath===true){this.m_path=animationPath+this.m_path}for(const key in info.params){const param=info.params[key];this.m_params[key]=KeyframeAttrUtils.getKeyframeValue(param)}}instantiateFromText(renderGroup,parent){const prefab=Amaz$6.AmazingManager.getSingleton("PrefabManager").loadPrefab(`${this.m_path}/AmazingFeature`,`${this.m_path}/AmazingFeature/prefabs/${this.m_prefabName}`);this.m_entity=prefab.instantiateToEntity(parent.entity.scene,parent.entity);this.m_entity.name=`text_effect_${this.name}`;this.m_postProcess=this.m_entity.addComponent("PostProcess");this.m_postProcess.addListenerToRender(renderGroup,Amaz$6.TextRenderGroupLayerEvent.AFTER_TEXT_GROUP_RENDER);this.m_scriptComp=this.m_entity.getComponent("ScriptComponent");return this}setParam(key,value){if(this.m_scriptComp){this.m_scriptComp.call("setEffectAttr",[key,value,this.m_scriptComp])}}animing(time){for(const key in this.m_params){const param=this.m_params[key];const value=typeof param==="function"?param(time):param;if(value!==undefined){if(key in this.selectorShapeValueParams){const shapeValue=this.selectorShapeValueParams[key];this.setParam(key,value*shapeValue)}else{this.setParam(key,value)}}}if(this.m_scriptComp){this.m_scriptComp.call("onUpdate",[time,this.m_scriptComp])}}destroy(){if(this.m_entity){this.m_entity.scene.removeEntity(this.m_entity);this.m_entity=null}}}var Amaz$5=effect.Amaz;class TextRenderGroup{constructor(startIndex,endIndex,expandRatioX,expandRatioY,effects,animationPath){this.m_startIndex=0;this.m_endIndex=0;this.m_expandRatioX=1;this.m_expandRatioY=1;this.m_fixedRect=true;this.m_textRenderGroup=null;this.m_entity=null;this.m_enabled=false;this.m_startIndex=startIndex;this.m_endIndex=endIndex;this.m_expandRatioX=expandRatioX;this.m_expandRatioY=expandRatioY;this.m_effects=[];for(let i=0;i<effects.length;i++){const effectInfo=effects[i];const postEffect=new TextPostEffect(effectInfo,animationPath);this.m_effects.push(postEffect)}this.m_enabled=true}instantiateFromText(parent,textComp){if(this.m_enabled==false){console.error("TextRenderGroup instantiate failed because of invalid params");return}this.m_textRenderGroup=textComp.addRenderGroup(this.m_startIndex,this.m_endIndex);this.m_textRenderGroup.expandRectByRatio=new Amaz$5.Vector2f(this.m_expandRatioX,this.m_expandRatioY);this.m_textRenderGroup.isFixedRect=this.m_fixedRect;if(parent==null){return}this.m_entity=AmazUtils$1.createEntity(`effect_${this.m_startIndex}_${this.m_endIndex}`,textComp.entity.scene);const childTrans=this.m_entity.transform;childTrans.parent=parent;parent.addTransform(childTrans);for(let i=0;i<this.m_effects.length;i++){const effect=this.m_effects[i];effect.instantiateFromText(this.m_textRenderGroup,childTrans)}const worldScale=textComp.entity.getComponent("Transform").getWorldScale();const pixelRect=this.m_textRenderGroup.getPixelRect();pixelRect.width=Math.ceil(pixelRect.width*worldScale.x);pixelRect.height=Math.ceil(pixelRect.height*worldScale.y);const showingTexture=this._createRenderTexture(pixelRect.width,pixelRect.height);if(this.m_effects.length==1){const effect=this.m_effects[0];const inputTex=this.m_textRenderGroup.getTextMainTex();const outputTex=showingTexture;effect.setParam("InputTex",inputTex);effect.setParam("OutputTex",outputTex)}else{const midRt=this._createRenderTexture(pixelRect.width,pixelRect.height);let changeFlag=true;for(let i=this.m_effects.length-1;i>=0;i--){const effect=this.m_effects[i];if(changeFlag){effect.setParam("OutputTex",showingTexture);effect.setParam("InputTex",midRt)}else{effect.setParam("OutputTex",midRt);effect.setParam("InputTex",showingTexture)}changeFlag=!changeFlag}const firstEffect=this.m_effects[0];firstEffect.setParam("InputTex",this.m_textRenderGroup.getTextMainTex())}this.m_textRenderGroup.setShowingTexture(showingTexture);return this}setShapeValue(shapeValue,effectSelectorParams=null){if(effectSelectorParams==null){for(let i=0;i<this.m_effects.length;i++){const effect=this.m_effects[i];effect.selectorShapeValueParams={}}return}for(let i=0;i<this.m_effects.length;i++){const effect=this.m_effects[i];if(effect.name in effectSelectorParams){const selectorParams=effectSelectorParams[effect.name];const params={};for(const key in selectorParams){if(selectorParams[key]){params[key]=shapeValue}}effect.selectorShapeValueParams=params}}}animing(time){if(this.m_enabled==false){console.error("current TextRenderGroup is not enabled");return}for(let i=0;i<this.m_effects.length;i++){const effect=this.m_effects[i];effect.setParam("textExpandRatio",new Amaz$5.Vector2f(this.m_expandRatioX,this.m_expandRatioY));effect.animing(time)}}destroy(){for(const effect of this.m_effects){effect.destroy()}this.m_effects=[]}_createRenderTexture(width,height){const rt=new Amaz$5.RenderTexture;rt.width=width;rt.height=height;rt.depth=1;rt.filterMipmap=Amaz$5.FilterMipmapMode.NONE;rt.attachment=Amaz$5.RenderTextureAttachment.NONE;return rt}}exports.TextRenderGroup=TextRenderGroup;var TextPostRenderMode;(function(TextPostRenderMode){TextPostRenderMode[TextPostRenderMode["Page"]=0]="Page";TextPostRenderMode[TextPostRenderMode["PerLetter"]=1]="PerLetter";TextPostRenderMode[TextPostRenderMode["PerLine"]=2]="PerLine"})(TextPostRenderMode||(TextPostRenderMode={}));const SplitRenderGroupByModeFuncMaps={[TextPostRenderMode.Page]:(textComp,rgSelector,animationPath)=>{const startIndex=0;const endIndex=textComp.letters.size();const renderGroup=new TextRenderGroup(startIndex,endIndex,rgSelector.expandRatioX,rgSelector.expandRatioY,rgSelector.effects,animationPath);rgSelector.renderGroups.push(renderGroup)},[TextPostRenderMode.PerLetter]:(textComp,rgSelector,animationPath)=>{for(let i=0;i<textComp.letters.size();i++){const renderGroup=new TextRenderGroup(i,i+1,rgSelector.expandRatioX,rgSelector.expandRatioY,rgSelector.effects,animationPath);rgSelector.renderGroups.push(renderGroup)}},[TextPostRenderMode.PerLine]:(textComp,rgSelector,animationPath)=>{const lineInfo=[];let startIndex=0;let endIndex=0;for(let i=0;i<textComp.letters.size();i++){const letter=textComp.letters.get(i);if(letter.utf8=="\n"){if(endIndex>startIndex){lineInfo.push({startIndex:startIndex,endIndex:endIndex})}startIndex=i+1}endIndex=i}lineInfo.push({startIndex:startIndex,endIndex:endIndex});for(let i=0;i<lineInfo.length;i++){const renderGroup=new TextRenderGroup(lineInfo[i].startIndex,lineInfo[i].endIndex+1,rgSelector.expandRatioX,rgSelector.expandRatioY,rgSelector.effects,animationPath);rgSelector.renderGroups.push(renderGroup)}}};class TextRenderGroupSelector{constructor(info){this.m_renderOutlineDisplay=null;this.m_mode=TextPostRenderMode.Page;this.m_entity=null;this.m_enabled=false;this.m_randomIndexMap={};this.shape=Shape.Square;this.randomSort=false;this.randomSeed=0;this.expandRatioX=1;this.expandRatioY=1;this.scopeStart=0;this.scopeEnd=0;this.offset=0;this.effects=[];this.renderGroups=[];const renderGroupAttrs=info.renderGroup_attrs;this.expandRatioX=renderGroupAttrs.expandRatioX.value;this.expandRatioY=renderGroupAttrs.expandRatioY.value;this.m_mode=renderGroupAttrs.mode.value;this.effects=info.effects;this.shape=renderGroupAttrs.shape.value;this.randomSort=renderGroupAttrs.randomSort.value;this.randomSeed=renderGroupAttrs.randomSeed.value;this.customShape=renderGroupAttrs.customShape;this.offset=KeyframeAttrUtils.getKeyframeValue(renderGroupAttrs.offset);this.effectSelectorParams=renderGroupAttrs.effectSelectorParams;if(renderGroupAttrs.renderOutlineDisplay){this.m_renderOutlineDisplay=renderGroupAttrs.renderOutlineDisplay.value}this.m_enabled=true}instantiateFromText(textComp,index,animationPath){if(this.m_enabled==false||textComp==null){console.error("TextRenderGroupSelector instantiate failed because of invalid params");return}SplitRenderGroupByModeFuncMaps[this.m_mode](textComp,this,animationPath);if(this.renderGroups.length==0){console.error("TextRenderGroup instantiate failed because of renderGroups is empty");return}if(this.m_renderOutlineDisplay!==null){textComp.outlineDisplay=this.m_renderOutlineDisplay}else{textComp.outlineDisplay=false}if(this.effects.length==0){for(let i=0;i<this.renderGroups.length;i++){const renderGroup=this.renderGroups[i];renderGroup.instantiateFromText(null,textComp)}return}this.m_entity=AmazUtils$1.createEntity("renderGroup_"+index,textComp.entity.scene);const parentTrans=textComp.entity.getComponent("Transform");const childTrans=this.m_entity.transform;childTrans.parent=parentTrans;parentTrans.addTransform(childTrans);for(let i=0;i<this.renderGroups.length;i++){const renderGroup=this.renderGroups[i];renderGroup.instantiateFromText(childTrans,textComp)}if(this.m_mode==TextPostRenderMode.PerLetter){this.scopeStart=0;this.scopeEnd=this.renderGroups.length;this.m_randomIndexMap={};for(let i=0;i<this.renderGroups.length;i++){this.m_randomIndexMap[i]=i}if(this.randomSort){const seed=this.randomSeed;const random=seedrandom(seed.toString());const values=Object.values(this.m_randomIndexMap);for(let i=values.length-1;i>0;i--){const randVal=random();const j=Math.floor(randVal*(i+1));[values[i],values[j]]=[values[j],values[i]]}Object.keys(this.m_randomIndexMap).forEach(((key,index)=>{this.m_randomIndexMap[Number(key)]=values[index]}))}}}animing(time){if(this.m_enabled==false){console.error("current TextRenderGroupSelector is not enabled");return}if(this.m_mode==TextPostRenderMode.PerLetter){const count=this.scopeEnd-this.scopeStart;const offset=typeof this.offset==="function"?this.offset(time):this.offset;let start=this.scopeStart/count;start=(start+offset)*count;let end=this.scopeEnd/count;end=(end+offset)*count;const shapeFunc=this.shape===Shape.Custom?BaseSelector.getShapeCurve(this.shape,0,start,end,time,TextSelector.adaptCustomShape(this.customShape,start,end)):BaseSelector.getShapeCurve(this.shape,0,start,end,time);for(let i=0;i<this.renderGroups.length;i++){const index=this.m_randomIndexMap[i];const shapeValue=shapeFunc(index+.5);const renderGroup=this.renderGroups[i];renderGroup.setShapeValue(shapeValue,this.effectSelectorParams)}}else{for(let i=0;i<this.renderGroups.length;i++){const renderGroup=this.renderGroups[i];renderGroup.setShapeValue(1)}}for(let i=0;i<this.renderGroups.length;i++){const renderGroup=this.renderGroups[i];renderGroup.animing(time)}}destroy(){for(let i=0;i<this.renderGroups.length;i++){const renderGroup=this.renderGroups[i];renderGroup.destroy()}this.renderGroups=[]}}exports.TextRenderGroup=TextRenderGroup;var lzString={exports:{}};(function(module){var LZString=function(){var f=String.fromCharCode;var keyStrBase64="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var keyStrUriSafe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$";var baseReverseDic={};function getBaseValue(alphabet,character){if(!baseReverseDic[alphabet]){baseReverseDic[alphabet]={};for(var i=0;i<alphabet.length;i++){baseReverseDic[alphabet][alphabet.charAt(i)]=i}}return baseReverseDic[alphabet][character]}var LZString={compressToBase64:function(input){if(input==null)return"";var res=LZString._compress(input,6,(function(a){return keyStrBase64.charAt(a)}));switch(res.length%4){default:case 0:return res;case 1:return res+"===";case 2:return res+"==";case 3:return res+"="}},decompressFromBase64:function(input){if(input==null)return"";if(input=="")return null;return LZString._decompress(input.length,32,(function(index){return getBaseValue(keyStrBase64,input.charAt(index))}))},compressToUTF16:function(input){if(input==null)return"";return LZString._compress(input,15,(function(a){return f(a+32)}))+" "},decompressFromUTF16:function(compressed){if(compressed==null)return"";if(compressed=="")return null;return LZString._decompress(compressed.length,16384,(function(index){return compressed.charCodeAt(index)-32}))},compressToUint8Array:function(uncompressed){var compressed=LZString.compress(uncompressed);var buf=new Uint8Array(compressed.length*2);for(var i=0,TotalLen=compressed.length;i<TotalLen;i++){var current_value=compressed.charCodeAt(i);buf[i*2]=current_value>>>8;buf[i*2+1]=current_value%256}return buf},decompressFromUint8Array:function(compressed){if(compressed===null||compressed===undefined){return LZString.decompress(compressed)}else{var buf=new Array(compressed.length/2);for(var i=0,TotalLen=buf.length;i<TotalLen;i++){buf[i]=compressed[i*2]*256+compressed[i*2+1]}var result=[];buf.forEach((function(c){result.push(f(c))}));return LZString.decompress(result.join(""))}},compressToEncodedURIComponent:function(input){if(input==null)return"";return LZString._compress(input,6,(function(a){return keyStrUriSafe.charAt(a)}))},decompressFromEncodedURIComponent:function(input){if(input==null)return"";if(input=="")return null;input=input.replace(/ /g,"+");return LZString._decompress(input.length,32,(function(index){return getBaseValue(keyStrUriSafe,input.charAt(index))}))},compress:function(uncompressed){return LZString._compress(uncompressed,16,(function(a){return f(a)}))},_compress:function(uncompressed,bitsPerChar,getCharFromInt){if(uncompressed==null)return"";var i,value,context_dictionary={},context_dictionaryToCreate={},context_c="",context_wc="",context_w="",context_enlargeIn=2,context_dictSize=3,context_numBits=2,context_data=[],context_data_val=0,context_data_position=0,ii;for(ii=0;ii<uncompressed.length;ii+=1){context_c=uncompressed.charAt(ii);if(!Object.prototype.hasOwnProperty.call(context_dictionary,context_c)){context_dictionary[context_c]=context_dictSize++;context_dictionaryToCreate[context_c]=true}context_wc=context_w+context_c;if(Object.prototype.hasOwnProperty.call(context_dictionary,context_wc)){context_w=context_wc}else{if(Object.prototype.hasOwnProperty.call(context_dictionaryToCreate,context_w)){if(context_w.charCodeAt(0)<256){for(i=0;i<context_numBits;i++){context_data_val=context_data_val<<1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}}value=context_w.charCodeAt(0);for(i=0;i<8;i++){context_data_val=context_data_val<<1|value&1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=value>>1}}else{value=1;for(i=0;i<context_numBits;i++){context_data_val=context_data_val<<1|value;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=0}value=context_w.charCodeAt(0);for(i=0;i<16;i++){context_data_val=context_data_val<<1|value&1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=value>>1}}context_enlargeIn--;if(context_enlargeIn==0){context_enlargeIn=Math.pow(2,context_numBits);context_numBits++}delete context_dictionaryToCreate[context_w]}else{value=context_dictionary[context_w];for(i=0;i<context_numBits;i++){context_data_val=context_data_val<<1|value&1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=value>>1}}context_enlargeIn--;if(context_enlargeIn==0){context_enlargeIn=Math.pow(2,context_numBits);context_numBits++}context_dictionary[context_wc]=context_dictSize++;context_w=String(context_c)}}if(context_w!==""){if(Object.prototype.hasOwnProperty.call(context_dictionaryToCreate,context_w)){if(context_w.charCodeAt(0)<256){for(i=0;i<context_numBits;i++){context_data_val=context_data_val<<1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}}value=context_w.charCodeAt(0);for(i=0;i<8;i++){context_data_val=context_data_val<<1|value&1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=value>>1}}else{value=1;for(i=0;i<context_numBits;i++){context_data_val=context_data_val<<1|value;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=0}value=context_w.charCodeAt(0);for(i=0;i<16;i++){context_data_val=context_data_val<<1|value&1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=value>>1}}context_enlargeIn--;if(context_enlargeIn==0){context_enlargeIn=Math.pow(2,context_numBits);context_numBits++}delete context_dictionaryToCreate[context_w]}else{value=context_dictionary[context_w];for(i=0;i<context_numBits;i++){context_data_val=context_data_val<<1|value&1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=value>>1}}context_enlargeIn--;if(context_enlargeIn==0){context_enlargeIn=Math.pow(2,context_numBits);context_numBits++}}value=2;for(i=0;i<context_numBits;i++){context_data_val=context_data_val<<1|value&1;if(context_data_position==bitsPerChar-1){context_data_position=0;context_data.push(getCharFromInt(context_data_val));context_data_val=0}else{context_data_position++}value=value>>1}while(true){context_data_val=context_data_val<<1;if(context_data_position==bitsPerChar-1){context_data.push(getCharFromInt(context_data_val));break}else context_data_position++}return context_data.join("")},decompress:function(compressed){if(compressed==null)return"";if(compressed=="")return null;return LZString._decompress(compressed.length,32768,(function(index){return compressed.charCodeAt(index)}))},_decompress:function(length,resetValue,getNextValue){var dictionary=[],enlargeIn=4,dictSize=4,numBits=3,entry="",result=[],i,w,bits,resb,maxpower,power,c,data={val:getNextValue(0),position:resetValue,index:1};for(i=0;i<3;i+=1){dictionary[i]=i}bits=0;maxpower=Math.pow(2,2);power=1;while(power!=maxpower){resb=data.val&data.position;data.position>>=1;if(data.position==0){data.position=resetValue;data.val=getNextValue(data.index++)}bits|=(resb>0?1:0)*power;power<<=1}switch(bits){case 0:bits=0;maxpower=Math.pow(2,8);power=1;while(power!=maxpower){resb=data.val&data.position;data.position>>=1;if(data.position==0){data.position=resetValue;data.val=getNextValue(data.index++)}bits|=(resb>0?1:0)*power;power<<=1}c=f(bits);break;case 1:bits=0;maxpower=Math.pow(2,16);power=1;while(power!=maxpower){resb=data.val&data.position;data.position>>=1;if(data.position==0){data.position=resetValue;data.val=getNextValue(data.index++)}bits|=(resb>0?1:0)*power;power<<=1}c=f(bits);break;case 2:return""}dictionary[3]=c;w=c;result.push(c);while(true){if(data.index>length){return""}bits=0;maxpower=Math.pow(2,numBits);power=1;while(power!=maxpower){resb=data.val&data.position;data.position>>=1;if(data.position==0){data.position=resetValue;data.val=getNextValue(data.index++)}bits|=(resb>0?1:0)*power;power<<=1}switch(c=bits){case 0:bits=0;maxpower=Math.pow(2,8);power=1;while(power!=maxpower){resb=data.val&data.position;data.position>>=1;if(data.position==0){data.position=resetValue;data.val=getNextValue(data.index++)}bits|=(resb>0?1:0)*power;power<<=1}dictionary[dictSize++]=f(bits);c=dictSize-1;enlargeIn--;break;case 1:bits=0;maxpower=Math.pow(2,16);power=1;while(power!=maxpower){resb=data.val&data.position;data.position>>=1;if(data.position==0){data.position=resetValue;data.val=getNextValue(data.index++)}bits|=(resb>0?1:0)*power;power<<=1}dictionary[dictSize++]=f(bits);c=dictSize-1;enlargeIn--;break;case 2:return result.join("")}if(enlargeIn==0){enlargeIn=Math.pow(2,numBits);numBits++}if(dictionary[c]){entry=dictionary[c]}else{if(c===dictSize){entry=w+w.charAt(0)}else{return null}}result.push(entry);dictionary[dictSize++]=w+entry.charAt(0);enlargeIn--;w=entry;if(enlargeIn==0){enlargeIn=Math.pow(2,numBits);numBits++}}}};return LZString}();if(module!=null){module.exports=LZString}else if(typeof angular!=="undefined"&&angular!=null){angular.module("LZString",[]).factory("LZString",(function(){return LZString}))}})(lzString);var LZString=lzString.exports;const ENCRYPTION_KEY=26282;function xorEncryptDecrypt(data){const result=new Uint8Array(data.length);for(let i=0;i<data.length;i++){result[i]=data[i]^ENCRYPTION_KEY>>i%16}return result}class TextStudioAnimation extends Animation2D{constructor(path,resourceID,scriptType){super(path,resourceID,scriptType);this.m_textComp=null;this.m_selectors=[];this.m_renderGroupSelectors=[];this.m_animParams=null;this.m_isLoaded=false;this.m_hasConstructedAnimators=false;this.m_reverseAniming=false}static getOrCreateTRSCaculator(textComp){if(this.m_TRSCaculator===null){this.m_TRSCaculator=new ExtraMatrixCaculator(textComp,[-1,-1])}return this.m_TRSCaculator}static clearTRSCaculator(){this.m_TRSCaculator=null}registerRichText(textComp){this.m_textComp=textComp}_checkAssestLoaded(){if(this.m_animParams!=null){return}}get loaded(){return this.m_isLoaded}loadAnimation(path,_entity){if(this.m_isLoaded)return true;let embedAnimConfig=false;for(const[key,value]of this.m_animationPropertys){if(key==="studio_anim_params"){this.m_animParams=JSON.parse(value);embedAnimConfig=true}}if(embedAnimConfig==false){const extraPath=path+"/config.json";const extraJsonStr=AmazFileUtils.readFileContent(extraPath);if(extraJsonStr!==undefined){const contentJson=JSON.parse(extraJsonStr);let encrypt=true;if("encrypt"in contentJson){encrypt=contentJson.encrypt}if("studio_animation_path"in contentJson){const studioAnimationPath=path+contentJson.studio_animation_path;if(encrypt){const buffer=fs.readFileSync(studioAnimationPath);if(buffer){const uint8Array=xorEncryptDecrypt(new Uint8Array(buffer));const decompressed=LZString.decompressFromUint8Array(uint8Array);if(decompressed){const studioContentJson=JSON.parse(decompressed);if("studio_anim_params"in studioContentJson){this.m_animParams=studioContentJson.studio_anim_params}if("reverseAniming"in studioContentJson){this.m_reverseAniming=studioContentJson.reverseAniming}}}else{console.error(TEMPLATE_TAG,"read encrypted file content error");return false}}else{const studioContentJsonStr=AmazFileUtils.readFileContent(studioAnimationPath);if(studioContentJsonStr!==undefined){const studioContentJson=JSON.parse(studioContentJsonStr);if("studio_anim_params"in studioContentJson){this.m_animParams=studioContentJson.studio_anim_params}if("reverseAniming"in studioContentJson){this.m_reverseAniming=studioContentJson.reverseAniming}}else{console.error(TEMPLATE_TAG,"read file content error");return false}}}}}if(this.m_animParams===null){console.error(TEMPLATE_TAG,"animParams is null in TextStudioAnimation");return false}this._checkAssestLoaded();this.m_isLoaded=true;return true}setAnimationProperty(animationPropertys){if(animationPropertys){for(const[key,value]of animationPropertys){this.m_animationPropertys.set(key,value);if(key==="studio_anim_params"){this._reset()}}this.m_isLoaded=false}}_constructTextAnimators(initValues){if(this.m_animParams===null||this.m_textComp===null)return false;this.m_selectors=[];const studioAnimParams=this.m_animParams;if("selectors"in studioAnimParams){for(const selector of studioAnimParams.selectors){const textSelector=new TextSelector(selector,initValues.letterList);this.m_selectors.push(textSelector)}}if(this.m_renderGroupSelectors.length>0){for(const renderGroupSelector of this.m_renderGroupSelectors){renderGroupSelector.destroy()}this.m_renderGroupSelectors=[]}if("effectAnimators"in studioAnimParams){for(let i=0;i<studioAnimParams.effectAnimators.length;i++){const renderGroupSelector=studioAnimParams.effectAnimators[i];const textRenderGroupSelector=new TextRenderGroupSelector(renderGroupSelector);textRenderGroupSelector.instantiateFromText(this.m_textComp,i,this.m_animationPath);this.m_renderGroupSelectors.push(textRenderGroupSelector)}}return true}onEnter(){if(!this.m_state.entered){this.m_state.entered=true}}onLeave(){if(this.m_state.entered){this._reset();this.m_state.entered=false}}_reset(){this.m_isLoaded=false;this.m_hasConstructedAnimators=false;this.m_selectors=[];let hasRenderGroup=false;for(const renderGroupSelector of this.m_renderGroupSelectors){renderGroupSelector.destroy();hasRenderGroup=true}if(hasRenderGroup&&this.m_textComp){this.m_textComp.clearRenderGroup()}this.m_renderGroupSelectors=[]}_updateSelectors(timeInSegment,initValues){if(this.m_selectors.length>0&&this.m_textComp&&initValues){const duration=this.animationType===AnimationType.loop?this.animationLoopDuration:this.animationDuration;const relativeTime=timeInSegment/duration*TextSelector.STUDIO_TOTAL_TIME;const lettersAnimingMap={};for(const selector of this.m_selectors){selector.checkInitValueByAttrs(relativeTime,lettersAnimingMap,initValues)}for(const selector of this.m_selectors){selector.updateAnimingData(relativeTime,lettersAnimingMap)}const trsCaculator=TextStudioAnimation.getOrCreateTRSCaculator(this.m_textComp);for(const selector of this.m_selectors){trsCaculator.reset();selector.handleExtraMatrix(lettersAnimingMap,trsCaculator,initValues)}const inJsonStr=JSON.stringify({letters:lettersAnimingMap});AmazUtils$1.swingTemplateUtils.textStudioAnimSetParamsBatch(this.m_textComp,inJsonStr)}}_updateRenderGroupSelectors(timeInSegment){if(this.m_textComp===null)return;const duration=this.animationType===AnimationType.loop?this.animationLoopDuration:this.animationDuration;const relativeTime=timeInSegment/duration*TextSelector.STUDIO_TOTAL_TIME;for(const renderGroupSelector of this.m_renderGroupSelectors){renderGroupSelector.animing(relativeTime)}}seek(timeInWidget,initValues){if(this.m_textComp===null||initValues===null)return;if(this.m_isLoaded&&this.m_hasConstructedAnimators===false){if(this._constructTextAnimators(initValues)===false){return}this.m_hasConstructedAnimators=true}let seekTime=timeInWidget-this.animationStartTimeInWidget;if(this.m_animationType===AnimationType.loop){seekTime=seekTime%this.animationLoopDuration}if(this.m_reverseAniming){seekTime=this.animationDuration-seekTime}this._updateSelectors(seekTime,initValues);this._updateRenderGroupSelectors(seekTime)}onClear(){if(this.m_state.started){this.m_state.started=false;this.m_state.entered=false;this._reset()}}unloadAnmation(_entity){this.onClear()}reloadAnimation(_entity){this.onClear()}checkStateStarted(seekInAnim,_scene){if(seekInAnim&&this.m_state.started===false){this.m_state.started=true}}}TextStudioAnimation.m_TRSCaculator=null;class AnimationFactory{static createAnimation(path,resourceID,scriptType){switch(scriptType){case"lua":return new Animation2D(path,resourceID,scriptType);case"js":return new TextStudioAnimation(path,resourceID,scriptType);default:console.error(`Unknown script type in animation: '${scriptType}'`);return null}}}var Amaz$4=effect.Amaz;var Rect$1=Amaz$4.Rect;var Vec2$1=Amaz$4.Vector2f;var Vec3$1=Amaz$4.Vector3f;class Widget2D extends Widget{constructor(name,widgetType,scene){super(name,widgetType,scene);this.m_anchor=new Vec2$1(0,0);this.m_layoutParams={};this.m_layoutUpdateFirst=true;this.m_bindingBox=new Rect$1(0,0,0,0);this.m_animations=null;this.m_animationsDirty=false;this.m_scriptPassthroughParamsDirty=false;this.m_renderEntity=null;this.m_widgetOriginalPixelSize=new Vec2$1(0,0);this.m_widgetOriginalSize=new Vec2$1(0,0);this.m_normalizedSize=new Vec2$1(0,0);this.m_originalPixelSizeDirty=false;this.m_scriptPassthroughParams=new Map}set anchor(anchor){this.m_anchor=anchor}get anchor(){return this.m_anchor}get bindingBox(){return this.m_bindingBox}set layoutUpdateFirst(isFirst){this.m_layoutUpdateFirst=isFirst}get layoutUpdateFirst(){return this.m_layoutUpdateFirst}set layoutParams(layout){this.m_layoutParams=layout}get layoutParams(){return this.m_layoutParams}set originalPixelSize(size){if(!this.m_widgetOriginalPixelSize.eq(size)){this.m_widgetOriginalPixelSize=size;this.m_originalPixelSizeDirty=true}}get originalPixelSize(){return this.m_widgetOriginalPixelSize}set originalSize(size){if(!this.m_widgetOriginalSize.eq(size)){this.m_widgetOriginalSize=size}}get originalSize(){return this.m_widgetOriginalSize}getTextureNormalizedScale(){const normalizedScale=new Vec2$1(0,0);if(this.m_normalizedSize.x<0||this.m_normalizedSize.y<0||this.m_widgetOriginalSize.x<=0||this.m_widgetOriginalSize.y<=0){return normalizedScale}normalizedScale.x=this.m_normalizedSize.x/this.m_widgetOriginalSize.x;normalizedScale.y=this.m_normalizedSize.y/this.m_widgetOriginalSize.y;return normalizedScale}updateOriginalSize(size,screenSize){this.m_widgetOriginalPixelSize=size;this.m_widgetOriginalSize.x=size.x/screenSize.x*2;this.m_widgetOriginalSize.y=size.y/screenSize.y*2}updateOriginalPixelSize(size){if(!this.m_widgetOriginalPixelSize.eq(size)){this.m_widgetOriginalPixelSize=size;this.m_originalPixelSizeDirty=true}}setAnimationParameters(animation_arr){if(animation_arr&&animation_arr instanceof Array){animation_arr.forEach((value=>{if(null!=value){const animParam=TemplateConfigParser.parseAnimationConfig(value);if((animParam===null||animParam===void 0?void 0:animParam.anim_type)==null)return;if((animParam===null||animParam===void 0?void 0:animParam.anim_type)=="caption"){animParam.anim_type="loop"}const animation_type=AnimationType[animParam===null||animParam===void 0?void 0:animParam.anim_type];if(animation_type!=null){this.setWidgetAnimation(animParam.anim_resource_path,animParam.anim_resource_id,animParam.anim_script_type,animParam.anim_start_time,animParam.duration,animParam.loop_duration,animation_type);this.m_animationsDirty=true}else{console.error(TEMPLATE_TAG,"widget setAnimationParameters error, has no anim_type in json")}}}))}}setAnimationProperties(animation_property_arr){if(animation_property_arr&&animation_property_arr instanceof Array){animation_property_arr.forEach((value=>{var _a,_b;if(null!=value){if("anim_type"in value){const animation_type=AnimationType[value.anim_type];if(animation_type!=null){if((_a=this.m_animations)===null||_a===void 0?void 0:_a.has(animation_type)){const anim=(_b=this.m_animations)===null||_b===void 0?void 0:_b.get(animation_type);if(anim){if("propert_key"in value&&"propert_value"in value){const propertKey=value.propert_key;const propertValue=value.propert_value;anim.setAnimationProperty(new Map([[propertKey,propertValue]]))}}else{console.error(TEMPLATE_TAG,"widget setAnimationProperties error,anim_type is null")}}}else{console.error(TEMPLATE_TAG,"widget setAnimationProperties error, has no anim_type in json")}}}}))}}setParameters(jsonParam){if(jsonParam){super.parameters=jsonParam;if("layout_params"in jsonParam){const layoutParamConfig=jsonParam.layout_params;this.layoutParams=layoutParamConfig}if("original_size"in jsonParam){const widgetPixelSize=AmazUtils$1.CastJsonArray2fToAmazVector2f(jsonParam.original_size);if(widgetPixelSize){if(Widget.m_isPostLayoutEnable){if(widgetPixelSize.x>.001&&widgetPixelSize.y>.001){this.originalPixelSize=widgetPixelSize}}else{this.originalPixelSize=widgetPixelSize}}else{console.error(TEMPLATE_TAG,"original_size in json is not vector2f:",JSON.stringify(jsonParam.original_size))}}if("anims"in jsonParam){const animation_arr=jsonParam.anims;this.setAnimationParameters(animation_arr)}if("anims_properties"in jsonParam){const animation_property_arr=jsonParam.anims_properties;this.setAnimationProperties(animation_property_arr)}}}getParameters(){const widget2DAnims=[];if(this.m_animations){this.m_animations.forEach(((anima,key)=>{if(anima){const anima_typestr=AnimationType[key];const animaScriptType=anima.animationScriptType===ScriptType.LUA?"lua":"js";const animation={anim_type:anima_typestr,anim_script_type:animaScriptType,anim_resource_id:anima.animationResourceID,anim_resource_path:anima.animationPath,anim_start_time:anima.animationStartTimeInWidget,duration:anima.animationDuration,loop_duration:anima.m_animationLoopDuration};widget2DAnims.push(animation)}}))}const widget2DParam={original_size:[this.originalPixelSize.x,this.originalPixelSize.y],layout_params:this.layoutParams,anims:widget2DAnims};return widget2DParam}setWidgetAnimation(path,resourceID,scriptType,startTimeInWidget,duration,loopDuration,animType){var _a,_b,_c;if(((_a=this.m_animations)===null||_a===void 0?void 0:_a.has(animType))&&((_c=(_b=this.m_animations)===null||_b===void 0?void 0:_b.get(animType))===null||_c===void 0?void 0:_c.animationPath)===path){this.updateWidgetAnimation(startTimeInWidget,duration,loopDuration,animType)}else{this.resetAnimation(animType);if(path!=null&&path!=""){this.addWidgetAnimation(path,resourceID,scriptType,startTimeInWidget,duration,loopDuration,animType)}}this.m_animationsDirty=true;return true}updateWidgetAnimation(startTimeInWidget,duration,loopDuration,animType){var _a;const anim=(_a=this.m_animations)===null||_a===void 0?void 0:_a.get(animType);if(anim){if(-1!==startTimeInWidget){anim.animationStartTimeInWidget=startTimeInWidget}anim.animationDuration=Math.max(duration,0);anim.animationEndTimeInWidget=anim.animationStartTimeInWidget+anim.animationDuration;anim.animationLoopDuration=Math.max(loopDuration,0)}}addWidgetAnimation(path,resourceID,scriptType,startTimeInWidget,duration,loopDuration,animType){const studioPath=path+"/studioAnim.lsanim";const isStudioAnim=AmazUtils$1.swingTemplateUtils.isFileExist(studioPath);if(isStudioAnim){scriptType="js"}const anim=AnimationFactory.createAnimation(path,resourceID,scriptType);if(anim===undefined){return}anim.animationStartTimeInWidget=Math.max(startTimeInWidget,0);anim.animationEndTimeInWidget=startTimeInWidget+Math.max(duration,0);anim.animationDuration=Math.max(duration,0);anim.animationLoopDuration=Math.max(loopDuration,0);anim.animationType=animType;if(null!=this.m_animations){this.m_animations.set(anim.animationType,anim)}else{this.m_animations=new Map;this.m_animations.set(anim.animationType,anim)}if(animType==AnimationType.loop&&anim.animationScriptType==ScriptType.LUA){const extraPath=anim.animationPath+"/extra.json";const extraJsonStr=AmazFileUtils.readFileContent(extraPath);if(extraJsonStr!==undefined){const contentJson=JSON.parse(extraJsonStr);if(contentJson&&"caption_setting"in contentJson){const captionSetting=contentJson.caption_setting;if(captionSetting&&"merge_key_style"in captionSetting){const needMerge=captionSetting.merge_key_style;anim.m_needMerge=needMerge;return}}}anim.m_needMerge=true}return true}resetAnimation(animType){if(null!=this.m_animations&&this.m_animations.has(animType)){const anim=this.m_animations.get(animType);if(anim!==undefined){if(this.m_renderEntity){anim.unloadAnmation(this.m_renderEntity);this.m_animations.delete(anim.animationType)}}}}reloadAllAnimation(){var _a;(_a=this.m_animations)===null||_a===void 0?void 0:_a.forEach((anim=>{if(anim!==undefined){if(this.m_renderEntity){anim.reloadAnimation(this.m_renderEntity);this._updatePassthroughParams(anim)}}}))}resetAllAnimation(){var _a;(_a=this.m_animations)===null||_a===void 0?void 0:_a.forEach((anim=>{if(anim!==undefined){if(this.m_renderEntity){anim.unloadAnmation(this.m_renderEntity)}}}))}onLeaveAllAnimation(){var _a;(_a=this.m_animations)===null||_a===void 0?void 0:_a.forEach((anim=>{if(anim!==undefined){anim.onLeave()}}))}onUpdate(timeInTrack){super.onUpdate(timeInTrack)}_setPassthroughParams(scriptComponent){this.m_scriptPassthroughParams.forEach(((value,key)=>{scriptComponent.properties.set(key,value)}));scriptComponent.properties.set("enable_infosticker_new_text_component",true)}_updatePassthroughParams(anim){if(null!==anim.m_scriptComponent){this._setPassthroughParams(anim.m_scriptComponent)}}seekAnimations(timeInTrack){const isInRange=this.compareFloatRange;let animTimeInWidget=timeInTrack-this.startTimeInTrack;if(animTimeInWidget<0)animTimeInWidget=0;if(!this.m_animations||!this.renderEntity){return}for(const[anim_type,anim]of this.m_animations){const inAnim=isInRange(anim.animationStartTimeInWidget,anim.animationEndTimeInWidget,animTimeInWidget,true);if(anim&&anim_type&&!anim.loaded&&inAnim){anim.loadAnimation(anim.animationPath,this.renderEntity);anim.setAnimationProperty()}if(this.m_scriptPassthroughParamsDirty===true&&anim){this._updatePassthroughParams(anim)}anim.checkStateStarted(inAnim,this.m_scene);if(!inAnim&&anim.state.entered===true){anim.onLeave()}if(inAnim){if(anim.state.entered===false){anim.onEnter();anim.state.entered=true}anim.seek(animTimeInWidget)}}this.m_scriptPassthroughParamsDirty=false}static createRenderEntity(scene,rootEntity,name,layer){const renderEntityName=name;const renderEntity=AmazUtils$1.createEntity(renderEntityName,scene);renderEntity.layer=layer;const localPos=new Vec3$1(0,0,0);const scale=new Vec3$1(1,1,1);const rotate=new Vec3$1(0,0,0);renderEntity.transform={position:localPos,scale:scale,rotation:rotate};if(rootEntity)AmazUtils$1.addChildEntity(rootEntity,renderEntity);return renderEntity}static removeRenderEntity(rootEntity,renderEntity){if(!renderEntity)return false;const root=rootEntity;if(root)AmazUtils$1.removeChildEntity(root,renderEntity);return true}get renderEntity(){if(!this.m_renderEntity){this.m_renderEntity=Widget2D.createRenderEntity(this.m_scene,this.m_rootEntity,this.m_name+"renderEntity",this.m_cameraLayer)}return this.m_renderEntity}set renderEntity(entity){if(this.m_renderEntity){Widget2D.removeRenderEntity(this.m_rootEntity,this.m_renderEntity);this.m_renderEntity.scene.removeEntity(this.m_renderEntity);this.m_renderEntity=null}this.m_renderEntity=entity}onUpdateAnimationDuration(originTime,newTime){var _a;if(this.m_animations==null)return;const animTimeRangesInWidget=new Map(Array.from(this.m_animations).map((val=>{const ret=[val[0],new TimeRange(val[1].animationStartTimeInWidget,val[1].animationDuration)];return ret})));(_a=this.calculateAnimDuration(originTime,newTime,animTimeRangesInWidget))===null||_a===void 0?void 0:_a.forEach(((animTimeInWidget,type)=>{if(this.m_animations!=null&&this.m_animations.has(type)){this.m_animations.get(type).animationStartTimeInWidget=animTimeInWidget.startTime;this.m_animations.get(type).animationEndTimeInWidget=animTimeInWidget.endTime}}))}calculateAnimDuration(originTimeRange,newTimeRange,animTimeRangesInWidget){const animDuration=Array.from(animTimeRangesInWidget).reduce(((prev,val)=>prev+val[1].duration),0);if(animDuration<originTimeRange.duration&&animDuration<newTimeRange.duration&&originTimeRange.duration!==0){const durationScale=newTimeRange.duration/originTimeRange.duration;return new Map(Array.from(animTimeRangesInWidget).map((val=>{const ret=[val[0],new TimeRange(val[1].startTime*durationScale,val[1].duration*durationScale)];return ret})))}return undefined}hasAnimation(){return this.m_animations!=null&&this.m_animations.size>0}hasAnimationWithType(type){if(this.m_animations!=null&&this.m_animations.size>0){return this.m_animations.has(type)}return false}updateLayoutInfo(){super.updateLayoutInfo();this.layoutInfo.m_widgetOriginalPixelSize=this.originalPixelSize.copy()}checkLayoutDirty(){if(Widget.m_isPostLayoutEnable==false){return}const oriLayoutInfo=this.layoutInfo.copy();this.updateLayoutInfo();if(this.layoutUpdateFirst){this.layoutParams.dirty=true;this.layoutUpdateFirst=false}else if(!oriLayoutInfo.isEqualTo(this.layoutInfo)){this.layoutParams.dirty=true}}}var Amaz$3=effect.Amaz;class TextAttributeSetter{static setRootScaleX(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="sx"){widget.scale=new Amaz$3.Vector3f(value,widget.scale.y,widget.scale.z)}}static setRootScaleY(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="sy"){widget.scale=new Amaz$3.Vector3f(widget.scale.x,value,widget.scale.z)}}static setRootRotationZ(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="rz"){widget.rotation=new Amaz$3.Vector3f(widget.rotation.x,widget.rotation.y,value)}}static setRootPosX(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="px"){widget.position=new Amaz$3.Vector3f(value,widget.position.y,widget.position.z)}}static setRootPosY(obj){const widget=obj.motionContext.widget;const value=obj.v;const propertyName=obj.propertyName;if(propertyName==="py"){widget.position=new Amaz$3.Vector3f(widget.position.x,value,widget.position.z)}}static getValueCallback(propertyName){return function(obj){const value=obj.v;const motionContext=obj.motionContext;motionContext.callbackParams.values.push({name:propertyName,value:value,overlayMode:motionContext.overlayMode})}}}TextAttributeSetter.colorNames=["tc","bc","sc","oc","tbc"];TextAttributeSetter.setterMap={px:TextAttributeSetter.setRootPosX,py:TextAttributeSetter.setRootPosY,sx:TextAttributeSetter.setRootScaleX,sy:TextAttributeSetter.setRootScaleY,rz:TextAttributeSetter.setRootRotationZ};class TextValueGeneratorContext{constructor(widget,textComp,overlayMode,callbackParams){this.widget=null;this.textComp=null;this.callbackParams=null;this.widget=widget;this.textComp=textComp;this.overlayMode=overlayMode;this.callbackParams=callbackParams}}class TextAnimationController extends TextBaseAnimationController{constructor(widget,textComp){super();this.m_host=null;this.m_textComp=null;this.m_callbackParams={};this.m_text_trs_motion=new Group;this.m_text_pre_motion=new Group;this.m_text_style_motion=new Group;this.m_host=widget;this.m_textComp=textComp}setKeyFrameParams(keyframeParams){super.setKeyFrameParams(keyframeParams)}updateKeyFrames(){if(!this.checkIsDirty()){return}this.m_keyFramesDirty=false;this.m_text_trs_motion.removeAll();this.m_text_pre_motion.removeAll();this.m_text_style_motion.removeAll();this.m_callbackParams={};if(this.m_keyframeParams==null){return}const keyframes=this.m_keyframeParams.text_keyframes;if(keyframes==null){return}for(let i=0;i<keyframes.length;i++){const keyframe=keyframes[i];let overlayMode="over";if(KeyframeAttrUtils.isValidOverlayMode(keyframe.overlay_mode))overlayMode=keyframe.overlay_mode;if(keyframe.k==null){console.error(TEMPLATE_TAG,"keyframe.k is undefined");return}const startTimeInWidget=keyframe.s*KEYFRAME_TIME_FACTOR;const endTimeInWidget=keyframe.e*KEYFRAME_TIME_FACTOR;if(endTimeInWidget-startTimeInWidget<TIME_EPS){console.error(TEMPLATE_TAG,"keyframe time range too short");return}this.setTimeRange(startTimeInWidget,endTimeInWidget);const motionContext=new TextValueGeneratorContext(this.m_host,this.m_textComp,overlayMode,this.m_callbackParams);motionContext.callbackParams=this.m_callbackParams;for(const key in keyframe.k){if(keyframe.k[key]!=null){this.createMotionsForOneProperty(keyframe,motionContext,key)}}}this._sortKeyframes()}_sortKeyframes(){const groups=[this.m_text_trs_motion,this.m_text_pre_motion,this.m_text_style_motion];for(let i=0;i<groups.length;i++){const group=groups[i];KeyframeAttrUtils.sortKeyframesByAttributeSetter(group,TextAttributeSetter)}}createMotionsForOneProperty(keyframeJson,motionContext,propertyName){const isPreGroup=propertyName=>{if(propertyName==="s"){return true}return false};const prop=keyframeJson.k[propertyName];if(prop==undefined){console.error(TEMPLATE_TAG,"prop is undefined");return}if(prop.length==0){console.error(TEMPLATE_TAG,"prop is empty");return}const interp=Interpolation.Linear;let group=this.m_text_trs_motion;const keyframes=KeyframeAttrUtils.createKeyframes(keyframeJson,propertyName,TextAttributeSetter.colorNames);if(propertyName in TextAttributeSetter.setterMap){const trsCallback=TextAttributeSetter.setterMap[propertyName];KeyframeAttrUtils.createOneMotionByRelativeTime(keyframes,trsCallback,interp,motionContext,group,propertyName,this.m_startTimeInWidget,this.m_endTimeInWidget,keyframeJson.s,keyframeJson.e).start()}else{group=isPreGroup(propertyName)?this.m_text_pre_motion:this.m_text_style_motion;const styleCallback=TextAttributeSetter.getValueCallback(propertyName);KeyframeAttrUtils.createOneMotionByRelativeTime(keyframes,styleCallback,interp,motionContext,group,propertyName,this.m_startTimeInWidget,this.m_endTimeInWidget,keyframeJson.s,keyframeJson.e).start()}}update(timeInWidget){var _a;if(this.m_keyframeParams!=null){this.m_callbackParams.values=[];this.m_text_trs_motion.update(timeInWidget,true);this.m_text_pre_motion.update(timeInWidget,true);if(this.m_callbackParams.values.length>0){const jsonStr=JSON.stringify(this.m_callbackParams);AmazUtils$1.swingTemplateUtils.textKeyframeCallback(this.m_textComp,jsonStr);this.m_callbackParams.values=[]}(_a=this.m_textComp)===null||_a===void 0?void 0:_a.forceTypeSetting();this.m_text_style_motion.update(timeInWidget,true);if(this.m_callbackParams.values.length>0){const jsonStr=JSON.stringify(this.m_callbackParams);AmazUtils$1.swingTemplateUtils.textKeyframeCallback(this.m_textComp,jsonStr);this.m_callbackParams.values=[]}}return timeInWidget}}const SDK_VERSION="13.1.0";const TEXT_LOCALE_VER="13.3.0";const TEXT_BLOOM_VER="13.6.0";const TEXT_CANVAS_WH_FIXED_VER="14.2.0";const TEXT_PATH_TYPESETTING_VER="14.9.0";const TEXT_DIRECTION_LEVEL_VER="15.7.0";const TEXT_BACK_GROUNDS_VER="16.2.0";const TEXT_SHADOW_DIFFUSE_VER="16.3.0";class VersionUtils{static version2Str(version){const majorStr=String(version.major);const minorStr=String(version.minor);const revisionStr=String(version.revision);return majorStr+"."+minorStr+"."+revisionStr}static str2Version(str){const version={major:0,minor:0,revision:0};const vers=str.split(".",3);if(vers.length<3){return version}version.major=Number(vers[0]),version.minor=Number(vers[1]),version.revision=Number(vers[2]);return version}static compareVersion(version1,version2){const v1=[version1.major,version1.minor,version1.revision];const v2=[version2.major,version2.minor,version2.revision];for(let i=0;i<v1.length;i++){if(v1[i]>v2[i]){return 1}else if(v1[i]<v2[i]){return-1}}return 0}static versionMax(version1,version2){if(this.compareVersion(version1,version2)>=0){return version1}else{return version2}}static versionMin(version1,version2){if(this.compareVersion(version1,version2)<=0){return version1}else{return version2}}}class WordAssociationParams{constructor(){this.m_paramsJson=null;this.m_oriWordInfoStr="";this.m_curWordInfoStr="";this.m_oriWordInfo=null;this.m_curWordInfo=null;this.m_dirty=false;this.m_associationType="";this.m_associationName=""}get oriWordInfoStr(){return this.m_oriWordInfoStr}set oriWordInfoStr(wordInfoStr){this.m_oriWordInfoStr=wordInfoStr}get curWordInfoStr(){return this.m_curWordInfoStr}set curWordInfoStr(wordInfoStr){this.m_curWordInfoStr=wordInfoStr}get oriWordInfo(){return this.m_oriWordInfo}set oriWordInfo(wordInfo){this.m_oriWordInfo=wordInfo}get curWordInfo(){return this.m_curWordInfo}set curWordInfo(wordInfo){this.m_curWordInfo=wordInfo}get dirty(){return this.m_dirty}set dirty(dirty){this.m_dirty=dirty}get associationType(){return this.m_associationType}set associationType(associationType){this.m_associationType=associationType}get associationName(){return this.m_associationName}set associationName(associationName){this.m_associationName=associationName}set paramsJson(paramsJson){this.m_paramsJson=paramsJson}get paramsJson(){return this.m_paramsJson}}var Amaz$2=effect.Amaz;class TextAssociationController extends BaseAssociationController{constructor(){super(...arguments);this.m_dirty=false;this.m_startTimeInTrack=0;this.m_wordIdRange=[];this.m_wordIdRangeDirty=false;this.m_wordInfoDirty=false;this.m_reuseStyleStr="";this.m_needUpdateCaption=false;this.m_isStrChanged=false;this.m_wordAssociationParams=new WordAssociationParams;this.m_setTextAssoWithWordInfo=false;this.m_stickerAssoControllers=new Set;this.m_stickerAssoTimeScopeDirty=false;this.m_stickerAssoLayoutDirty=false}set oriWordInfo(wordInfo){this.m_wordAssociationParams.oriWordInfo=wordInfo}get oriWordInfo(){return this.m_wordAssociationParams.oriWordInfo}set curWordInfo(wordInfo){this.m_wordAssociationParams.curWordInfo=wordInfo}get curWordInfo(){return this.m_wordAssociationParams.curWordInfo}set dirty(dirty){this.m_dirty=dirty}get dirty(){return this.m_dirty}set startTimeInTrack(startTimeInTrack){this.m_startTimeInTrack=startTimeInTrack}get startTimeInTrack(){return this.m_startTimeInTrack}set wordIdRange(wordIdRange){this.m_wordIdRange=wordIdRange}get wordIdRange(){return this.m_wordIdRange}set wordIdRangeDirty(wordIdRangeDirty){this.m_wordIdRangeDirty=wordIdRangeDirty}get wordIdRangeDirty(){return this.m_wordIdRangeDirty}set wordInfoDirty(wordInfoDirty){this.m_wordInfoDirty=wordInfoDirty}get wordInfoDirty(){return this.m_wordInfoDirty}get needUpdateCaption(){return this.m_needUpdateCaption}set isStrChanged(isStrChanged){this.m_isStrChanged=isStrChanged}get isStrChanged(){return this.m_isStrChanged}getTextWidget(){if(this.widget&&this.widget.widgetType===WidgetType.TEXT){return this.widget}return null}setAssociationParams(params){super.associationParams=params;if("word_id_range"in super.associationParams){this.updateAssociationParams()}else if("word_info"in super.associationParams){this.updateAssociationParamsWithWordInfo()}}addStickerAssoController(controller){this.m_stickerAssoControllers.add(controller)}notifyStickerAssoTimeScopeChanged(){if(this.m_stickerAssoTimeScopeDirty===false){return}this.m_stickerAssoTimeScopeDirty=false;this.m_stickerAssoControllers.forEach((controller=>{controller.timeScopeDirty=true}))}notifyStickerAssoLayoutChanged(){if(this.m_stickerAssoLayoutDirty===false){return}this.m_stickerAssoLayoutDirty=false;this.m_stickerAssoControllers.forEach((controller=>{controller.layoutDirty=true}))}set stickerAssoTimeScopeDirty(dirty){this.m_stickerAssoTimeScopeDirty=dirty}get stickerAssoTimeScopeDirty(){return this.m_stickerAssoTimeScopeDirty}set stickerAssoLayoutDirty(dirty){this.m_stickerAssoLayoutDirty=dirty}get stickerAssoLayoutDirty(){return this.m_stickerAssoLayoutDirty}get wordAssociationParams(){return this.m_wordAssociationParams}get isSetTextAssoWithWordInfo(){return this.m_setTextAssoWithWordInfo}updateAssociationParamsWithWordInfo(){if("word_id_range"in super.associationParams){return}if("word_info"in super.associationParams){const oriWordInfoParam=super.associationParams.word_info;if(oriWordInfoParam==null){return}const oriWordInfoStr=JSON.stringify(oriWordInfoParam);if(oriWordInfoStr!=this.m_wordAssociationParams.oriWordInfoStr){this.m_wordAssociationParams.oriWordInfoStr=oriWordInfoStr;this.m_wordAssociationParams.curWordInfoStr=oriWordInfoStr;if(!this.m_wordAssociationParams.oriWordInfo){this.m_wordAssociationParams.oriWordInfo=new Amaz$2.TextWordInfo}if(!this.m_wordAssociationParams.curWordInfo){this.m_wordAssociationParams.curWordInfo=new Amaz$2.TextWordInfo}AmazUtils$1.TextWordMatchUtils.parseWordInfoByJsonStr(this.m_wordAssociationParams.oriWordInfo,oriWordInfoStr);this.m_wordAssociationParams.curWordInfo.initWith(this.m_wordAssociationParams.oriWordInfo);this.m_setTextAssoWithWordInfo=true;this.m_dirty=true}}if("current_word_info"in super.associationParams){const curWordInfoParam=super.associationParams.current_word_info;if(curWordInfoParam==null){return}if(!this.m_wordAssociationParams.curWordInfo){this.m_wordAssociationParams.curWordInfo=new Amaz$2.TextWordInfo}const curWordInfoStr=JSON.stringify(curWordInfoParam);if(curWordInfoStr!=this.m_wordAssociationParams.curWordInfoStr){this.m_wordAssociationParams.curWordInfoStr=curWordInfoStr;AmazUtils$1.TextWordMatchUtils.parseWordInfoByJsonStr(this.m_wordAssociationParams.curWordInfo,curWordInfoStr);this.m_dirty=true}}const textWidget=this.getTextWidget();if(textWidget&&textWidget.textComp){textWidget.textComp.oriTextWordInfo=this.oriWordInfo;textWidget.textComp.curTextWordInfo=this.curWordInfo}this.m_associationParamsDirty=false}updateAssociationParams(){var _a,_b;const text=this.getTextWidget();if(text&&this.startTimeInTrack!=text.startTimeInTrack){this.startTimeInTrack=text.startTimeInTrack;if(this.widget&&((_a=this.widget.rootWidget)===null||_a===void 0?void 0:_a.rootAssociationController)){this.m_wordIdRangeDirty=true}}if("word_id_range"in super.associationParams){const wordIdRange=super.associationParams.word_id_range;let isEqual=true;if(this.m_wordIdRange.length!=wordIdRange.length){isEqual=false}else{for(let i=0;i<this.m_wordIdRange.length;i++){if(this.m_wordIdRange[i]!=wordIdRange[i]){isEqual=false;break}}}if(!isEqual){this.m_wordIdRange.length=0;if(wordIdRange.length<2){this.oriWordInfo=null;this.curWordInfo=null;const textWidget=this.getTextWidget();if(textWidget&&textWidget.textComp){textWidget.textComp.oriTextWordInfo=null;textWidget.textComp.curTextWordInfo=null}this.m_wordIdRangeDirty=false}else{this.m_wordIdRange.push(wordIdRange[0]);this.m_wordIdRange.push(wordIdRange[1]);this.m_wordIdRangeDirty=true}}}if(((_b=this.widget)===null||_b===void 0?void 0:_b.rootWidget)!=null&&(this.m_wordIdRangeDirty||this.m_dirty)&&this.m_wordIdRange.length>=2){const rootController=this.widget.rootWidget.rootAssociationController;if(rootController&&rootController&&rootController.oriWordInfo&&rootController.curWordInfo){if(!this.oriWordInfo){this.oriWordInfo=new Amaz$2.TextWordInfo}if(!this.curWordInfo){this.curWordInfo=new Amaz$2.TextWordInfo}const wordIdRangeVec=new Amaz$2.Vector;wordIdRangeVec.pushBack(this.wordIdRange[0]);wordIdRangeVec.pushBack(this.wordIdRange[1]);let msTimeOffset=(this.m_startTimeInTrack-this.widget.rootWidget.startTimeInTrack)*-1e3;msTimeOffset=msTimeOffset-.5|0;this.oriWordInfo.initWith(rootController.oriWordInfo,wordIdRangeVec,msTimeOffset);this.curWordInfo.initWith(rootController.curWordInfo,wordIdRangeVec,msTimeOffset);this.oriWordInfo.dirtyIndex.clear();this.curWordInfo.dirtyIndex.clear();this.curWordInfo.isUpdateEnable=true;const textWidget=this.getTextWidget();if(textWidget&&textWidget.textComp){textWidget.textComp.oriTextWordInfo=this.oriWordInfo;textWidget.textComp.curTextWordInfo=this.curWordInfo}}}this.m_associationParamsDirty=false}updateByAssociation(){var _a;if(((_a=this.widget)===null||_a===void 0?void 0:_a.rootWidget)!=null&&(this.m_wordIdRange.length>=2||this.curWordInfo)){this._judgeNeedUpdateCaption();const textWidget=this.getTextWidget();if(this.curWordInfo&&(this.m_wordIdRangeDirty||this.m_dirty)){const text=this.curWordInfo.text;if(textWidget&&textWidget.textComp){if(textWidget.textComp.isLetterDirty()){textWidget.textComp.updateLetters()}let templateStyle=textWidget.textComp.getRichStr(0,0,0,0,true);if(text.length==0&&templateStyle.length!=0){this.m_reuseStyleStr=templateStyle}if(text.length!=0&&templateStyle.length==0){templateStyle=this.m_reuseStyleStr}const jsonParam5={text:text,templateStyle:templateStyle};const textOP={m_iOpCode:69,m_fParam1:1,m_fParam2:0,m_fParam3:0,m_fParam4:0,m_sParam:JSON.stringify(jsonParam5)};textWidget.setRichTextByOPCode(textOP);textWidget.captionDurationInfoDirty=true;textWidget.captionParamsDirty=true}this.updateCaptionDurationInfo();this.m_dirty=false}const rootControl=this.widget.rootWidget.rootAssociationController;if(textWidget&&textWidget.textComp){const text=textWidget.textComp.getPlainStr(0,0,0);if(this.oriWordInfo&&this.curWordInfo&&this.isStrChanged){this.isStrChanged=false;AmazUtils$1.TextWordMatchUtils.matchWordInfoByText(this.oriWordInfo,this.curWordInfo,text);this.curWordInfo.dirtyIndex.clear();this.updateCaptionDurationInfo();if(rootControl&&!rootControl.dirty&&this.m_wordIdRange.length>0||!rootControl){this.wordInfoDirty=true}}if(this.curWordInfo&&this.curWordInfo.dirtyIndex.size()>0){this.curWordInfo.dirtyIndex.clear();this.updateCaptionDurationInfo();if(rootControl&&!rootControl.dirty&&this.m_wordIdRange.length>0||!rootControl){this.wordInfoDirty=true}}}}}_judgeNeedUpdateCaption(){var _a;const textWidget=this.getTextWidget();if(!textWidget||!textWidget.textComp||!this.curWordInfo){return false}let hasCapital=false;const captionControl=textWidget.captionAnimationController;if(captionControl!=null){const capital=captionControl.captionInfo.capitalStr;hasCapital=capital.length>0&&capital!="none"}let hasKeyword=false;if(AmazUtils$1.TextWordMatchUtils.isWordInfoHasKeyword(this.curWordInfo)){hasKeyword=true}this.m_needUpdateCaption=textWidget.hasAnimationWithType(AnimationType.loop)||hasCapital||hasKeyword||((_a=captionControl===null||captionControl===void 0?void 0:captionControl.doesAnimEffectExist())!==null&&_a!==void 0?_a:false);return this.m_needUpdateCaption}updateCaptionDurationInfo(){const textWidget=this.getTextWidget();if(!textWidget||!this.curWordInfo){return}if(this.needUpdateCaption){const captionDurationInfoStr=AmazUtils$1.TextWordMatchUtils.generateCaptionDurationInfo(this.curWordInfo);const captionDurationInfo=JSON.parse(captionDurationInfoStr);textWidget.setCaptionDurationInfo(captionDurationInfo)}}}var Amaz$1=effect.Amaz;var Rect=Amaz$1.Rect;var Vec3=Amaz$1.Vector3f;var Vec2=Amaz$1.Vector2f;var ObjectModelType;(function(ObjectModelType){ObjectModelType[ObjectModelType["OBJECT_MODEL_RTTI"]=0]="OBJECT_MODEL_RTTI";ObjectModelType[ObjectModelType["OBJECT_MODEL_STATIC"]=1]="OBJECT_MODEL_STATIC"})(ObjectModelType||(ObjectModelType={}));class RichText extends Widget2D{constructor(name,widgetType,scene){super(name,widgetType,scene);this.m_jsonParams=null;this.m_textComp=null;this.m_needUpdateTextCom=true;this.m_lineMaxWidth=-1;this.m_textInfoLayerWidth=-1;this.m_ktvColor=new Amaz$1.Vector4f;this.m_ktvOutlineColor=new Amaz$1.Vector4f;this.m_ktvShadowColor=new Amaz$1.Vector4f;this.m_captionDurationInfo=null;this.m_captionDurationInfoDirty=false;this.m_captionParamsCache=null;this.m_captionParamsDirty=false;this.m_textCapitalDirty=false;this.m_runtimeParamsCache=null;this.m_captionAnimationController=null;this.m_textAnimationController=null;this.m_keyframeParams=null;this.m_cachePixelSize=new Vec2;this.m_textAssociationController=null;this.m_enableTextTemplateOpt=false;this.m_initValues=undefined;this.m_hasStudioAnimation=false}createRichText(jsonParams,scene){this._createTextEntity(scene);this.parameters=jsonParams}get initValues(){return this.m_initValues}get textComp(){return this.m_textComp}set textComp(text){this.m_textComp=text}get cachePixelSize(){return this.m_cachePixelSize}get captionDurationInfo(){return this.m_captionDurationInfo}set captionDurationInfoDirty(dirty){this.m_captionDurationInfoDirty=dirty}set captionParamsDirty(dirty){this.m_captionParamsDirty=dirty}set textCapitalDirty(dirty){this.m_textCapitalDirty=dirty}get textAssociationController(){return this.m_textAssociationController}set captionAnimationController(captionAnimationController){this.m_captionAnimationController=captionAnimationController}get captionAnimationController(){return this.m_captionAnimationController}getOrCreateCaptionAnimationController(){if(this.m_captionAnimationController==null&&this.m_textComp!=null){this.m_captionAnimationController=new CaptionAnimationController(this.m_textComp)}return this.m_captionAnimationController}get jsonParams(){return this.m_jsonParams}setParamsToAnims(){var _a,_b,_c,_d,_e,_f,_g,_h,_j;(_a=this.m_captionAnimationController)===null||_a===void 0?void 0:_a.updateCaptionInfo();const properties=new Map;const captionParams=(_c=(_b=this.m_captionAnimationController)===null||_b===void 0?void 0:_b.captionInfo)===null||_c===void 0?void 0:_c.caption_params;if((this.m_animationsDirty||this.m_captionParamsDirty)&&this.m_captionAnimationController&&captionParams){if(captionParams.keyword_rich_text!=null&&captionParams.enable_keyword){const keyword_rich_text_str=this.m_captionAnimationController.captionInfo.getKeywordRichTextWithSize();properties.set("keyword_rich_text",keyword_rich_text_str)}properties.set("caption_params",JSON.stringify(captionParams))}if((this.m_animationsDirty||this.m_textCapitalDirty)&&this.m_captionAnimationController&&captionParams){properties.set("text_capital",this.m_captionAnimationController.captionInfo.capitalStr)}if((this.m_animationsDirty||this.m_captionDurationInfoDirty)&&((_e=(_d=this.m_captionAnimationController)===null||_d===void 0?void 0:_d.captionInfo)===null||_e===void 0?void 0:_e.caption_duration_info_raw)){properties.set("caption_duration_info",JSON.stringify((_g=(_f=this.m_captionAnimationController)===null||_f===void 0?void 0:_f.captionInfo)===null||_g===void 0?void 0:_g.caption_duration_info_raw))}if(this.m_animations==null||this.m_animations.size==0){if(this.m_captionAnimationController!=null){this.m_captionAnimationController.needMerge=false}return}if(this.m_captionAnimationController!=null){if((this.m_animationsDirty||this.m_captionAnimationController.checkIsDirty())&&this.m_keyframeParams&&"value_generators"in this.m_keyframeParams){this.m_captionAnimationController.updateSelectors(0);this.m_captionAnimationController.updateValueGenerators(this.m_keyframeParams.value_generators);const keywordValueGeneration=(_h=this.getOrCreateCaptionAnimationController())===null||_h===void 0?void 0:_h.keywordAnimations;if(keywordValueGeneration&&captionParams&&"keyword_rich_text"in captionParams&&captionParams.keyword_rich_text!=""){const keywordStyles=[];for(let i=0;i<keywordValueGeneration.length;i++){const vg=keywordValueGeneration[i];if(vg.generatoType!="expression"){continue}if(vg.targetAttribute=="tc"){const expression=vg.generator;expression.parse();const expressionRes=expression.update();for(let index=0;index<expressionRes.length;index++){const value=expressionRes[index];const content=JSON.parse(captionParams.keyword_rich_text);content.styles[0].fill.content.solid.color=[value[0],value[1],value[2],value[3]];keywordStyles.push(content)}}else if(vg.targetAttribute=="object"){const expression=vg.generator;expression.parse();const expressionRes=expression.update();for(let index=0;index<expressionRes.length;index++){const value=expressionRes[index];const content=JSON.parse(captionParams.keyword_rich_text);if(this.m_textComp){const letters=this.m_textComp.letters.clone();const callbackParams={values:[{name:vg.targetAttribute,value:value,start:0,end:1,overlayMode:vg.overlayMode,selectorUnit:"word"}]};const jsonStr=JSON.stringify(callbackParams);AmazUtils$1.swingTemplateUtils.captionSetParamsBatch(this.m_textComp,jsonStr);const richTextStr=this.m_textComp.getRichStr(2,0,1,1,true);const newStyle=JSON.parse(richTextStr);if(content.styles[0]&&content.styles[0].fill&&content.styles[0].fill.content){content.styles[0].fill.content=newStyle.styles[0].fill.content}keywordStyles.push(content);this.m_textComp.letters=letters}}}}if(keywordStyles.length>0){properties.set("keyword_rich_text",JSON.stringify(keywordStyles))}}}}if(properties.size<1){return}(_j=this.m_animations)===null||_j===void 0?void 0:_j.forEach((anim=>{if(anim!==undefined&&anim.animationType==AnimationType.loop){anim.setAnimationProperty(properties);this.m_captionParamsDirty=false;this.m_textCapitalDirty=false;this.m_captionDurationInfoDirty=false;this.m_animationsDirty=false;if(this.m_captionAnimationController!=null){this.m_captionAnimationController.needMerge=anim.m_needMerge}anim.onClear();if(this._isStudioAnim(anim)){this._resetForStudioAnimation()}}}))}getWordInfosFromAnims(){if(this.m_captionAnimationController==null||this.m_animations==null)return null;const wordInfos=new Map;this.m_animations.forEach((anim=>{var _a;if(anim!==undefined&&anim.animationType==AnimationType.loop){const infos=(_a=anim.script)===null||_a===void 0?void 0:_a.call("getWordInfos");if(infos){wordInfos.set(anim.animationType,infos)}else{console.log("Monkey getWordInfosFromAnims() failed!")}}}));return wordInfos}set parameters(jsonParams){var _a,_b,_c,_d,_e,_f;if(this.m_textAnimationController==null){this.m_textAnimationController=new TextAnimationController(this,this.textComp)}if(this.m_jsonParams!=jsonParams&&jsonParams){this.m_jsonParams=jsonParams;this.m_needUpdateTextCom=true;super.setParameters(this.m_jsonParams);this.updateRootEntityParam();this.updateAssociationParam(this.m_jsonParams);if("studio_op_params"in jsonParams){const studio_op_params=jsonParams.studio_op_params;if("reload"in studio_op_params&&studio_op_params.reload==true){if(this.textComp&&this.m_initValues){this.reloadAllAnimation();this.m_initValues.recover(this.textComp)}}}this.checkStudioAnimationAndRegister();this.updateText();this.handleStudioAnimation();this._updateCaptionDurationInfo(jsonParams);const capital=(_a=jsonParams===null||jsonParams===void 0?void 0:jsonParams.text_params)===null||_a===void 0?void 0:_a.capital;if(this.m_captionAnimationController&&capital){this.m_textCapitalDirty=true;this.m_captionAnimationController.setTextCapital(capital)}(_b=this.m_textAssociationController)===null||_b===void 0?void 0:_b.updateByAssociation();const keyframe_params=jsonParams===null||jsonParams===void 0?void 0:jsonParams.keyframe_params;let keyframeContent;if(keyframe_params!=null){if("resource_path"in keyframe_params){const contentJson=AmazFileUtils.readFileContent(keyframe_params.resource_path);if(contentJson!=undefined){keyframeContent=JSON.parse(contentJson)}}else{keyframeContent=keyframe_params}if(keyframeContent!=null&&this.m_keyframeParams!=keyframeContent){this.m_keyframeParams=keyframeContent;(_c=this.m_textAnimationController)===null||_c===void 0?void 0:_c.setKeyFrameParams(keyframeContent);if(keyframeContent&&typeof keyframeContent==="object"&&Object.keys(keyframeContent).some((key=>key!=="text_keyframes"))){(_d=this.getOrCreateCaptionAnimationController())===null||_d===void 0?void 0:_d.setKeyFrameParams(keyframeContent)}}}const caption_params=jsonParams===null||jsonParams===void 0?void 0:jsonParams.caption_params;if(caption_params!=null&&this.m_captionParamsCache!=caption_params){this.m_captionParamsCache=caption_params;this.m_captionParamsDirty=true;(_e=this.getOrCreateCaptionAnimationController())===null||_e===void 0?void 0:_e.setCaptionParams(caption_params)}const runtime_params=jsonParams===null||jsonParams===void 0?void 0:jsonParams.runtime_params;if(runtime_params!=null&&this.m_runtimeParamsCache!=runtime_params){this.m_runtimeParamsCache=runtime_params;(_f=this.getOrCreateCaptionAnimationController())===null||_f===void 0?void 0:_f.setCaptionRuntimeParams(runtime_params)}this.setParamsToAnims();if(this.textAssociationController){this.textAssociationController.stickerAssoTimeScopeDirty=true;this.textAssociationController.stickerAssoLayoutDirty=true}}}checkStudioAnimationAndRegister(){if(this.textComp==null)return;this.m_hasStudioAnimation=false;if(this.m_animations&&this.textComp){for(const anim of this.m_animations.values()){if(this._isStudioAnim(anim)){this.m_hasStudioAnimation=true;anim.registerRichText(this.textComp)}}}if(this.m_hasStudioAnimation){if(this.m_initValues&&this.m_textComp){this.m_initValues.recover(this.m_textComp)}}}handleStudioAnimation(){if(this.textComp==null)return;if(this.m_hasStudioAnimation){this.m_initValues=new InitValues(this.textComp);this.m_initValues.recordForPerformance(this.textComp);TextStudioAnimation.clearTRSCaculator()}}clearInitValueForBridge(){if(this.m_hasStudioAnimation){if(this.m_initValues&&this.m_textComp){this.m_initValues.recover(this.m_textComp)}}this.m_initValues=undefined}query(queries){if(this.m_captionAnimationController){return this.m_captionAnimationController.query(queries)}else{return{}}}get parameters(){var _a,_b;if(this.textComp){const textCanvasColor=this.textComp.canvas.canvasColor;const canvasType=(_a=this.textComp.canvas)===null||_a===void 0?void 0:_a.canvasType;let tmpShapeType="";if(canvasType===effect.Amaz.CanvasStyle.BOUNDING_BOX){tmpShapeType="bounding"}else if(canvasType===effect.Amaz.CanvasStyle.WRAP_MERGED){tmpShapeType="wrapMerged"}else if(canvasType===effect.Amaz.CanvasStyle.WRAP_SEPARATE){tmpShapeType="wrapSeparate"}else if(canvasType===effect.Amaz.CanvasStyle.WRAP_INDEPENDENT){tmpShapeType="wrapIndependent"}const tmpFillInfo=JSON.parse((_b=this.textComp.globalBackground)===null||_b===void 0?void 0:_b.getFillInfoStr());const tmpCanvasAdvancedInfo={shapeType:tmpShapeType,fill:tmpFillInfo};const textSelectColor=this.textComp.selectColor;const textBloomColor=this.textComp.bloomColor;const textParam={version:"2",richText:this.textComp.getRichStr(0,0,0,0,false),typeSettingKind:this.textComp.typeSettingParam.typeSettingKind,typeSettingAlign:this.textComp.typeSettingParam.typeSettingAlign,typeSettingPathEnable:this.textComp.typeSettingParam.enablePathTypeSetting,enableMergeLines:this.textComp.typeSettingParam.mergingIntoOneLine,canvas:this.textComp.canvas.canvasEnabled,canvasColor:[textCanvasColor.x,textCanvasColor.y,textCanvasColor.z,textCanvasColor.w],canvasRoundCorner:this.textComp.canvas.canvasRoundCornerEnabled,canvasRoundRadius:this.textComp.canvas.canvasRoundRadius,canvasRoundRadiusScale:this.textComp.canvas.canvasRoundRadiusScale,canvasWrapped:this.textComp.canvas.canvasWrappText,canvasCustomized:this.textComp.canvas.canvasCustomizedEnabled,canvasWHCustomized:[this.textComp.canvas.canvasWHCustomized.x,this.textComp.canvas.canvasWHCustomized.y],canvasOffsetCustomized:[this.textComp.canvas.canvasOffsetCustomized.x,this.textComp.canvas.canvasOffsetCustomized.y],canvasAdvancedInfo:tmpCanvasAdvancedInfo,boldValue:this.textComp.activeTextStyle.boldValue,italicDegree:this.textComp.activeTextStyle.italicAngle,decorationWidth:this.textComp.activeTextStyle.decorationWidth,decorationOffset:this.textComp.activeTextStyle.decorationOffset,lineSpacing:this.textComp.typeSettingParam.lineSpacing,letterSpacing:this.textComp.typeSettingParam.letterSpacing,innerPadding:this.textComp.typeSettingParam.verticalPadding,lineMaxWidth:this.m_lineMaxWidth,canvasWHFixed:[this.textComp.typeSettingParam.canvasWHFixed.x,this.textComp.typeSettingParam.canvasWHFixed.y],fallbackFontPathList:[],oneLineCutOff:this.textComp.typeSettingParam.lineBreakType===Amaz$1.LineBreakType.CUT_OFF,cutOffPostfix:this.textComp.cutOffPostfix,shapePath:" ",shapeFlipX:false,shapeFlipY:false,ktvColor:[this.m_ktvColor.x,this.m_ktvColor.y,this.m_ktvColor.z,this.m_ktvColor.w],ktvOutlineColor:[this.m_ktvOutlineColor.x,this.m_ktvOutlineColor.y,this.m_ktvOutlineColor.z,this.m_ktvOutlineColor.w],ktvShadowColor:[this.m_ktvShadowColor.x,this.m_ktvShadowColor.y,this.m_ktvShadowColor.z,this.m_ktvShadowColor.w],autoAdaptDpiEnabled:this.textComp.typeSettingParam.autoAdaptDpiEnabled,globalAlpha:this.textComp.globalAlpha,selectedColor:[textSelectColor.x,textSelectColor.y,textSelectColor.z,textSelectColor.w],rootPath:this.textComp.rootPath,bloomPath:this.textComp.bloomPath,bloomColorCustomized:this.textComp.bloomColorCustomized,bloomColor:[textBloomColor.x,textBloomColor.y,textBloomColor.z,textBloomColor.w],bloomStrength:this.textComp.bloomStrength,bloomRange:this.textComp.bloomRange,bloomDirX:this.textComp.bloomDirX,bloomDirY:this.textComp.bloomDirY,bloomBlurDegree:this.textComp.bloomBlurDegree,sdfTextAlpha:this.textComp.sdfTextAlpha,textLocale:this.textComp.textLocale,directionLevel:this.textComp.textDirectionLevel};const typeSettingPathParam=this.textComp.typeSettingParam.pathTypeSettingParam;if(typeSettingPathParam!=null){const pathInfoData={curvesInfo:[]};for(let curveId=0;curveId<typeSettingPathParam.curvesInfoAry.size();++curveId){const curveInfomParam=typeSettingPathParam.curvesInfoAry.get(curveId);const curveInfoData={curveType:curveInfomParam.curveType,curveAngle:curveInfomParam.curveAngle,offsetOnCurve:curveInfomParam.curveOffset,points:[]};const pointsNum=curveInfomParam.curvePoints.size();for(let pointId=0;pointId<pointsNum;++pointId){const pointParam=curveInfomParam.curvePoints.get(pointId);curveInfoData.points.push(pointParam.x);curveInfoData.points.push(pointParam.y)}pathInfoData.curvesInfo.push(curveInfoData)}textParam.typeSettingPathParam=pathInfoData}const fallbackFontPaths=this.textComp.fallbackFontPaths;for(let i=0;i<fallbackFontPaths.size();i++){const fontPath=fallbackFontPaths.get(i);textParam.fallbackFontPathList.push(fontPath)}if(this.m_captionDurationInfo!=null){textParam.caption_duration_info=this.m_captionDurationInfo}const widgetParam=super.parameters;const widget2DParam=super.getParameters();const richTextParamData={name:widgetParam.name,type:widgetParam.type,position:widgetParam.position,rotation:widgetParam.rotation,scale:widgetParam.scale,order_in_layer:widgetParam.order_in_layer,start_time:widgetParam.start_time,duration:widgetParam.duration,layout_params:widget2DParam.layout_params,original_size:widget2DParam.original_size,text_params:textParam,anims:widget2DParam.anims,action_type:widgetParam.action_type};if(this.m_keyframeParams!=null){richTextParamData.keyframe_params=this.m_keyframeParams}if(this.textAssociationController&&this.textAssociationController.associationParams){richTextParamData.association_params=this.textAssociationController.associationParams}return richTextParamData}else{return"{}"}}set needUpdateTextCom(needed){this.m_needUpdateTextCom=needed}get needUpdateTextCom(){return this.m_needUpdateTextCom}setCaptionDurationInfo(captionDurationInfo){var _a;if(captionDurationInfo!=null&&this.m_captionDurationInfo!=captionDurationInfo){this.m_captionDurationInfo=captionDurationInfo;this.m_captionDurationInfoDirty=true;(_a=this.getOrCreateCaptionAnimationController())===null||_a===void 0?void 0:_a.setCaptionDurationInfo(captionDurationInfo)}}_updateCaptionDurationInfo(jsonParams){var _a,_b,_c;if((_a=this.m_textAssociationController)===null||_a===void 0?void 0:_a.needUpdateCaption){return}if(((_b=this.m_textAssociationController)===null||_b===void 0?void 0:_b.isSetTextAssoWithWordInfo)&&this.captionAnimationController===null){return}const caption_duration_info=(_c=jsonParams===null||jsonParams===void 0?void 0:jsonParams.text_params)===null||_c===void 0?void 0:_c.caption_duration_info;this.setCaptionDurationInfo(caption_duration_info)}_setRichStr(richStr){var _a;if(this.textComp!=null){if(!this.m_enableTextTemplateOpt){this.textComp.richStr=richStr;this.textComp.updateLetters()}else{if(richStr.length!==this.textComp.richStr.length||this.textComp.richStr!==richStr){this.textComp.richStr=richStr;if(this.m_captionAnimationController){this.textComp.updateLetters()}}}}(_a=this.m_captionAnimationController)===null||_a===void 0?void 0:_a.onTextChangeForScript()}_convertPosBetweenRect(srcPos,srcRect,dstRect){const srcRectCenter=new Vec2(srcRect.x+.5*srcRect.width,srcRect.y+.5*srcRect.height);const dstRectCenter=new Vec2(dstRect.x+.5*dstRect.width,dstRect.y+.5*dstRect.height);const real_pos=new Vec2(srcRectCenter.x+srcPos.x*srcRect.width*.5,srcRectCenter.y+srcPos.y*srcRect.height*.5);if(dstRect.width!==0&&dstRect.height!==0){const dstPos=new Vec2((real_pos.x-dstRectCenter.x)/(dstRect.width*.5),(real_pos.y-dstRectCenter.y)/(dstRect.height*.5));return dstPos}else{console.error(TEMPLATE_TAG,"_convertPosBetweenRect dstRect error.");const dstPos=new Vec2(srcPos.x,srcPos.y);return dstPos}}_getTextUIRect(){let textRect=new Rect(0,0,0,0);if(this.textComp){textRect=this.textComp.canvas.UIRect}return textRect}_updateCachePixelSize(){if(this.textComp&&this.hasAnimation()){this.textComp.forceTypeSetting();let rect=this._getTextUIRect();if(!(rect.width>0&&rect.height>0)){rect=this.textComp.getCanvasCustomizedExpanded()}this.m_cachePixelSize.x=rect.width;this.m_cachePixelSize.y=rect.height}}_isCommandStrChanged(cmdType){return cmdType==TextCommandType.CT_INPUT_STR||cmdType==TextCommandType.CT_INPUT_RICH_STR||cmdType==TextCommandType.CT_BACK_DELETE||cmdType==TextCommandType.CT_FORWARD_DELETE||cmdType==TextCommandType.CT_PREEDIT_COMPOSE||cmdType==TextCommandType.CT_END_COMPOSE||cmdType==TextCommandType.CT_EDIT_TEMPLATE_TEXT_STYLE||cmdType==TextCommandType.CT_EDIT_RESET_TEXT_CONTEXT||cmdType==TextCommandType.CT_EDIT_CONVERT_CASE||cmdType==TextCommandType.CT_COMMAND_BATCH_PROCESS}_isCommandCursorOrSelectionChanged(cmdType){return cmdType>=TextCommandType.CT_ENTER_EDIT_STATE||cmdType>=TextCommandType.CT_MOVE_CURSOR_LR&&cmdType<=TextCommandType.CT_SELECT_ALL_CONTENT}_isCommandStyleChanged(cmdType){return cmdType==TextCommandType.CT_FORCE_REFRESH||cmdType>=TextCommandType.CT_EDIT_FONT&&cmdType<=TextCommandType.CT_EDIT_BACKGROUND_STYLE||cmdType==TextCommandType.CT_COMMAND_BATCH_PROCESS}_updateBeforeTextCommand(cmd){if(!cmd){console.error(TEMPLATE_TAG,"_updateBeforeTextCommand: update command with a nullptr!");return}if(this.textComp==null){return}if(cmd.type==TextCommandType.CT_EDIT_TEXT_PARAM||cmd.type==TextCommandType.CT_EDIT_TEXT_PRESET_STYLE_PARAM){const config=JSON.parse(cmd.sParam1);if(!config){console.error(TEMPLATE_TAG,"_updateBeforeTextCommand: CT_EDIT_TEXT_PARAM/PRESET_STYLE_PARAM cmd read json failed!");return}if(cmd.type==TextCommandType.CT_EDIT_TEXT_PRESET_STYLE_PARAM){console.debug(TEMPLATE_TAG,"_updateBeforeTextCommand: CT_EDIT_TEXT_PRESET_STYLE_PARAM, param:",cmd.sParam1);cmd.sParam1="";if("richText"in config){cmd.sParam1=config.richText;delete config.richText}}if(cmd.type==TextCommandType.CT_EDIT_TEXT_PARAM){if("richText"in config&&cmd.iParam1<.5){this.textComp.resetEditContextState()}}this._updateTextParams(config)}else if(cmd.type==TextCommandType.CT_MOVE_CURSOR_BY_POS||cmd.type==TextCommandType.CT_SELECT_MOUSE_CONTENT){if(null==this.m_textComp)return;const x=-.5*this.m_widgetOriginalPixelSize.x;const y=-.5*this.m_widgetOriginalPixelSize.y;const text_rect=new Amaz$1.Rect(x,y,this.m_widgetOriginalPixelSize.x,this.m_widgetOriginalPixelSize.y);const canvas_rect=this.m_textComp.getCanvasCustomizedExpanded();const srcPos=new Vec2(cmd.iParam1,cmd.iParam2);const dstPos=this._convertPosBetweenRect(srcPos,text_rect,canvas_rect);cmd.iParam1=dstPos.x;cmd.iParam2=dstPos.y}}_updateAfterTextCommand(cmd){if(!cmd){console.error(TEMPLATE_TAG,"_updateAfterTextCommand: update command with a nullptr!");return}}_forceFlushCommandQueue(cmdList){if(null==this.m_textComp){console.error(TEMPLATE_TAG,"_forceFlushCommandQueue: textComp in nullptr!");return}if(this.m_textComp.isLetterDirty()){this.m_textComp.updateLetters()}for(let cmdId=0;cmdId<cmdList.length;cmdId++){const cmd=cmdList[cmdId];this._updateBeforeTextCommand(cmd);if(!this.m_textComp){console.error(TEMPLATE_TAG,"_forceFlushCommandQueue: editing textComp in nullptr!");break}this.m_textComp.executeOneEditCommand(cmd);this._updateAfterTextCommand(cmd)}}setRichTextByOPCode(textOP){if(null==this.m_textComp){console.error(TEMPLATE_TAG,"setRichTextByOPCode: text component is null!");return}const commandStr=textOP.m_sParam;const cmdList=[];if(textOP.m_iOpCode===TextCommandType.CT_COMMAND_BATCH_PROCESS){const config=JSON.parse(commandStr);if(!("commands"in config)){console.error(TEMPLATE_TAG,"setRichTextByOPCode: CT_COMMAND_BATCH_PROCESS cmd read nothing!");return}const cmdVec=config.commands;if(cmdVec instanceof Array&&cmdVec.length>0){for(let cmdId=0;cmdId<cmdVec.length;++cmdId){const cmdInfo=cmdVec[cmdId];if(!("cmdCode"in cmdInfo)){continue}const cmd=new Amaz$1.TextCommand;cmd.type=cmdInfo.cmdCode;if("iParams"in cmdInfo){const iParams=cmdInfo.iParams;if(iParams instanceof Array&&iParams.length>=4){cmd.iParam1=iParams[0];cmd.iParam2=iParams[1];cmd.iParam3=iParams[2];cmd.iParam4=iParams[3]}}if("sParams"in cmdInfo){cmd.sParam1=cmdInfo.sParams}cmdList.push(cmd)}}console.debug(TEMPLATE_TAG,"setRichTextByOPCode: CT_COMMAND_BATCH_PROCESS cmd executing: ",commandStr)}else{const cmd=new Amaz$1.TextCommand;cmd.type=textOP.m_iOpCode;cmd.iParam1=textOP.m_fParam1;cmd.iParam2=textOP.m_fParam2;cmd.iParam3=textOP.m_fParam3;cmd.iParam4=textOP.m_fParam4;cmd.sParam1=commandStr;cmdList.push(cmd)}let isStrChanged=false;let isStyleChanged=false;for(let cmdId=0;cmdId<cmdList.length;++cmdId){const cmd=cmdList[cmdId];isStrChanged=isStrChanged||this._isCommandStrChanged(cmd.type);isStrChanged=isStrChanged||this.hasAnimation()&&this._isCommandCursorOrSelectionChanged(cmd.type);isStyleChanged=isStyleChanged||this._isCommandStyleChanged(cmd.type)}if(isStrChanged||isStyleChanged){this.reloadAllAnimation()}if(isStrChanged&&this.m_textAssociationController){this.m_textAssociationController.isStrChanged=true}this._forceFlushCommandQueue(cmdList);const needUpdateRect=isStrChanged||isStyleChanged;if(needUpdateRect){this.updateTextRect()}if(this.m_captionAnimationController){this.m_captionAnimationController.onTextChangeForScript()}}getRichTextByOPCode(textOP){const resultTextOp={m_iOpCode:TextCommandType.CT_NONE,m_fParam1:0,m_fParam2:0,m_fParam3:0,m_fParam4:0,m_sParam:""};if(null==this.m_textComp){console.error(TEMPLATE_TAG,"getRichTextByOPCode: text component is null!");return resultTextOp}if(textOP.m_iOpCode===TextCommandType.CT_GET_PLAIN_STR||textOP.m_iOpCode===TextCommandType.CT_GET_RICH_STR){let resultStr="";const range=this.m_textComp.convertIdUincodesToLetter(textOP.m_fParam2,textOP.m_fParam3);if(textOP.m_iOpCode===TextCommandType.CT_GET_PLAIN_STR){resultStr=this.m_textComp.getPlainStr(textOP.m_fParam1,range.x,range.y);resultTextOp.m_iOpCode=TextCommandType.CT_GET_PLAIN_STR}else{resultStr=this.m_textComp.getRichStr(textOP.m_fParam1,range.x,range.y,textOP.m_fParam4,false);resultTextOp.m_iOpCode=TextCommandType.CT_GET_RICH_STR}resultTextOp.m_sParam=resultStr}else if(textOP.m_iOpCode===TextCommandType.CT_GET_CURSOR_RECT){const cursor_rect=this.m_textComp.getCursorRect();const canvas_rect=this.m_textComp.canvas.canvasRect;const x=cursor_rect.x+(canvas_rect.x+.5*canvas_rect.width);const y=cursor_rect.y+(canvas_rect.y+.5*canvas_rect.height);cursor_rect.x=x;cursor_rect.y=y;const sticker_rect=new Rect(-.5*this.m_widgetOriginalPixelSize.x,-.5*this.m_widgetOriginalPixelSize.y,this.m_widgetOriginalPixelSize.x,this.m_widgetOriginalPixelSize.y);const sticker_center=new Amaz$1.Vector2f(0,0);resultTextOp.m_iOpCode=TextCommandType.CT_GET_CURSOR_RECT;resultTextOp.m_fParam1=sticker_rect.width===0?0:(cursor_rect.x-sticker_center.x)/(sticker_rect.width*.5);resultTextOp.m_fParam2=sticker_rect.height===0?0:(cursor_rect.y-sticker_center.y)/(sticker_rect.height*.5);resultTextOp.m_fParam3=sticker_rect.width===0?0:cursor_rect.width/sticker_rect.width;resultTextOp.m_fParam4=sticker_rect.height===0?0:cursor_rect.height/sticker_rect.height}else if(textOP.m_iOpCode===TextCommandType.CT_GET_CHAR_RECT){const letter_id=this.m_textComp.convertIdUToL(textOP.m_fParam1);const char_rect=this.m_textComp.getTextRect(letter_id,letter_id);const canvas_rect=this.m_textComp.getCanvasCustomizedExpanded();const canvas_center=new Amaz$1.Vector2f(canvas_rect.x+canvas_rect.width*.5,canvas_rect.y+canvas_rect.height*.5);resultTextOp.m_iOpCode=TextCommandType.CT_GET_CHAR_RECT;resultTextOp.m_fParam1=canvas_rect.width===0?0:(char_rect.x-canvas_center.x)/(canvas_rect.width*.5);resultTextOp.m_fParam2=canvas_rect.height===0?0:(char_rect.y-canvas_center.y)/(canvas_rect.height*.5);resultTextOp.m_fParam3=canvas_rect.width===0?0:char_rect.width/canvas_rect.width;resultTextOp.m_fParam4=canvas_rect.height===0?0:char_rect.height/canvas_rect.height}else if(textOP.m_iOpCode===TextCommandType.CT_GET_CURSOR_CHAR_INDEX){resultTextOp.m_iOpCode=TextCommandType.CT_GET_CURSOR_CHAR_INDEX;const letter_id=this.m_textComp.getLetterIndexByCursor(textOP.m_fParam1);let utf16_id=this.m_textComp.convertIdLToU(letter_id);const cursor_id=this.m_textComp.getTextEditCursorIndex();if(letter_id===0&&cursor_id==letter_id&&textOP.m_fParam1===-1){utf16_id-=1}else if(letter_id===this.m_textComp.letters.size()-1&&cursor_id===letter_id+1&&textOP.m_fParam1===1){utf16_id+=1;const last_letter=this.m_textComp.letters.back();if(last_letter instanceof Amaz$1.Letter){utf16_id+=last_letter.getUTF16Size()}}resultTextOp.m_fParam1=utf16_id}else if(textOP.m_iOpCode===TextCommandType.CT_GET_SELECT_RANGE){resultTextOp.m_iOpCode=TextCommandType.CT_GET_SELECT_RANGE;const ret=this.m_textComp.getSelectRange();resultTextOp.m_fParam1=ret.x;resultTextOp.m_fParam2=this.m_textComp.convertIdLToU(ret.y);resultTextOp.m_fParam3=this.m_textComp.convertIdLToU(ret.z)}else if(textOP.m_iOpCode===TextCommandType.CT_GET_ERROR_INFO){resultTextOp.m_iOpCode=TextCommandType.CT_GET_ERROR_INFO;resultTextOp.m_fParam1=this.m_textComp.getEditErrorCode();resultTextOp.m_sParam=this.m_textComp.getEditErrorLog()}else if(textOP.m_iOpCode===TextCommandType.CT_GET_EDIT_CONTEXT_INFO){resultTextOp.m_iOpCode=TextCommandType.CT_GET_EDIT_CONTEXT_INFO;resultTextOp.m_fParam1=this.m_textComp.getTextEditEnable()?1:0;resultTextOp.m_fParam2=this.m_textComp.convertIdLToU(this.m_textComp.getTextEditCursorIndex());resultTextOp.m_fParam3=this.m_textComp.getTextEditHasSelected()?1:0;resultTextOp.m_fParam4=this.m_textComp.getTextEditIsInputMethord()?1:0;const range=this.m_textComp.getSelectRange();range.y=this.m_textComp.convertIdLToU(range.y);range.z=this.m_textComp.convertIdLToU(range.z);const cursor_rect=this.m_textComp.getCursorRect().copy();const canvas_rect=this.m_textComp.canvas.canvasRect;cursor_rect.x=cursor_rect.x+canvas_rect.x+.5*canvas_rect.width;cursor_rect.y=cursor_rect.y+canvas_rect.y+.5*canvas_rect.height;const sticker_rect=new Rect(-.5*this.m_widgetOriginalPixelSize.x,-.5*this.m_widgetOriginalPixelSize.y,this.m_widgetOriginalPixelSize.x,this.m_widgetOriginalPixelSize.x);const sticker_center=new Amaz$1.Vector2f(0,0);const x=sticker_rect.width===0?0:(cursor_rect.x-sticker_center.x)/(sticker_rect.width*.5);const y=sticker_rect.height===0?0:(cursor_rect.y-sticker_center.y)/(sticker_rect.height*.5);const width=sticker_rect.width===0?0:cursor_rect.width/sticker_rect.width;const height=sticker_rect.height===0?0:cursor_rect.height/sticker_rect.height;const richTextStr=this.m_textComp.getRichStr(0,0,0,0,false);const errorInfoStr=this.m_textComp.getEditErrorLog();const jsonObj={selectRange:[range.x,range.y,range.z],cursorRect:[x,y,width,height],richText:richTextStr,errorInfo:errorInfoStr};const retStr=JSON.stringify(jsonObj);resultTextOp.m_sParam=retStr}else if(textOP.m_iOpCode===TextCommandType.CT_GET_SELECT_HANDLE_RECT){resultTextOp.m_iOpCode=TextCommandType.CT_GET_SELECT_HANDLE_RECT;if(this.m_textComp.getSelectHandleVisible()){const indexRet=this.m_textComp.getSelectHandleIndex();const rectRet=this.m_textComp.getSelectHandleRect();const sticker_rect=new Rect(-.5*this.m_widgetOriginalPixelSize.x,-.5*this.m_widgetOriginalPixelSize.y,this.m_widgetOriginalPixelSize.x,this.m_widgetOriginalPixelSize.y);const sticker_center=new Amaz$1.Vector2f(0,0);const canvas_rect=this.m_textComp.canvas.canvasRect;const rect_1=rectRet.get(0);const rect_2=rectRet.get(1);let x_1=0,y_1=0,width_1=0,height_1=0;let x_2=0,y_2=0,width_2=0,height_2=0;if(rect_1 instanceof Rect){rect_1.x=rect_1.x+canvas_rect.x+.5*canvas_rect.width;rect_1.y=rect_1.y+canvas_rect.y+.5*canvas_rect.height;x_1=sticker_rect.width===0?0:(rect_1.x-sticker_center.x)/(sticker_rect.width*.5);y_1=sticker_rect.height===0?0:(rect_1.y-sticker_center.y)/(sticker_rect.height*.5);width_1=sticker_rect.width===0?0:rect_1.width/sticker_rect.width;height_1=sticker_rect.height===0?0:rect_1.height/sticker_rect.height}if(rect_2 instanceof Rect){rect_2.x=rect_2.x+canvas_rect.x+.5*canvas_rect.width;rect_2.y=rect_2.y+canvas_rect.y+.5*canvas_rect.height;x_2=sticker_rect.width===0?0:(rect_2.x-sticker_center.x)/(sticker_rect.width*.5);y_2=sticker_rect.height===0?0:(rect_2.y-sticker_center.y)/(sticker_rect.height*.5);width_2=sticker_rect.width===0?0:rect_2.width/sticker_rect.width;height_2=sticker_rect.height===0?0:rect_2.height/sticker_rect.height}resultTextOp.m_fParam1=this.m_textComp.convertIdLToU(indexRet.x);resultTextOp.m_fParam2=this.m_textComp.convertIdLToU(indexRet.y);const jsonObj={selectHandleRect_0:[x_1,y_1,width_1,height_1],selectHandleRect_1:[x_2,y_2,width_2,height_2]};const retStr=JSON.stringify(jsonObj);resultTextOp.m_sParam=retStr}}return resultTextOp}_createTextEntity(scene){const renderEntityName=this.m_name+"renderEntity";this.m_renderEntity=AmazUtils$1.createEntity(renderEntityName,scene);this.m_renderEntity.layer=this.m_cameraLayer;const localPos=new Vec3(0,0,0);const scale=new Vec3(1,1,1);const rotate=new Vec3(0,0,0);this.m_renderEntity.transform={position:localPos,scale:scale,rotation:rotate};this.m_renderEntity.addComponent("Text");this.m_renderEntity.addComponent("MeshRenderer");this.m_textComp=this.m_renderEntity.getComponent("Text");this.createWidgetRootEntity(scene);if(null!=this.m_rootEntity){AmazUtils$1.addChildEntity(this.m_rootEntity,this.m_renderEntity)}const utils=new Amaz$1.SwingTemplateUtils;this.m_enableTextTemplateOpt=utils.getABConfigValue(AmazUtils$1.AMGABTestName.ENABLE_TEXT_TEMPLATE_OPTIMIZE_V0,this.m_enableTextTemplateOpt)}_updateTextLineMaxWidth(screenSize,extraScale){if(this.m_textComp){if(this.m_lineMaxWidth>0){this.m_textComp.typeSettingParam.wordWrapWidth=extraScale.x===0?0:screenSize.x*this.m_lineMaxWidth/extraScale.x}else{this.m_textComp.typeSettingParam.wordWrapWidth=1e7}}}_setTextLineMaxWidth(lineMaxWidth){const DEGSIN_SIZE=new Amaz$1.Vector2f(720,1280);this.m_lineMaxWidth=lineMaxWidth;let extraScale=new Amaz$1.Vector2f(1,1);if(this.widgetResolutionType===WidgetResolutionType.NORMALIZED){if(this.m_enableTextTemplateOpt){this.updateTextRect()}const normalizedScale=this.getTextureNormalizedScale();this._updateTextLineMaxWidth(this.screenSize,normalizedScale)}else if(this.widgetResolutionType===WidgetResolutionType.ORIGINAL){if(this.m_textInfoLayerWidth>0){const screenSize=new Amaz$1.Vector2f(this.m_textInfoLayerWidth,this.m_textInfoLayerWidth);this._updateTextLineMaxWidth(screenSize,extraScale)}else{this._updateTextLineMaxWidth(this.screenSize,extraScale)}}else if(this.widgetResolutionType===WidgetResolutionType.DESIGN_HEIGHT){const scale=this.screenSize.y/DEGSIN_SIZE.y;extraScale=new Amaz$1.Vector2f(scale,scale);this._updateTextLineMaxWidth(this.screenSize,extraScale)}else{const scale=this.screenSize.x/DEGSIN_SIZE.x;extraScale=new Amaz$1.Vector2f(scale,scale);this._updateTextLineMaxWidth(this.screenSize,extraScale)}}_updateRichTextShapeParams(richTextConfig){}_updatePathTypeSettingParam(configJson){const pathInfoRet=new Amaz$1.PathInfo;if("curvesInfo"in configJson){const curvesArray=configJson.curvesInfo;if(curvesArray instanceof Array){for(let curveId=0;curveId<curvesArray.length;curveId++){const curve=curvesArray[curveId];const curveInfoRet=new Amaz$1.CurveInfo;if("curveType"in curve){curveInfoRet.curveType=curve.curveType}if("curveAngle"in curve){curveInfoRet.curveAngle=curve.curveAngle}if("offsetOnCurve"in curve){curveInfoRet.curveOffset=curve.offsetOnCurve}if("points"in curve){const pointsArray=curve.points;if(pointsArray instanceof Array){const pointsNum=pointsArray.length/2;for(let pointId=0;pointId<pointsNum;pointId++){const pointInfoRet=new Amaz$1.Vector2f(pointsArray[2*pointId],pointsArray[2*pointId+1]);curveInfoRet.curvePoints.pushBack(pointInfoRet)}}}pathInfoRet.curvesInfoAry.pushBack(curveInfoRet)}}}return pathInfoRet}_updateTextJsonParams(configJson){const textComp=this.m_textComp;if(!configJson||null==textComp){return}{if(!this.m_enableTextTemplateOpt){if("richText"in configJson){const configRichText=configJson.richText;this._setRichStr(configRichText)}}}{if("rootPath"in configJson){const configRootPath=configJson.rootPath;textComp.rootPath=configRootPath}}{if("globalAlpha"in configJson){const configGlobalAlpha=configJson.globalAlpha;textComp.globalAlpha=configGlobalAlpha}if("sdfTextAlpha"in configJson){const configSDFTextAlpha=configJson.sdfTextAlpha;textComp.sdfTextAlpha=configSDFTextAlpha}}{if("selectedColor"in configJson){const configSelectedColor=AmazUtils$1.CastJsonArray4fToAmazVector4f(configJson.selectedColor);if(null!==configSelectedColor){textComp.selectColor=configSelectedColor}else{console.error(TEMPLATE_TAG,"_updateTextParams config json selectedColor is not vector4f!")}}}{if("typeSettingKind"in configJson){const configTypeSettingKind=configJson.typeSettingKind;textComp.typeSettingParam.typeSettingKind=configTypeSettingKind}if("typeSettingAlign"in configJson){const configTypeSettingAlign=configJson.typeSettingAlign;textComp.typeSettingParam.typeSettingAlign=configTypeSettingAlign}if("lineSpacing"in configJson){const configLineSpacing=configJson.lineSpacing;textComp.typeSettingParam.lineSpacing=configLineSpacing}if("letterSpacing"in configJson){const configLetterSpacing=configJson.letterSpacing;textComp.typeSettingParam.letterSpacing=configLetterSpacing}if("innerPadding"in configJson){const configLetterSpacing=configJson.innerPadding;textComp.typeSettingParam.horizontalPadding=configLetterSpacing;textComp.typeSettingParam.verticalPadding=configLetterSpacing}if(!this.m_enableTextTemplateOpt){if("lineMaxWidth"in configJson){const configLineMaxWidth=configJson.lineMaxWidth;this._setTextLineMaxWidth(configLineMaxWidth)}}if("canvasWHFixed"in configJson){const configCanvasWHFixed=AmazUtils$1.CastJsonArray2fToAmazVector2f(configJson.canvasWHFixed);if(null!==configCanvasWHFixed){textComp.typeSettingParam.canvasWHFixed=configCanvasWHFixed}else{console.error(TEMPLATE_TAG,"_updateTextParams config json canvasWHFixed is not vector2f!")}}if("oneLineCutOff"in configJson){const configFlag=configJson.oneLineCutOff;if(configFlag){textComp.typeSettingParam.lineBreakType=Amaz$1.LineBreakType.CUT_OFF}else{textComp.typeSettingParam.lineBreakType=Amaz$1.LineBreakType.AUTO_LINEBREAK}}if("cutOffPostfix"in configJson){const configCutOffPostfix=configJson.cutOffPostfix;textComp.cutOffPostfix=configCutOffPostfix}if("autoAdaptDpiEnabled"in configJson){const configAutoAdaptDpiEnabled=configJson.autoAdaptDpiEnabled;textComp.typeSettingParam.autoAdaptDpiEnabled=configAutoAdaptDpiEnabled}if("typeSettingPathEnable"in configJson){const configTypeSettingPathEnable=configJson.typeSettingPathEnable;textComp.typeSettingParam.enablePathTypeSetting=configTypeSettingPathEnable}if("enableMergeLine"in configJson){const configEnableMergeLine=configJson.enableMergeLine;textComp.typeSettingParam.mergingIntoOneLine=configEnableMergeLine}if("typeSettingPathParam"in configJson){const configTypeSettingPathParam=this._updatePathTypeSettingParam(configJson.typeSettingPathParam);textComp.typeSettingParam.pathTypeSettingParam=configTypeSettingPathParam}}{if("canvas"in configJson){const configCanvas=configJson.canvas;textComp.canvas.canvasEnabled=configCanvas}if("canvasColor"in configJson){const configCanvasColor=AmazUtils$1.CastJsonArray4fToAmazVector4f(configJson.canvasColor);if(null!==configCanvasColor){textComp.canvas.canvasColor=configCanvasColor}else{console.error(TEMPLATE_TAG,"_updateTextParams config json canvasColor is not vector4f!")}}if("canvasRoundCorner"in configJson){const configCanvasRoundCorner=configJson.canvasRoundCorner;textComp.canvas.canvasRoundCornerEnabled=configCanvasRoundCorner}if("canvasRoundRadius"in configJson){const configCanvasRoundRadius=configJson.canvasRoundRadius;textComp.canvas.canvasRoundRadius=configCanvasRoundRadius}if("canvasRoundRadiusScale"in configJson){const configCanvasRoundRadiusScale=configJson.canvasRoundRadiusScale;textComp.canvas.canvasRoundRadiusScale=configCanvasRoundRadiusScale}if("canvasWrapped"in configJson){const configCanvasWrapped=configJson.canvasWrapped;textComp.canvas.canvasWrappText=configCanvasWrapped}if("canvasCustomized"in configJson){const configCanvasCustomized=configJson.canvasCustomized;textComp.canvas.canvasCustomizedEnabled=configCanvasCustomized}if("canvasWHCustomized"in configJson){const configCanvasWHCustomized=AmazUtils$1.CastJsonArray2fToAmazVector2f(configJson.canvasWHCustomized);if(null!==configCanvasWHCustomized){textComp.canvas.canvasWHCustomized=configCanvasWHCustomized}else{console.error(TEMPLATE_TAG,"_updateTextParams config json canvasWHCustomized is not vector2f!")}}if("canvasOffsetCustomized"in configJson){const configCanvasOffsetCustomized=AmazUtils$1.CastJsonArray2fToAmazVector2f(configJson.canvasOffsetCustomized);if(null!==configCanvasOffsetCustomized){textComp.canvas.canvasOffsetCustomized=configCanvasOffsetCustomized}else{console.error(TEMPLATE_TAG,"_updateTextParams config json canvasOffsetCustomized is not vector2f!")}}if("canvasAdvancedInfo"in configJson){if("shapeType"in configJson.canvasAdvancedInfo&&textComp.canvas!==null){const type=configJson.canvasAdvancedInfo.shapeType;if(type==="bounding"){textComp.canvas.canvasType=effect.Amaz.CanvasStyle.BOUNDING_BOX}else if(type==="wrapMerged"){textComp.canvas.canvasType=effect.Amaz.CanvasStyle.WRAP_MERGED}else if(type==="wrapSeparate"){textComp.canvas.canvasType=effect.Amaz.CanvasStyle.WRAP_SEPARATE}else if(type==="wrapIndependent"){textComp.canvas.canvasType=effect.Amaz.CanvasStyle.WRAP_INDEPENDENT}}if("fill"in configJson.canvasAdvancedInfo&&textComp.globalBackground!==null){textComp.globalBackground.setFillInfoStr(JSON.stringify(configJson.canvasAdvancedInfo))}}}{if("italicDegree"in configJson){const configItalicDegree=configJson.italicDegree;textComp.activeTextStyle.italicAngle=configItalicDegree}if("boldValue"in configJson){const configBoldValue=configJson.boldValue;textComp.activeTextStyle.boldValue=configBoldValue}if("decorationWidth"in configJson){const configDecorationWidth=configJson.decorationWidth;textComp.activeTextStyle.decorationWidth=configDecorationWidth}if("decorationOffset"in configJson){const configDecorationOffset=configJson.decorationOffset;textComp.activeTextStyle.decorationOffset=configDecorationOffset}if("fallbackFontPathList"in configJson){const configFontPaths=AmazUtils$1.CastJsonArrayToAmazVector(configJson.fallbackFontPathList);textComp.fallbackFontPaths=configFontPaths}}{if("ktvColor"in configJson){const configKTVColor=AmazUtils$1.CastJsonArray4fToAmazVector4f(configJson.ktvColor);if(null!==configKTVColor){this.m_ktvColor=configKTVColor;const configKTVColorVec=AmazUtils$1.CastJsonArrayToAmazVector(configJson.ktvColor);this.m_scriptPassthroughParams.set("ktvColor",configKTVColorVec);this.m_scriptPassthroughParams.set("sdfTextDirty",true);this.m_scriptPassthroughParamsDirty=true}else{console.error(TEMPLATE_TAG,"_updateTextParams config json ktvColor is not vector4f!")}}if("ktvOutlineColor"in configJson){const configKTVOutlineColor=AmazUtils$1.CastJsonArray4fToAmazVector4f(configJson.ktvOutlineColor);if(null!==configKTVOutlineColor){this.m_ktvOutlineColor=configKTVOutlineColor;const configKTVOutlineColorVec=AmazUtils$1.CastJsonArrayToAmazVector(configJson.ktvOutlineColor);this.m_scriptPassthroughParams.set("ktvOutlineColor",configKTVOutlineColorVec);this.m_scriptPassthroughParams.set("sdfTextDirty",true);this.m_scriptPassthroughParamsDirty=true}else{console.error(TEMPLATE_TAG,"_updateTextParams config json ktvOutlineColor is not vector4f!")}}if("ktvShadowColor"in configJson){const configKTVShadowColor=AmazUtils$1.CastJsonArray4fToAmazVector4f(configJson.ktvShadowColor);if(null!==configKTVShadowColor){this.m_ktvShadowColor=configKTVShadowColor;const configKTVShadowColorVec=AmazUtils$1.CastJsonArrayToAmazVector(configJson.ktvShadowColor);this.m_scriptPassthroughParams.set("ktvShadowColor",configKTVShadowColorVec);this.m_scriptPassthroughParams.set("sdfTextDirty",true);this.m_scriptPassthroughParamsDirty=true}else{console.error(TEMPLATE_TAG,"_updateTextParams config json ktvShadowColor is not vector4f!")}}}if("textLocale"in configJson){const configTextLocale=configJson.textLocale;textComp.textLocale=configTextLocale}if("directionLevel"in configJson){const configDirectionLevel=configJson.directionLevel;textComp.textDirectionLevel=configDirectionLevel}{if("bloomPath"in configJson){const configBloomPath=configJson.bloomPath;textComp.updateBloomInfoByPath(configBloomPath)}if("bloomColorCustomized"in configJson){const configBloomColorCustomized=configJson.bloomColorCustomized;textComp.bloomColorCustomized=configBloomColorCustomized}if("bloomColor"in configJson){const configBloomColor=AmazUtils$1.CastJsonArray4fToAmazVector4f(configJson.bloomColor);if(null!==configBloomColor){textComp.bloomColor=configBloomColor}else{console.error(TEMPLATE_TAG,"_updateTextParams config json bloomColor is not vector4f!")}}if("bloomStrength"in configJson){const configBloomStrength=configJson.bloomStrength;textComp.bloomStrength=configBloomStrength}if("bloomRange"in configJson){const configBloomRange=configJson.bloomRange;textComp.bloomRange=configBloomRange}if("bloomDirX"in configJson){const configBloomDirX=configJson.bloomDirX;textComp.bloomDirX=configBloomDirX}if("bloomDirY"in configJson){const configBloomDirY=configJson.bloomDirY;textComp.bloomDirY=configBloomDirY}if("bloomBlurDegree"in configJson){const configBloomBlurDegree=configJson.bloomBlurDegree;textComp.bloomBlurDegree=configBloomBlurDegree}}{if(this.m_enableTextTemplateOpt){if("richText"in configJson){const configRichText=configJson.richText;this._setRichStr(configRichText)}}}{let textShapeConfig="";if("textShape"in configJson){textShapeConfig=configJson.textShape}if(textShapeConfig!==""){this._updateRichTextShapeParams(configJson)}else{const letterPosChanged=textComp.typeSettingDirty;if(!this.m_enableTextTemplateOpt){this.updateTextRect()}if(letterPosChanged){this.reloadAllAnimation()}}}if(this.m_enableTextTemplateOpt){if("lineMaxWidth"in configJson){const configLineMaxWidth=configJson.lineMaxWidth;this._setTextLineMaxWidth(configLineMaxWidth)}}if("fixFontSize"in configJson){const configFixFontSize=configJson.fixFontSize;textComp.fixFontSize=configFixFontSize}}_updateTextParams(configJson){if(!configJson){return}if(null==this.m_textComp){console.error(TEMPLATE_TAG,"_updateTextComp text component is null!");return}this.reloadAllAnimation();this._updateTextJsonParams(configJson)}updateAssociationParam(jsonParam){if("association_params"in jsonParam){const associationParams=jsonParam.association_params;if(associationParams!=null){if(this.m_textAssociationController==null){this.m_textAssociationController=new TextAssociationController(this)}this.m_textAssociationController.setAssociationParams(associationParams)}else{console.error("text widget set parameters json config association_params is not json object!")}}}updateText(){if(this.m_needUpdateTextCom){if(!this.m_jsonParams){console.error(TEMPLATE_TAG,"_updateTextComp config json is empty!");return}else if("rich_text_edit"in this.m_jsonParams){const richTextEditConfig=this.m_jsonParams.rich_text_edit;const textOP={m_iOpCode:richTextEditConfig.op_code,m_fParam1:richTextEditConfig.fparam1,m_fParam2:richTextEditConfig.fparam2,m_fParam3:richTextEditConfig.fparam3,m_fParam4:richTextEditConfig.fparam4,m_sParam:richTextEditConfig.sparam};this.setRichTextByOPCode(textOP);this.m_captionDurationInfoDirty=true;this.m_captionParamsDirty=true;this.m_textCapitalDirty=true}else{let configJson=null;if("text_params"in this.m_jsonParams){configJson=this.m_jsonParams.text_params}this._updateTextParams(configJson)}this.m_needUpdateTextCom=false}}reloadAllAnimation(){var _a,_b,_c,_d,_e;super.reloadAllAnimation();(_a=this.m_captionAnimationController)===null||_a===void 0?void 0:_a.resetAnimation();(_b=this.m_textAnimationController)===null||_b===void 0?void 0:_b.resetAnimation();this.m_animationsDirty=true;const needResetCaption=(_d=(_c=this.m_textAssociationController)===null||_c===void 0?void 0:_c.needUpdateCaption)!==null&&_d!==void 0?_d:false;if(needResetCaption){(_e=this.m_captionAnimationController)===null||_e===void 0?void 0:_e.postUpdate(0)}}onUpdate(timeInTrack){if(!this.m_enable||!this.checkIsInRange(timeInTrack)){super.onUpdate(timeInTrack);if(this.m_rootEntity&&this.m_rootEntity.visible){this.m_rootEntity.visible=false;this.resetAllAnimation()}this.checkLayoutDirty();return}else{if(this.m_rootEntity&&!this.m_rootEntity.visible){this.m_rootEntity.visible=true}const eps=1e-5;if(this.m_timeRangeInTrack.endTime-this.m_timeRangeInTrack.startTime<eps){console.warn("time range is too short, please check!");return}if(this.m_captionAnimationController!=null){const needGenerateOnePage=this.m_captionAnimationController.needMerge||this.m_captionAnimationController.captionInfo.onlyForKeyword;if(this.m_animations==null||this.m_animations.size==0||needGenerateOnePage){this.m_captionAnimationController.needGenerateOnePage=needGenerateOnePage;const timeInWidget=this.getTimeInWidget(timeInTrack);this.m_captionAnimationController.update(timeInWidget)}}if(this.m_textAnimationController!=null){this.m_textAnimationController.updateKeyFrames();const timeInWidget=this.getTimeInWidget(timeInTrack);if(this.m_textAnimationController.isEnable(timeInWidget)){this.m_textAnimationController.update(timeInWidget)}}super.onUpdate(timeInTrack);this.seekAnimations(timeInTrack);this.updateBloomIfNeeded(timeInTrack);this._updateCachePixelSize()}if(this.textAssociationController){this.textAssociationController.notifyStickerAssoLayoutChanged();this.textAssociationController.notifyStickerAssoTimeScopeChanged()}}_isStudioAnim(anim){if(anim.animationScriptType==ScriptType.JAVASCRIPT){return true}return false}seekAnimations(timeInTrack){const isInRange=this.compareFloatRange;let animTimeInWidget=timeInTrack-this.startTimeInTrack;if(animTimeInWidget<0)animTimeInWidget=0;if(!this.m_animations||!this.renderEntity){return}let animingAnimation=null;for(const[anim_type,anim]of this.m_animations){const inAnim=isInRange(anim.animationStartTimeInWidget,anim.animationEndTimeInWidget,animTimeInWidget,true);if(anim&&anim_type&&!anim.loaded&&inAnim){anim.loadAnimation(anim.animationPath,this.renderEntity);anim.setAnimationProperty()}if(this.m_scriptPassthroughParamsDirty===true&&anim){this._updatePassthroughParams(anim)}anim.checkStateStarted(inAnim,this.m_scene);if(!inAnim&&anim.state.entered===true){anim.onLeave();if(this._isStudioAnim(anim)){this._resetForStudioAnimation()}}if(inAnim){if(animingAnimation!==null){if(Math.abs(animingAnimation.animationEndTimeInWidget-timeInTrack)<1e-5){animingAnimation.onLeave();if(this._isStudioAnim(animingAnimation)){this._resetForStudioAnimation()}animingAnimation=anim}}else{animingAnimation=anim}}}if(animingAnimation){if(animingAnimation.state.entered===false){animingAnimation.onEnter();animingAnimation.state.entered=true}if(this._isStudioAnim(animingAnimation)){animingAnimation.seek(animTimeInWidget,this.m_initValues)}else{animingAnimation.seek(animTimeInWidget)}}this.m_scriptPassthroughParamsDirty=false}updateBloomIfNeeded(timeInTrack){if(this.textComp&&this.textComp.bloomPath.length>0&&this.textComp.bloomScriptComp){const timeInWidget=this.getTimeInWidget(timeInTrack);if(!this.textComp.bloomScriptComp.getScript()){const scriptSystem=this.m_scene.getSystem("ScriptSystem");scriptSystem.initAndStartScript(this.textComp.bloomScriptComp);this.textComp.bloomScriptComp.call("onUpdate",[this.textComp,timeInWidget])}else if(this.hasAnimation()||this.m_captionAnimationController!=null){this.textComp.bloomScriptComp.call("onUpdate",[this.textComp,timeInWidget])}this.checkLayoutDirty()}}_resetForStudioAnimation(){if(this.m_initValues&&this.m_textComp){this.m_textComp.letters=this.m_initValues.letters.clone();this.m_textComp.backgrounds=this.m_initValues.backgrounds;this.m_textComp.str=this.m_initValues.textStr;this.m_textComp.forceTypeSetting();this.m_initValues.recordForPerformance(this.m_textComp)}}onLateUpdate(timeInTrack){var _a;if(this.m_animations==null||this.m_animations.size==0){const timeInWidget=this.getTimeInWidget(timeInTrack);(_a=this.m_captionAnimationController)===null||_a===void 0?void 0:_a.postUpdate(timeInWidget)}}updateTextRect(){if(this.m_textComp){this.m_textComp.forceTypeSetting();const rect=this.m_textComp.getCanvasCustomizedExpanded();const pixelSize=new Vec2(rect.width,rect.height);this.updateOriginalSize(pixelSize,this.m_screenSize)}}getCapabilityVersion(){let textVersion=VersionUtils.str2Version(SDK_VERSION);if(null!==this.m_textComp){if(this.m_textComp.textLocale==Amaz$1.TextLocale.RTL_LOCALE){const capVersion=VersionUtils.str2Version(TEXT_LOCALE_VER);textVersion=VersionUtils.versionMax(textVersion,capVersion);if(this.m_textComp.textDirectionLevel!==Amaz$1.TextDirectionLevel.DEFAULT_LTR){const capVersion=VersionUtils.str2Version(TEXT_DIRECTION_LEVEL_VER);textVersion=VersionUtils.versionMax(textVersion,capVersion)}}if(this.m_textComp.bloomPath!==""){const capVersion=VersionUtils.str2Version(TEXT_BLOOM_VER);textVersion=VersionUtils.versionMax(textVersion,capVersion)}if(this.m_textComp.typeSettingParam.canvasWHFixed.x>=0||this.m_textComp.typeSettingParam.canvasWHFixed.y>=0){const capVersion=VersionUtils.str2Version(TEXT_CANVAS_WH_FIXED_VER);textVersion=VersionUtils.versionMax(textVersion,capVersion)}if(this.m_textComp.typeSettingParam.enablePathTypeSetting===true){const capVersion=VersionUtils.str2Version(TEXT_PATH_TYPESETTING_VER);textVersion=VersionUtils.versionMax(textVersion,capVersion)}if(!this.m_textComp.backgrounds.empty()){const capVersion=VersionUtils.str2Version(TEXT_BACK_GROUNDS_VER);textVersion=VersionUtils.versionMax(textVersion,capVersion)}let hasShadowDiffuse=false;const letters=this.m_textComp.letters;for(let letterId=0;letterId<letters.size();letterId++){const style=letters.get(letterId).letterStyle;if(style!=null&&!style.shadows.empty()){for(let shadowId=0;shadowId<style.shadows.size();shadowId++){const shadow=style.shadows.get(shadowId);if(shadow.enable&&shadow.shadowDiffuse>=0){hasShadowDiffuse=true;break}}}}if(hasShadowDiffuse){const capVersion=VersionUtils.str2Version(TEXT_SHADOW_DIFFUSE_VER);textVersion=VersionUtils.versionMax(textVersion,capVersion)}}return textVersion}}class StudioAnimBridge{constructor(richText){this.m_richText=richText}update(timeInWidget){if(this.m_richText.hasAnimation()){if(this.m_richText.textComp!=null&&this.m_richText.initValues==undefined)this.m_richText.handleStudioAnimation();this.m_richText.onUpdate(timeInWidget)}}onTextChangeForScript(){if(this.m_richText.hasAnimation()){this.m_richText.reloadAllAnimation()}}}var Amaz=effect.Amaz;const contentDefault={type:"ScriptTemplate",root:{name:"rootWidget",preview_time:1.5,duration:3}};class TemplatePrivateUtils{static buildGenericScene(path,scriptPath){const scene=new Amaz.Scene;const am=new Amaz.AssetManager;am.rootDir=path;scene.setAssetManager(am);const utils=new Amaz.SwingTemplateUtils;const ecsOptEnabled=utils.getABConfigValue(AmazUtils$1.AMGABTestName.EFFECTAB_ENABLE_ECS_SYSTEM_OPTIMIZE,false);if(ecsOptEnabled){const systemList=["EventSystem","TransformSystem","BehaviorSystem","CameraSystem","TweenSystem","ScriptSystem","JSScriptSystem","AnimSeqSystem","GifAnimSeqSystem","VideoAnimSeqSystem","BuiltinObjectSystem","Sprite2DRendererSystem","MeshRendererSystem","SDFTextSystem","TextSystem","TextLayerRendererSystem","RenderSystem","UserEventHandleSystem","IFRootSystem","PostProcessSystem"];const systemV=new Amaz.StringVector;for(let i=0;i<systemList.length;i++){systemV.pushBack(systemList[i])}scene.setSystemList(systemV)}else{scene.addSystem("IFRootSystem");scene.addSystem("PostProcessSystem")}scene.visible=true;scene.name="template_scene";if(scriptPath!=""){scene.jsScriptSystems.pushBack(scriptPath+"/main.js")}else{scene.jsScriptSystems.pushBack("js/main.js")}TemplatePrivateUtils.addCameraEntityToNativeScene(scene);return scene}static addCameraEntityToNativeScene(scene){const ent=AmazUtils$1.createEntity("InfoSticker_camera_entity",scene);const trans=ent.transform;const ca=ent.camera;trans.localPosition=new Amaz.Vector3f(0,0,10);trans.localEulerAngle=new Amaz.Vector3f(0,0,0);trans.localScale=new Amaz.Vector3f(1,1,1);ca.type=Amaz.CameraType.ORTHO;ca.clearType=Amaz.CameraClearType.DONT;ca.fovy=60;ca.zNear=.1;ca.zFar=1e3;ca.orthoScale=1;ca.renderOrder=1;const layerObj=new Amaz.DynamicBitset(1,1);ca.layerVisibleMask=layerObj;const rt=new Amaz.SceneOutputRT;ca.renderTexture=rt}}class Template{constructor(){this.scenes=[];this.m_templateTimeRangeInTrack=new TimeRange(0,0);this.m_screenSize=new Amaz.Vector2f(0,0);this.m_resolutionType=0;this.m_mainJSRef=null;this.m_animationController=null;this.m_studioAnimBridge=null}init(pathPrefix,screenWidth,screenHeight,dependResource="",scriptPath=""){this.scenes.push(TemplatePrivateUtils.buildGenericScene(pathPrefix,scriptPath));const contentJsonPath=pathPrefix+"/content.json";let contentJson=AmazFileUtils.readFileContent(contentJsonPath);if(contentJson===undefined){const configJson=JSON.parse(dependResource);if(configJson!=null&&"content_json"in configJson){contentJson=configJson.content_json}else{contentJson=JSON.stringify(contentDefault)}}const screenSize=new Amaz.Vector2f(screenWidth,screenHeight);if(contentJson!=undefined){this.scenes.map((scene=>{scene.config.set("params",contentJson);if(dependResource!=null){scene.config.set("depends",dependResource)}scene.config.set("screenSize",screenSize);scene.config.set("resolutionType",this.m_resolutionType)}))}this.m_screenSize.x=screenWidth;this.m_screenSize.y=screenHeight}createAnimationController(textComp,entity,startTimeInTrack,endTimeInTrack){this.m_animationController=new CaptionAnimationController(textComp);this.m_animationController.enabled=true;this.m_animationController.richText=new RichText("RichTextForCaptionAnimationController",WidgetType.TEXT,this.scenes[0]);this.m_animationController.richText.m_renderEntity=entity;this.m_animationController.richText.textComp=textComp;this.m_animationController.richText.setTimeRange(startTimeInTrack,endTimeInTrack);this.m_animationController.richText.captionAnimationController=this.m_animationController}createStudioAnimBridge(textComp,entity,startTimeInTrack,endTimeInTrack){const richText=new RichText("RichTextForStudioAnimBridge",WidgetType.TEXT,this.scenes[0]);richText.renderEntity=entity;richText.textComp=textComp;richText.setTimeRange(startTimeInTrack,endTimeInTrack);this.m_studioAnimBridge=new StudioAnimBridge(richText);this.m_studioAnimBridge.richText=richText}updateStudioAnimBridge(timeInWidget){if(this.m_studioAnimBridge!=null){this.m_studioAnimBridge.update(timeInWidget)}}setParametersForScript(parameters){const jsonParam=JSON.parse(parameters);if("children"in jsonParam){const children=jsonParam.children;if(children instanceof Array){for(let i=0;i<children.length;i++){const child=children[i];if(child&&"name"in child){if("text_params"in child){const text_params=child.text_params;if(text_params){if("caption_duration_info"in text_params){this.setCaptionDurationInfo(text_params.caption_duration_info)}if("capital"in text_params){this.setTextCapital(text_params.capital)}}}if("keyframe_params"in child){this.setKeyFrameParams(child.keyframe_params)}if("caption_params"in child){this.setCaptionParams(child.caption_params)}this.setRichTextParameters(child);if("anims"in child){this.setAnimations(child.anims)}}}}}}getParametersForScript(parameters){const jsonParam=JSON.parse(parameters);if("children"in jsonParam){const children=jsonParam.children;if(children instanceof Array){for(let i=0;i<children.length;i++){const child=children[i];if(child&&"name"in child){if("queries"in child){if(this.m_animationController!=null)child.queries=this.m_animationController.query(child.queries);else child.queries={}}}}}}return JSON.stringify(jsonParam)}splitLinePage(parameters){const jsonParam=JSON.parse(parameters);if("caption_params"in jsonParam&&"caption_duration_info"in jsonParam){const ret=CaptionInfo.splitLinePage(jsonParam.caption_params,jsonParam.caption_duration_info);return JSON.stringify(ret)}return"{}"}resetForScript(){const empty=JSON.parse("{}");this.setCaptionDurationInfo(empty);this.setKeyFrameParams(empty);this.resetCaptionParams();if(this.m_animationController!=null)this.m_animationController.enabled=false}setTimeRangeForScript(startTimeInTrack,endTimeInTrack){if(this.m_animationController!=null&&this.m_animationController.richText!=null){this.m_animationController.richText.setTimeRange(startTimeInTrack,endTimeInTrack);this.m_animationController.richText.setTimeRange(startTimeInTrack,endTimeInTrack)}if(this.m_studioAnimBridge!=null&&this.m_studioAnimBridge.richText!=null){this.m_studioAnimBridge.richText.setTimeRange(startTimeInTrack,endTimeInTrack)}}setCaptionDurationInfo(captionDurationInfo){if(this.m_animationController!=null){this.m_animationController.setCaptionDurationInfo(captionDurationInfo)}}setTextCapital(capital){if(this.m_animationController!=null){this.m_animationController.setTextCapital(capital)}}setKeyFrameParams(keyframeParams){if(this.m_animationController!=null){this.m_animationController.setKeyFrameParams(keyframeParams)}}setCaptionParams(captionParams){if(this.m_animationController!=null){this.m_animationController.setCaptionParams(captionParams)}}setAnimations(animParams){const richText=this.m_studioAnimBridge.richText;richText.setAnimationParameters(animParams);richText.checkStudioAnimationAndRegister();richText.handleStudioAnimation()}resetCaptionParams(){this.m_animationController.resetCaptionParams()}updateAnimationController(timeInWidget){if(this.m_animationController!=null){this.m_animationController.update(timeInWidget)}}postUpdateForScript(timeInWidget){if(this.m_animationController!=null){this.m_animationController.postUpdate(timeInWidget)}}reloadAllAnimationForScript(){if(this.m_animationController!=null&&this.m_animationController.richText!=null){this.m_animationController.richText.reloadAllAnimation()}if(this.m_studioAnimBridge!=null&&this.m_studioAnimBridge.richText!=null){const richText=this.m_studioAnimBridge.richText;richText.clearInitValueForBridge();richText.reloadAllAnimation()}}onTextChangeForScript(){if(this.m_animationController!=null){this.m_animationController.onTextChangeForScript()}if(this.m_studioAnimBridge!=null){this.m_studioAnimBridge.onTextChangeForScript()}}getAnimationControllerEnabled(){if(this.m_animationController!=null)return this.m_animationController.enabled;return false}setAnimsParameters(animation_arr){if(this.m_animationController!=null){this.m_animationController.setAnimsParameters(animation_arr)}}setRichTextParameters(paras){if(this.m_animationController!=null){this.m_animationController.setRichTextParameters(paras)}}clearCache(){if(null===this.m_mainJSRef){this.getMainJSRef()}if(this.m_mainJSRef&&"clearCache"in this.m_mainJSRef){this.m_mainJSRef.clearCache()}else{console.error(TEMPLATE_TAG,"jsMian is null or has no clearCache property!")}}destroy(){var _a,_b,_c;this.m_mainJSRef=null;this.scenes=[];if(this.m_animationController){if(this.m_animationController.richText){(_a=this.m_animationController)===null||_a===void 0?void 0:_a.richText.resetAllAnimation();this.m_animationController.richText=null}this.m_animationController=null}if(this.m_studioAnimBridge){if(this.m_studioAnimBridge.richText){(_b=this.m_studioAnimBridge)===null||_b===void 0?void 0:_b.richText.clearInitValueForBridge();(_c=this.m_studioAnimBridge)===null||_c===void 0?void 0:_c.richText.resetAllAnimation();this.m_studioAnimBridge.richText=null}this.m_studioAnimBridge=null}}setParameters(parameters){this.scenes.map((scene=>{const event=new Amaz.Event;event.type=TemplateEventType.layerOperation;event.args.pushBack("setParameters");event.args.pushBack(parameters);scene.sendEvent(event)}))}getMainJSRef(){let currentScene=null;this.scenes.map((scene=>{if(scene&&scene.name==="template_scene"){currentScene=scene}}));if(currentScene){const jsSystem=currentScene.getSystem("JSScriptSystem");const jsScript=jsSystem.getSystemScriptByName("main");if(jsScript){this.m_mainJSRef=jsScript.ref}else{console.error(TEMPLATE_TAG,"JSSystem has no main script!")}}else{console.error(TEMPLATE_TAG,"currentScene is null in getParameters")}}getParameters(parameters){if(null===this.m_mainJSRef){this.getMainJSRef()}if(this.m_mainJSRef&&"getParameters"in this.m_mainJSRef){return this.m_mainJSRef.getParameters(parameters)}else{console.error(TEMPLATE_TAG,"jsMian is null or has no getParameters property!")}return""}setResolutionType(resolutionType){if(resolutionType!==this.m_resolutionType){this.m_resolutionType=resolutionType;this.scenes.map((scene=>{const event=new Amaz.Event;event.type=TemplateEventType.layerOperation;event.args.pushBack("setResolutionType");event.args.pushBack(resolutionType);event.args.pushBack(this.m_screenSize.x);event.args.pushBack(this.m_screenSize.y);scene.sendEvent(event)}))}}onResize(screen_width,screen_heght){if(this.m_screenSize.x!==screen_width||this.m_screenSize.y!==screen_heght){this.m_screenSize.x=screen_width;this.m_screenSize.y=screen_heght;this.scenes.map((scene=>{const event=new Amaz.Event;event.type=TemplateEventType.layerOperation;event.args.pushBack("setScreenSize");event.args.pushBack(screen_width);event.args.pushBack(screen_heght);scene.sendEvent(event)}))}}setTimeRange(startTimeInTrack,endTimeInTrack){if(this.m_templateTimeRangeInTrack.startTime!==startTimeInTrack||this.m_templateTimeRangeInTrack.endTime!==endTimeInTrack){const event=new Amaz.Event;event.type=TemplateEventType.layerOperation;event.args.pushBack("setTimeRange");event.args.pushBack(startTimeInTrack);event.args.pushBack(endTimeInTrack);this.m_templateTimeRangeInTrack.startTime=startTimeInTrack;this.m_templateTimeRangeInTrack.endTime=endTimeInTrack;this.scenes.map((scene=>{scene.sendEvent(event)}))}}getRuntimeScenes(){return this.scenes}createLayer(layerName,layerParam){const event=new Amaz.Event;event.type=TemplateEventType.layerOperation;event.args.pushBack("createLayer");event.args.pushBack(layerName);event.args.pushBack(layerParam);event.args.pushBack(this.m_screenSize.x);event.args.pushBack(this.m_screenSize.y);this.scenes.map((scene=>{scene.sendEvent(event)}))}removeLayer(layerName){const event=new Amaz.Event;event.type=TemplateEventType.layerOperation;event.args.pushBack("removeLayer");event.args.pushBack(layerName);this.scenes.map((scene=>{scene.sendEvent(event)}))}updateSeekParamsToJS(seekParams){const event=new Amaz.Event;event.type=TemplateEventType.layerOperation;event.args.pushBack("updateSeekParamsToJS");event.args.pushBack(seekParams);this.scenes.map((scene=>{scene.sendEvent(event)}))}}const template=Template;exports.Template=Template;exports.template=template;
//# sourceMappingURL=template.cjs.js.map
